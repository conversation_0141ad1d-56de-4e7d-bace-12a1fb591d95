# Adding Components to Storybook - Step by Step Guide

## Overview
This guide provides simple steps to add any component to Storybook for previewing all props with interactive controls.

## Prerequisites
- Storybook is already configured in the project
- Component exists in `src/components/`
- Basic understanding of TypeScript/React

## Step-by-Step Process

### 1. Create Your Component
First, ensure your component is properly typed with TypeScript interfaces.

```typescript
// src/components/common/Button/Button.tsx
import React from 'react';

export interface ButtonProps {
  label: string;
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  onClick?: () => void;
}

export const Button: React.FC<ButtonProps> = ({
  label,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  onClick
}) => {
  return (
    <button
      className={`btn btn-${variant} btn-${size}`}
      disabled={disabled}
      onClick={onClick}
    >
      {label}
    </button>
  );
};
```

### 2. Create the Story File
Create a `.stories.tsx` file in the `src/stories/` directory.

```typescript
// src/stories/Button.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { Button, ButtonProps } from '../components/common/Button/Button';

// Meta configuration
const meta: Meta<typeof Button> = {
  title: 'Common/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: ['primary', 'secondary', 'danger'],
    },
    size: {
      control: { type: 'select' },
      options: ['small', 'medium', 'large'],
    },
    disabled: {
      control: { type: 'boolean' },
    },
    onClick: { action: 'clicked' },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default story
export const Default: Story = {
  args: {
    label: 'Button',
    variant: 'primary',
    size: 'medium',
    disabled: false,
  },
};

// Variant stories
export const Primary: Story = {
  args: {
    label: 'Primary Button',
    variant: 'primary',
  },
};

export const Secondary: Story = {
  args: {
    label: 'Secondary Button',
    variant: 'secondary',
  },
};

export const Danger: Story = {
  args: {
    label: 'Danger Button',
    variant: 'danger',
  },
};

// Size stories
export const Small: Story = {
  args: {
    label: 'Small Button',
    size: 'small',
  },
};

export const Large: Story = {
  args: {
    label: 'Large Button',
    size: 'large',
  },
};

// State stories
export const Disabled: Story = {
  args: {
    label: 'Disabled Button',
    disabled: true,
  },
};
```

### 3. Key Story Configuration Elements

#### Meta Object
- `title`: Defines the story hierarchy in Storybook sidebar
- `component`: The React component to render
- `parameters`: Layout and other display options
- `tags`: Include 'autodocs' for automatic documentation
- `argTypes`: Control types for interactive props

#### ArgTypes Controls
```typescript
argTypes: {
  // Select dropdown
  variant: {
    control: { type: 'select' },
    options: ['option1', 'option2'],
  },
  // Boolean toggle
  disabled: {
    control: { type: 'boolean' },
  },
  // Text input
  label: {
    control: { type: 'text' },
  },
  // Number input
  count: {
    control: { type: 'number' },
  },
  // Color picker
  color: {
    control: { type: 'color' },
  },
  // Action logger
  onClick: { action: 'clicked' },
}
```

### 4. Story Variants
Create multiple stories to showcase different prop combinations:

```typescript
// Showcase all variants
export const AllVariants: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1rem' }}>
      <Button label="Primary" variant="primary" />
      <Button label="Secondary" variant="secondary" />
      <Button label="Danger" variant="danger" />
    </div>
  ),
};

// Showcase all sizes
export const AllSizes: Story = {
  render: () => (
    <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
      <Button label="Small" size="small" />
      <Button label="Medium" size="medium" />
      <Button label="Large" size="large" />
    </div>
  ),
};
```

### 5. Advanced Features

#### Custom Decorators
```typescript
const meta: Meta<typeof Button> = {
  // ... other config
  decorators: [
    (Story) => (
      <div style={{ margin: '3em', padding: '1em', border: '1px solid #ccc' }}>
        <Story />
      </div>
    ),
  ],
};
```

#### Play Functions (Interactive Testing)
```typescript
import { userEvent, within } from '@storybook/testing-library';

export const Interactive: Story = {
  args: {
    label: 'Click me!',
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const button = canvas.getByRole('button');
    await userEvent.click(button);
  },
};
```

### 6. Running Storybook

```bash
# Start Storybook development server
npm run storybook

# Build Storybook for production
npm run build-storybook
```

### 7. Best Practices

1. **Naming Convention**: Use PascalCase for story names
2. **Organization**: Group related components under the same title hierarchy
3. **Documentation**: Use JSDoc comments in your component for better auto-docs
4. **Controls**: Provide meaningful control types for all interactive props
5. **Default Values**: Set sensible defaults in your Default story
6. **Edge Cases**: Create stories for edge cases (empty states, error states, etc.)

### 8. Example File Structure
```
src/
├── components/
│   └── common/
│       └── Button/
│           ├── Button.tsx
│           ├── Button.module.css
│           └── index.ts
└── stories/
    └── Button.stories.tsx
```

### 9. Troubleshooting

- **Component not showing**: Check import paths and component export
- **Controls not working**: Verify argTypes configuration
- **Styling issues**: Ensure CSS imports are included
- **TypeScript errors**: Check component prop types match story args

## Quick Template

For quick setup, copy this template and replace `ComponentName` with your component:

```typescript
import type { Meta, StoryObj } from '@storybook/react';
import { ComponentName } from '../components/path/to/ComponentName';

const meta: Meta<typeof ComponentName> = {
  title: 'Category/ComponentName',
  component: ComponentName,
  parameters: { layout: 'centered' },
  tags: ['autodocs'],
  argTypes: {
    // Add your prop controls here
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    // Add default props here
  },
};
```

This template provides a solid foundation for any component in Storybook with full prop preview capabilities.