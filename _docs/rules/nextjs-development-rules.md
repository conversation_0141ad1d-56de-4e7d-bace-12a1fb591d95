# Next.js Development Rules & Best Practices

This document outlines the development rules and best practices for Next.js projects based on our current architecture and established patterns.

---

## **1. Project Structure & Architecture**

### **Core Architecture Principles**

- **Layered Architecture**: Follow the established layered structure with clear separation of concerns
- **Feature-Based Organization**: Group related functionality together while maintaining reusability
- **Dependency Direction**: Dependencies should flow inward (UI → Business Logic → Infrastructure)

### **Directory Structure**

```
src/
├── app/                    # Next.js App Router pages and layouts
├── components/             # Reusable UI components
│   ├── common/            # Generic, reusable components
│   ├── features/          # Feature-specific components
│   └── pages/             # Page-level components
├── infrastructure/         # External concerns (APIs, storage, logging)
├── shared/                # Shared utilities, configs, hooks
│   ├── config/           # Application configuration
│   ├── hooks/            # Custom React hooks
│   ├── types/            # Shared TypeScript types
│   └── utils/            # Utility functions
└── stores/                # Global state management (Zustand)
```

---

## **2. File Naming Conventions**

### **General Files**
- **Format**: `kebab-case.<type>.ts`
- **Examples**:
  - `user.store.ts`
  - `auth.service.ts`
  - `api-client.config.ts`
  - `user-profile.interface.ts`
  - `trip-status.enum.ts`

### **React Components**
- **Format**: `PascalCase.tsx`
- **Examples**:
  - `UserProfile.tsx`
  - `TripCard.tsx`
  - `NavigationMenu.tsx`

### **Component Folders**
- **Format**: `kebab-case`
- **Examples**:
  - `user-profile/`
  - `trip-card/`
  - `navigation-menu/`

### **File Type Suffixes**
- `.interface.ts` - TypeScript interfaces
- `.type.ts` - TypeScript type definitions
- `.types.ts` - Multiple related types
- `.enum.ts` - Enumerations
- `.config.ts` - Configuration files
- `.service.ts` - Service classes
- `.store.ts` - State management stores
- `.hook.ts` - Custom React hooks
- `.utils.ts` - Utility functions
- `.constants.ts` - Application constants
- `.test.ts` / `.spec.ts` - Test files

---

## **3. Code Organization Rules**

### **Import Organization**

Follow this import order (enforced by ESLint):

```typescript
// 1. Node modules
import React from 'react';
import { NextPage } from 'next';

// 2. Internal modules (absolute imports)
import { Button } from '@/components/common';
import { useAppStore } from '@/stores/app.store';
import { appConfig } from '@/shared/config/app.config';

// 3. Relative imports
import './styles.css';
```

### **Export Patterns**

**Preferred**: Named exports for better tree-shaking
```typescript
// ✅ Good
export const UserService = {
  getUser: () => {},
  updateUser: () => {},
};

export interface User {
  id: string;
  name: string;
}
```

**Components**: Default exports are acceptable
```typescript
// ✅ Acceptable for components
function UserProfile() {
  return <div>Profile</div>;
}

export default UserProfile;
```

### **Index Files**

Use index files to create clean public APIs:

```typescript
// src/components/common/index.ts
export { Button } from './Button';
export { Input } from './Input';
export { Card } from './Card';
```

---

## **4. TypeScript Best Practices**

### **Interface Definitions**

```typescript
// user.interface.ts
export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'user' | 'guest';
}

export interface UserCreateRequest {
  name: string;
  email: string;
  role?: 'user' | 'guest'; // Optional with default
}
```

### **Type Safety Rules**

- **Always use strict TypeScript**: Enable `strict: true` in tsconfig.json
- **Avoid `any`**: Use specific types or `unknown` when type is truly unknown
- **Use type assertions sparingly**: Prefer type guards and proper typing
- **Generic constraints**: Use generic constraints for better type safety

```typescript
// ✅ Good
function processData<T extends { id: string }>(data: T): T {
  return { ...data, processed: true };
}

// ❌ Avoid
function processData(data: any): any {
  return data;
}
```

---

## **5. React Component Guidelines**

### **Component Structure**

```typescript
import React from 'react';
import { cn } from '@/shared/utils';

// 1. Interface definition
interface ComponentProps {
  className?: string;
  children?: React.ReactNode;
  // Other props
}

// 2. Component implementation
function Component({ className, children, ...props }: ComponentProps) {
  // 3. Hooks (in order: state, effects, custom hooks)
  const [state, setState] = useState();
  
  useEffect(() => {
    // Effect logic
  }, []);
  
  const customHook = useCustomHook();
  
  // 4. Event handlers
  const handleClick = () => {
    // Handler logic
  };
  
  // 5. Render
  return (
    <div className={cn('base-styles', className)} {...props}>
      {children}
    </div>
  );
}

export default Component;
```

### **Component Categories**

**Common Components** (`/components/common/`):
- Pure, reusable UI components
- No business logic
- Accept `className` prop for styling flexibility
- Use composition patterns

**Feature Components** (`/components/features/`):
- Feature-specific components
- Can contain business logic
- Moderate reusability within feature domain

**Page Components** (`/components/pages/`):
- Page-level components
- Orchestrate feature components
- Handle page-specific logic

---

## **6. State Management (Zustand)**

### **Store Structure**

```typescript
// app.store.ts
import { create } from 'zustand';

interface AppStore {
  // State
  user: User | null;
  isLoading: boolean;
  
  // Actions
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  
  // Computed/Selectors (if needed)
  reset: () => void;
}

export const useAppStore = create<AppStore>((set) => ({
  // Initial state
  user: null,
  isLoading: false,
  
  // Actions
  setUser: (user) => set({ user }),
  setLoading: (isLoading) => set({ isLoading }),
  
  // Reset
  reset: () => set({ user: null, isLoading: false }),
}));

// Selector hooks for better performance
export const useUser = () => useAppStore((state) => state.user);
export const useIsLoading = () => useAppStore((state) => state.isLoading);
```

### **Store Guidelines**

- **Single responsibility**: Each store should handle one domain
- **Immutable updates**: Always return new objects/arrays
- **Selector hooks**: Create specific selector hooks for performance
- **Actions**: Keep actions simple and focused

---

## **7. Configuration Management**

### **App Configuration Pattern**

Use the established singleton pattern for configuration:

```typescript
// app.config.ts
class AppConfigManager {
  private static instance: AppConfigManager;
  private config: AppConfig;
  
  public static getInstance(): AppConfigManager {
    if (!AppConfigManager.instance) {
      AppConfigManager.instance = new AppConfigManager();
    }
    return AppConfigManager.instance;
  }
  
  public get<T extends ConfigSection>(section: T): ConfigValue<T> {
    return this.config[section];
  }
}

export const appConfig = AppConfigManager.getInstance();
```

### **Environment Variables**

- Use `NEXT_PUBLIC_` prefix for client-side variables
- Validate environment variables at startup
- Provide sensible defaults
- Document all required environment variables

---

## **8. API & Data Fetching**

### **API Client Structure**

```typescript
// api-client.service.ts
import axios from 'axios';
import { appConfig } from '@/shared/config/app.config';

const apiClient = axios.create({
  baseURL: appConfig.get('api').baseUrl,
  timeout: appConfig.get('api').timeout,
});

// Request interceptor
apiClient.interceptors.request.use((config) => {
  // Add auth headers, logging, etc.
  return config;
});

// Response interceptor
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle errors globally
    return Promise.reject(error);
  }
);

export { apiClient };
```

### **Data Fetching Patterns**

**Use React Query/TanStack Query** for server state:

```typescript
// user.service.ts
import { useQuery, useMutation } from '@tanstack/react-query';
import { apiClient } from '@/infrastructure/api';

export const useUser = (id: string) => {
  return useQuery({
    queryKey: ['user', id],
    queryFn: () => apiClient.get(`/users/${id}`).then(res => res.data),
  });
};

export const useUpdateUser = () => {
  return useMutation({
    mutationFn: (user: User) => apiClient.put(`/users/${user.id}`, user),
    onSuccess: () => {
      // Invalidate queries, show success message, etc.
    },
  });
};
```

---

## **9. Styling Guidelines**

### **Tailwind CSS Best Practices**

- **Use utility classes**: Prefer Tailwind utilities over custom CSS
- **Component variants**: Use `class-variance-authority` for component variants
- **Responsive design**: Mobile-first approach with responsive utilities
- **Custom utilities**: Create custom utilities in `tailwind.config.js` when needed

```typescript
// Button component with variants
import { cva } from 'class-variance-authority';
import { cn } from '@/shared/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md font-medium transition-colors',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 px-3',
        lg: 'h-11 px-8',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

interface ButtonProps {
  variant?: 'default' | 'secondary';
  size?: 'default' | 'sm' | 'lg';
  className?: string;
}

function Button({ variant, size, className, ...props }: ButtonProps) {
  return (
    <button
      className={cn(buttonVariants({ variant, size }), className)}
      {...props}
    />
  );
}
```

---

## **10. Testing Strategy**

### **Testing Structure**

```
src/
├── components/
│   └── Button/
│       ├── Button.tsx
│       └── Button.test.tsx
├── services/
│   └── user.service.test.ts
└── utils/
    └── helpers.test.ts
```

### **Testing Guidelines**

- **Co-locate tests**: Keep test files next to the code they test
- **Test behavior, not implementation**: Focus on what the code does, not how
- **Use Testing Library**: Follow Testing Library principles for React components
- **Mock external dependencies**: Mock API calls, external services, etc.

---

## **11. Performance Best Practices**

### **Next.js Specific**

- **Use App Router**: Leverage the new App Router for better performance
- **Server Components**: Use Server Components by default, Client Components when needed
- **Image Optimization**: Always use `next/image` for images
- **Font Optimization**: Use `next/font` for font loading
- **Bundle Analysis**: Regularly analyze bundle size with `@next/bundle-analyzer`

### **React Performance**

- **Memoization**: Use `useMemo`, `useCallback`, and `React.memo` judiciously
- **Code Splitting**: Implement route-based and component-based code splitting
- **Lazy Loading**: Use `React.lazy` and `Suspense` for non-critical components

```typescript
// Lazy loading example
const LazyComponent = React.lazy(() => import('./LazyComponent'));

function App() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LazyComponent />
    </Suspense>
  );
}
```

---

## **12. Error Handling**

### **Error Boundaries**

```typescript
// error-boundary.tsx
import React from 'react';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>,
  ErrorBoundaryState
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
    // Log to external service
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-fallback">
          <h2>Something went wrong.</h2>
          <button onClick={() => this.setState({ hasError: false })}>
            Try again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

### **API Error Handling**

```typescript
// error-handler.service.ts
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export const handleApiError = (error: unknown): ApiError => {
  if (error instanceof ApiError) {
    return error;
  }
  
  if (axios.isAxiosError(error)) {
    return new ApiError(
      error.response?.data?.message || error.message,
      error.response?.status || 500,
      error.response?.data?.code
    );
  }
  
  return new ApiError('An unexpected error occurred', 500);
};
```

---

## **13. Security Best Practices**

### **Environment Variables**
- Never commit secrets to version control
- Use `.env.local` for local development secrets
- Validate and sanitize all environment variables

### **API Security**
- Implement proper authentication and authorization
- Validate all inputs on both client and server
- Use HTTPS in production
- Implement rate limiting

### **Client-Side Security**
- Sanitize user inputs to prevent XSS
- Use Content Security Policy (CSP)
- Implement proper CORS policies

---

## **14. Development Workflow**

### **Git Workflow**

- **Conventional Commits**: Use conventional commit messages (enforced by commitlint)
- **Branch Naming**: Use descriptive branch names (`feature/user-authentication`, `fix/api-timeout`)
- **Pull Requests**: Require code review for all changes
- **Pre-commit Hooks**: Use Husky for pre-commit linting and formatting

### **Code Quality**

- **ESLint**: Follow the established ESLint configuration
- **Prettier**: Use Prettier for consistent code formatting
- **TypeScript**: Maintain strict TypeScript configuration
- **Lint-staged**: Run linting and formatting on staged files only

### **Development Commands**

```bash
# Development
npm run dev          # Start development server with Turbopack
npm run build        # Build for production
npm run start        # Start production server

# Code Quality
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
npm run format       # Format code with Prettier

# Testing
npm run test         # Run tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Run tests with coverage
```

---

## **15. Documentation Standards**

### **Code Documentation**

- **JSDoc comments**: Use JSDoc for functions and classes
- **Interface documentation**: Document complex interfaces
- **README files**: Maintain README files for major modules

```typescript
/**
 * Calculates the total price including tax
 * @param price - The base price
 * @param taxRate - The tax rate as a decimal (e.g., 0.1 for 10%)
 * @returns The total price including tax
 */
function calculateTotal(price: number, taxRate: number): number {
  return price * (1 + taxRate);
}
```

### **Architecture Documentation**

- **Decision Records**: Document architectural decisions
- **API Documentation**: Maintain API documentation
- **Component Documentation**: Document component props and usage

---

## **16. Monitoring & Logging**

### **Logging Strategy**

Use the established logging infrastructure:

```typescript
import { logger } from '@/infrastructure/logging';

// Different log levels
logger.info('User logged in', { userId: user.id });
logger.warn('API response slow', { duration: 2000 });
logger.error('Failed to save user', { error: error.message });

// Performance logging
logger.performance('API call', { endpoint: '/users', duration: 150 });
```

### **Error Monitoring**

- **Client-side errors**: Capture and report client-side errors
- **API errors**: Log and monitor API errors
- **Performance monitoring**: Track Core Web Vitals and performance metrics

---

## **17. Deployment & Production**

### **Build Optimization**

- **Bundle analysis**: Regularly analyze bundle size
- **Tree shaking**: Ensure proper tree shaking
- **Code splitting**: Implement proper code splitting strategies
- **Asset optimization**: Optimize images, fonts, and other assets

### **Production Checklist**

- [ ] Environment variables configured
- [ ] Error monitoring setup
- [ ] Performance monitoring enabled
- [ ] Security headers configured
- [ ] HTTPS enabled
- [ ] CDN configured for static assets
- [ ] Database migrations applied
- [ ] Health checks implemented

---

## **Conclusion**

These rules and best practices are designed to maintain code quality, consistency, and scalability in our Next.js projects. They should be followed by all team members and regularly reviewed and updated as the project evolves.

For questions or suggestions regarding these rules, please create an issue or start a discussion with the development team.