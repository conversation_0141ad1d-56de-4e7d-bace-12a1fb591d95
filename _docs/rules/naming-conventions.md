# Naming Conventions

This document defines the naming conventions for files, folders, variables, functions, classes, and other entities in the project.

---

## **1. File Naming**

### **General Files**

- Target: Store, Enum, Service, Util, SignalR, Stories, Interfaces, Types, Tests, Mappers, etc...
- Format: `kebab-case.<type>.ts` or `kebab-case.<type>.[js|ts|tsx]`
- Example:
  - `trip-location.enum.ts`
  - `auth.service.ts`
  - `user.store.ts`
  - `user.test.ts`

### **Component Files**

- Target: Components
- Format: `PascalCase.ts` PascalCase.<type>.[tsx|jsx|css|scss]
- Example:
  - `TripCard.ts`
  - `UserProfile.ts`
  - `UserProfile.css`
  - `UserProfile.scss`

### **Svg Icons**

- Format: `camelCase.svg` or `camelCase-{num}.svg`
- Path: `/src/assets/imgs/svg`
- Example:
  - `tripLocation.svg`
  - `userProfile.svg`
  - `userProfile-1.svg`
  - `userProfile-2.svg`

---

## **2. Folder Naming**

### **Component Folders**

- Format: `kebab-case`
- Example:
  - `trip-card`
  - `user-profile`

### **Non-Component Folders**

- Format: `kebab-case`
- Example:
  - `services`
  - `utils`
  - `stores`

---

## **3. Code Naming**

### **Interfaces**

- File name: `<name>.interface.ts`
- Declaration: `PascalCase`
- Example:
  ```ts
  // trip-details.interface.ts
  export interface TripDetails {
    id: string;
    name: string;
  }
  ```

### **Class / Interface / Type Fields**

- File name: `<name>.<type>.ts`
- Declaration: `camelCase` e.g `id`, `contactNumber`
- Example:
  ```ts
  // trip-details.interface.ts
  export interface TripDetails {
    id: string; // field camelCase
    name: string; // field camelCase
  }
  ```

---

### **Types**

- File name: `<name>.types.ts`
- Declaration: `PascalCase`
- Example:
  ```ts
  // trip-status.types.ts
  export type TripStatus = "pending" | "confirmed";
  ```

---

### **Functions**

- Naming: `camelCase`
- Example:

  ```ts
  function getTrips() {}
  function fetchUserProfile() {}
  function fetchUserProfile(personAge: string) {}
  function fetchUserProfile({ personAge: string }) {}
  ```

### **Private Functions / Fields**

- Naming: `_camelCase`
- Example:

  ```ts
  private _handleSubmit() {}      // private function
  private _tripList: Trip[] = []; // private property
  private _callback = () => {}    // private arrow function
  ```

  ```ts
  export class TripStatus {
    private _id: number;
  }
  ```

  ```ts
  export class ParentClass {
    class _ChildPrivateClass {
      ....
    }
  }

  ```

### **Enums**

- File name: `<name>.enum.ts`
- Declaration: `PascalCase`
- Enum elements: `UPPER_SNAKE_CASE`
- Example:
  ```ts
  // trip-status.enum.ts
  export enum TripStatus {
    PENDING = "PENDING",
    CONFIRMED = "CONFIRMED",
    NOT_ACK = "not ack",
    JUST_A_NUMBER = 1,
  }
  ```

---

### **Constants**

- Naming: `UPPER_SNAKE_CASE`
- Example:
  ```ts
  export const API_URL = "https://api.example.com";
  export const MAX_TRIPS = 10;
  ```

---

### **Classes**

- Naming: `PascalCase`
- Example:

  ```ts
  class StoreCartStore {
    private readonly _personName: string = 'John'; // private readonly field
    public readonly ageNumber: number = 15;        // public readonly field
    public cityAddress: string = 'alex, eg';      // public field
    
    public handleSubmit() { ... }                 // public method
    private _validateInput() { ... }              // private method
  }
  ```

---

### **Component Name**

- Naming: `PascalCase`
- Example:

  ```ts
  export default function MyComponent() {...}

  export default function MyComponent({ propName: string }) {...}
  ```

---

### **Hooks**

- Naming: `camelCase` prefixed with `use` keyword
- Example:

  ```ts
  export function useState() {...}

  export function useLocalize({ propName: string }) {...}
  ```

---

### **Zod Schemas**

- Naming: `PascalCase` postfixed with `Schema` keyword
- Example:
  ```ts
  export const TripSchema = x.object({
    id: z.number(),
  });
  ```

---

## **4. File Type Suffixes**

Use the following suffixes for specific types of files:

| **Type**          | **Suffix**                | **Example**                     |
| ----------------- | ------------------------- | ------------------------------- |
| Store             | `.store.ts`               | `cart.store.ts`                 |
| Stories           | `.stories.tsx`            | `cart.stories.tsx`              |
| Service           | `.service.ts`             | `auth.service.ts`               |
| Hub / SignalR     | `.hub.ts` / `.signalr.ts` | `chat.signalr.ts`               |
| Hook              | `.hook.ts`                | `useTrips.hook.ts`              |
| Context           | `.context.ts`             | `user.context.ts`               |
| Interfaces         | `.interface.ts`           | `user.interface.ts`           |
| Types             | `.types.ts`               | `trip.types.ts`                 |
| Model            | `.model.ts`              | `trip.model.ts`                |
| Entity            | `.entity.ts`              | `trip.entity.ts`                |
| Enum              | `.enum.ts`                | `trip-status.enum.ts`           |
| Constants         | `.constants.ts`           | `auth.constants.ts`             |
| Schema            | `.schema.ts`              | `trip-location.schema.ts`       |
| Validators        | `.validators.ts`          | `trip.validators.ts`            |
| Mappers           | `.mappers.ts`             | `trip.mappers.ts`               |
| Utils             | `.utils.ts`               | `date.utils.ts`                 |
| Guard            | `.guard.ts`              | `auth.guard.ts`                   |
| Interceptor      | `.interceptor.ts`              | `auth.interceptor.ts`       |
| Config            | `.config.ts`              | `app.config.ts`                 |
| Environment Types | `.env.d.ts`               | `env.d.ts`                      |
| Routes            | `.routes.ts`              | `app.routes.ts`                 |
| Mock Data         | `.mock.ts`                | `trip-location.mock.ts`         |
| Fixture           | `.fixture.ts`             | `trip-location.fixture.ts`      |
| Tests             | `.test.ts` / `.spec.ts`   | `trip.test.ts` / `trip.spec.ts` |

---

## **Summary**

| Entity                 | Convention         | Example                |
| ---------------------- | ------------------ | ---------------------- |
| **File Name**          | `kebab-case.<type>.ts`  | `auth.service.ts`      |
| **Component File**     | `PascalCase.ts`    | `UserProfile.ts`       |
| **Folder (Component)** | `kebab-case`       | `trip-card`            |
| **Folder (Other)**     | `kebab-case`       | `services`             |
| **Interface**          | `PascalCase`       | `TripDetails`          |
| **Type**               | `PascalCase`       | `TripStatus`           |
| **Function**           | `camelCase`        | `getTrips`             |
| **Private Function**   | `_camelCase`       | `_handleSubmit`        |
| **Private Field**      | `_camelCase`       | `_tripList`            |
| **Enum**               | `PascalCase`       | `TripStatus`           |
| **Enum Elements**      | `UPPER_SNAKE_CASE` | `PENDING`, `CONFIRMED` |
| **Class**              | `PascalCase`       | `StoreCartStore`       |
| **Constant**           | `UPPER_SNAKE_CASE` | `API_URL`, `MAX_TRIPS` |
| **Component Name**     | `PascalCase`       | `MyComponent`          |
| **Hook**               | `use` + `camelCase`| `useState`, `useLocalize` |
| **Zod Schema**         | `PascalCase` + `Schema` | `TripSchema`      |
| **SVG Icon**           | `camelCase.svg`    | `tripLocation.svg`     |
| **SVG Icon (numbered)**| `camelCase-{num}.svg` | `userProfile-1.svg` |

---

## **Technical Debates**

### **File Naming Conventions: Singular vs. Plural**

#### **Question 1: Should I use `name.types.ts` or `name.type.ts`?**

This is a valid question that depends on your perspective and approach:

**Approach 1: Role-based naming (Singular)**
- Use singular suffixes to describe the file's role
- Example: `trip-status.type.ts` (contains one type definition)

**Approach 2: Content-based naming (Mixed)**
- Use singular for single entities, plural for multiple entities
- Example: `trip-status.type.ts` vs. `trip.types.ts` (contains multiple related types)

**Our Convention:**
We follow a **singular-first approach** for most file types:
- **Singular suffixes** (most file types): `.store.ts`, `.service.ts`, `.hook.ts`, `.context.ts`, `.interface.ts`, `.type.ts`, `.model.ts`, `.enum.ts`, `.schema.ts`, `.guard.ts`, `.interceptor.ts`, `.config.ts`, `.route.ts`, `.mock.ts`, `.fixture.ts`, `.test.ts`, `.spec.ts`, `.hub.ts` / `.signalr.ts` (SignalR)
- **Plural suffixes** (when containing multiple entities): `.types.ts`, `.stories.tsx`, `.validators.ts`, `.mappers.ts`, `.utils.ts`, `.constants.ts`, `.routes.ts`
- **Special cases**: `.env.d.ts` (environment types)

**Examples from our codebase:**
- `trip-details.interface.ts` (single interface)
- `trip.types.ts` (multiple types)
- `trip-status.enum.ts` (single enum)
- `auth.service.ts` (single service)

#### **Question 2: Why are test files always singular (`.test.ts`) even with multiple test cases?**

**Answer:**
Test file suffixes (`.test.ts` / `.spec.ts`) describe the file's **purpose**, not its **content quantity**.

**Explanation:**
- A file named `trip.test.ts` tells build tools (Vitest, Jest, etc.) that "this file contains test code"
- Whether you have 1 test or 100 tests inside, the suffix remains singular
- The file's role is testing, not defining a single "test entity"

**Framework Consistency:**
This is why testing frameworks (Vitest, Jest, Cypress) consistently use singular suffixes - they're describing the file's purpose, not counting the number of tests within it. The `.test` suffix is standardized across testing frameworks for better tooling integration and test runners.

#### **Question 3: Why are interface/schema/model/entity files always singular even with multiple definitions?**

**Answer:**
Interface, schema, model, and entity files use singular suffixes because they should represent **one major type** per file, with other definitions serving as supplementary types for the main type's fields.

**Explanation:**
- Each file should focus on **one primary entity/type** as the main subject
- Additional types within the file should be **related and supplementary** to the main type
- Files should **not contain unrelated types** that belong to different domains
- This approach follows the **Single Responsibility Principle** for file organization

**Benefits:**
- **Easier search and navigation** - developers can quickly find the main type they're looking for
- **Better code organization** - related types are grouped together logically
- **Improved maintainability** - changes to one major type don't affect unrelated types
- **Clearer file purpose** - each file has a single, well-defined responsibility

**Example:**
```ts
// trip-details.interface.ts - Main type: TripDetails
export interface TripDetails {
  id: string;
  location: TripLocation;     // Supplementary type
  passengers: Passenger[];     // Supplementary type
  status: TripStatus;         // Supplementary type
}

// Supplementary types (related to TripDetails)
interface TripLocation {
  city: string;
  country: string;
}

interface Passenger {
  name: string;
  age: number;
}

type TripStatus = "pending" | "confirmed" | "cancelled";
```

**Trade-off:**
While this approach results in more files, it significantly improves code discoverability and maintainability, making it easier for developers to locate and work with specific types.

