# Fetchy Library AI Agent Context for React/Next.js

## AI Agent Prompt

You are an expert React/Next.js developer specializing in API integration using the Fetchy library. When implementing API functionality, you MUST follow these patterns: create TypeScript interfaces for all data structures, set up centralized API configuration with interceptors, organize API calls in service layers, integrate with React Query for data fetching and caching, handle loading/error states in components, and implement proper error handling. Always use the service layer pattern instead of direct API calls in components, and ensure all API responses are properly typed with TypeScript interfaces.

## Installation & Setup

**Check if installed:**

```bash
grep -i "fetchy" package.json
```

**Install if needed:**

```bash
npm install @your-org/fetchy @tanstack/react-query
```

## Core Implementation Rules

### 1. TypeScript Interfaces (REQUIRED)

Always define interfaces for API data:

```typescript
interface User { id: number; name: string; email: string; }
interface ApiResponse<T> { data: T; message: string; success: boolean; }
```

### 2. Base API Configuration

Create centralized API setup in `services/api.ts`:

```typescript
import { Fetchy } from '@your-org/fetchy';

export const api = new Fetchy({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8088/api',
  timeout: 10000,
  headers: { 'Content-Type': 'application/json' },
  interceptors: {
    request: [(config) => {
      const token = localStorage.getItem('authToken');
      if (token) config.headers.Authorization = `Bearer ${token}`;
      return config;
    }],
    response: [(response) => response, (error) => Promise.reject(error)]
  }
});
```

### 3. Service Layer Pattern

Organize API calls in service files:

```typescript
// services/userService.ts
export const userService = {
  getUsers: () => api.get<ApiResponse<User[]>>('/users'),
  getUser: (id: number) => api.get<ApiResponse<User>>(`/users/${id}`),
  createUser: (data: CreateUserRequest) => api.post<ApiResponse<User>>('/users', data),
  updateUser: (id: number, data: Partial<User>) => api.put<ApiResponse<User>>(`/users/${id}`, data),
  deleteUser: (id: number) => api.delete<ApiResponse<void>>(`/users/${id}`)
};
```

### 4. React Query Integration

Create custom hooks for data fetching:

```typescript
// hooks/useUsers.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';

export const useUsers = () => useQuery({
  queryKey: ['users'],
  queryFn: userService.getUsers,
  staleTime: 5 * 60 * 1000
});

export const useCreateUser = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: userService.createUser,
    onSuccess: () => queryClient.invalidateQueries({ queryKey: ['users'] })
  });
};
```

### 5. Component Integration

Use hooks in components with proper error handling:

```typescript
export const UserList: React.FC = () => {
  const { data: users, isLoading, error } = useUsers();
  const createUser = useCreateUser();

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      {users?.data.map(user => <div key={user.id}>{user.name}</div>)}
    </div>
  );
};
```

### 6. File Upload

Handle file uploads with progress tracking:

```typescript
export const fileService = {
  uploadFile: (file: File, onProgress?: (progress: number) => void) => {
    const formData = new FormData();
    formData.append('file', file);
    return api.upload('/upload', formData, {
      onUploadProgress: (event) => {
        if (onProgress && event.total) {
          onProgress(Math.round((event.loaded * 100) / event.total));
        }
      }
    });
  }
};
```

### 7. Error Handling

Implement global error handling:

```typescript
import { FetchyError } from '@your-org/fetchy';

export const handleApiError = (error: unknown) => {
  if (error instanceof FetchyError) {
    switch (error.status) {
      case 401: localStorage.removeItem('authToken'); window.location.href = '/login'; break;
      case 403: console.error('Access denied'); break;
      case 404: console.error('Resource not found'); break;
      case 500: console.error('Server error'); break;
      default: console.error('API Error:', error.message);
    }
  }
};
```

### 8. Next.js API Routes

Structure API endpoints properly:

```typescript
// app/api/users/route.ts
export async function GET() {
  try {
    const users = await getUsersFromDatabase();
    return Response.json({ data: users, success: true });
  } catch (error) {
    return Response.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
```

## Environment Variables

```bash
# .env.local
NEXT_PUBLIC_API_URL=http://localhost:8088/api
NEXT_PUBLIC_API_TIMEOUT=10000
```

## Testing Pattern

```typescript
// Mock API for testing
jest.mock('../services/api');
const mockedApi = api as jest.Mocked<typeof api>;

test('should fetch users', async () => {
  const mockUsers = [{ id: 1, name: 'John', email: '<EMAIL>' }];
  mockedApi.get.mockResolvedValue({ data: mockUsers, success: true });
  
  const result = await userService.getUsers();
  expect(result.data).toEqual(mockUsers);
});
```

## Agent Instructions

**ALWAYS:**

- Use TypeScript interfaces for all API data
- Create service layer functions for API calls
- Integrate with React Query for data fetching
- Handle loading and error states in components
- Use environment variables for configuration
- Implement proper error handling

**NEVER:**

- Make API calls directly in components
- Hardcode API URLs or configuration
- Skip error handling
- Mix different HTTP client libraries
- Ignore loading states in UI

**File Structure:**

```
src/infrastructure
├── api/
│   ├── api.ts          # Base Fetchy configuration
│   └── userService.ts  # API service functions

```

This context enables AI agents to implement robust, maintainable API integrations using Fetchy in React/Next.js applications.
