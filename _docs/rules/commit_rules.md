# Conventional Commits 1.0.0-beta.4

## Summary

The Conventional Commits specification is a lightweight convention on top of commit messages. It provides an easy set of rules for creating an explicit commit history; which makes it easier to write automated tools on top of. This convention dovetails with SemVer, by describing the features, fixes, and breaking changes made in commit messages.

## Commit Message Structure

The commit message should be structured as follows:

```
<type>[optional scope]: <description>

[optional body]

[optional footer]
```

## Commit Types

The commit contains the following structural elements, to communicate intent to the consumers of your library:

### Core Types

- **`fix:`** - A commit of the type fix patches a bug in your codebase (this correlates with PATCH in semantic versioning)
- **`feat:`** - A commit of the type feat introduces a new feature to the codebase (this correlates with MINOR in semantic versioning)
- **`BREAKING CHANGE:`** - A commit that has the text BREAKING CHANGE: at the beginning of its optional body or footer section introduces a breaking API change (correlating with MAJOR in semantic versioning). A BREAKING CHANGE can be part of commits of any type

### Additional Types

Commit types other than `fix:` and `feat:` are allowed, for example @commitlint/config-conventional (based on the Angular convention) recommends:

- **`chore:`** - Changes to the build process or auxiliary tools
- **`docs:`** - Documentation only changes
- **`style:`** - Changes that do not affect the meaning of the code (white-space, formatting, missing semi-colons, etc)
- **`refactor:`** - A code change that neither fixes a bug nor adds a feature
- **`perf:`** - A code change that improves performance
- **`test:`** - Adding missing tests or correcting existing tests
- **`improvement:`** - Commits that improve a current implementation without adding a new feature or fixing a bug

> **Note:** These types are not mandated by the conventional commits specification, and have no implicit effect in semantic versioning (unless they include a BREAKING CHANGE).

## Scope

A scope may be provided to a commit's type, to provide additional contextual information and is contained within parenthesis, e.g., `feat(parser): add ability to parse arrays`.

## Examples

### Commit message with description and breaking change in body

```
feat: allow provided config object to extend other configs

BREAKING CHANGE: `extends` key in config file is now used for extending other config files
```

### Commit message with optional ! to draw attention to breaking change

```
chore!: drop Node 6 from testing matrix

BREAKING CHANGE: dropping Node 6 which hits end of life in April
```

### Commit message with no body

```
docs: correct spelling of CHANGELOG
```

### Commit message with scope

```
feat(lang): add polish language
```

### Commit message for a fix using an (optional) issue number

```
fix: correct minor typos in code

see the issue for details on the typos fixed

closes issue #12
```

## Specification

The key words "MUST", "MUST NOT", "REQUIRED", "SHALL", "SHALL NOT", "SHOULD", "SHOULD NOT", "RECOMMENDED", "MAY", and "OPTIONAL" in this document are to be interpreted as described in RFC 2119.

### Rules

1. **Commit Prefix**: Commits MUST be prefixed with a type, which consists of a noun, feat, fix, etc., followed by an OPTIONAL scope, and a REQUIRED terminal colon and space.

2. **Feature Commits**: The type `feat` MUST be used when a commit adds a new feature to your application or library.

3. **Bug Fix Commits**: The type `fix` MUST be used when a commit represents a bug fix for your application.

4. **Scope Usage**: A scope MAY be provided after a type. A scope MUST consist of a noun describing a section of the codebase surrounded by parenthesis, e.g., `fix(parser):`

5. **Description**: A description MUST immediately follow the space after the type/scope prefix. The description is a short summary of the code changes, e.g., `fix: array parsing issue when multiple spaces were contained in string`.

6. **Commit Body**: A longer commit body MAY be provided after the short description, providing additional contextual information about the code changes. The body MUST begin one blank line after the description.

7. **Footer**: A footer of one or more lines MAY be provided one blank line after the body. The footer MUST contain meta-information about the commit, e.g., related pull-requests, reviewers, breaking changes, with one piece of meta-information per-line.

8. **Breaking Changes**: Breaking changes MUST be indicated at the very beginning of the body section, or at the beginning of a line in the footer section. A breaking change MUST consist of the uppercase text BREAKING CHANGE, followed by a colon and a space.

9. **Breaking Change Description**: A description MUST be provided after the `BREAKING CHANGE:`, describing what has changed about the API, e.g., `BREAKING CHANGE: environment variables now take precedence over config files`.

10. **Additional Types**: Types other than `feat` and `fix` MAY be used in your commit messages.

11. **Case Sensitivity**: The units of information that make up conventional commits MUST NOT be treated as case sensitive by implementors, with the exception of BREAKING CHANGE which MUST be uppercase.

12. **Breaking Change Attention**: A `!` MAY be appended prior to the `:` in the type/scope prefix, to further draw attention to breaking changes. `BREAKING CHANGE:` description MUST also be included in the body or footer, along with the `!` in the prefix.