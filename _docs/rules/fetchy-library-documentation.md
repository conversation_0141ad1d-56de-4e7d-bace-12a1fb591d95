# Fetchy Library Documentation

## Overview

Fetchy is a custom HTTP client library built on top of Axios, designed to provide a consistent, type-safe, and feature-rich API for making HTTP requests in React applications. It integrates seamlessly with React Query for state management and provides built-in error handling, logging, and interceptor support.

## Architecture

### Core Components

- **Fetchy Class**: Main HTTP client implementation
- **IFetchy Interface**: Type-safe contract for HTTP operations
- **React Query Integration**: Built-in hooks for queries and mutations
- **Interceptor System**: Request/response middleware support
- **Type System**: Comprehensive TypeScript definitions

## Dependencies

### External Libraries

- **axios**: `^1.x.x` - Core HTTP client functionality
- **@tanstack/react-query**: `^5.x.x` - State management and caching
- **@/infrastructure/logging**: Internal logging system
- **@/shared/config/app-settings.config**: Application configuration

### Internal Dependencies

- Custom logging infrastructure (`ILogger`)
- Application configuration system
- Validation library integration (`Valy`)

## Installation & Setup

### Basic Setup

```typescript
import { fetchy } from '@/shared/lib/Fetchy';

// The library is pre-configured with your app's base URL
// No additional setup required for basic usage
```

### Advanced Configuration

```typescript
import { Fetchy } from '@/shared/lib/Fetchy';

const customFetchy = new Fetchy({
    baseURL: 'https://api.example.com',
    timeout: 30000,
    retries: 3,
    retryDelay: 1000,
    headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'ar'
    },
    Logger: customLogger
});
```

## API Reference

### HTTP Methods

#### GET Request
```typescript
// Basic GET request
const response = await fetchy.get<UserData>('/users/123');

// GET with query parameters
const response = await fetchy.get<UserList>('/users', {
    params: {
        page: 1,
        limit: 10,
        status: 'active'
    }
});
```

#### POST Request
```typescript
// POST with JSON body
const response = await fetchy.post<CreateUserResponse>('/users', {
    body: {
        name: 'John Doe',
        email: '<EMAIL>'
    }
});

// POST with custom headers
const response = await fetchy.post<ApiResponse>('/data', {
    body: payload,
    headers: {
        'X-Custom-Header': 'value'
    }
});
```

#### PUT Request
```typescript
const response = await fetchy.put<UpdateResponse>('/users/123', {
    body: {
        name: 'Updated Name',
        status: 'active'
    }
});
```

#### DELETE Request
```typescript
const response = await fetchy.delete<DeleteResponse>('/users/123');
```

#### PATCH Request
```typescript
const response = await fetchy.patch<PatchResponse>('/users/123', {
    body: {
        status: 'inactive'
    }
});
```

### File Upload

```typescript
// Single file upload
const response = await fetchy.upload<UploadResponse>('/upload', {
    files: selectedFile,
    fields: {
        category: 'documents',
        description: 'Important file'
    },
    onUploadProgress: (progress) => {
        console.log(`Upload progress: ${progress}%`);
    }
});

// Multiple files upload
const response = await fetchy.upload<UploadResponse>('/upload-multiple', {
    files: [file1, file2, file3],
    fields: {
        batch: 'batch-001'
    }
});
```

### React Query Integration

#### Query Hook
```typescript
import { useFetchyQuery } from '@/shared/lib/Fetchy';

function UserProfile({ userId }: { userId: string }) {
    const {
        data,
        isLoading,
        error,
        refetch
    } = useFetchyQuery(
        ['user', userId],
        () => fetchy.get<User>(`/users/${userId}`),
        {
            enabled: !!userId,
            staleTime: 5 * 60 * 1000, // 5 minutes
            retry: 3
        }
    );

    if (isLoading) return <div>Loading...</div>;
    if (error) return <div>Error: {error.message}</div>;

    return <div>Welcome, {data?.data.name}!</div>;
}
```

#### Mutation Hook
```typescript
import { useFetchyMutation } from '@/shared/lib/Fetchy';

function CreateUserForm() {
    const createUserMutation = useFetchyMutation(
        (userData: CreateUserRequest) => 
            fetchy.post<User>('/users', { body: userData }),
        {
            onSuccess: (data) => {
                console.log('User created:', data.data);
                // Invalidate and refetch users list
                queryClient.invalidateQueries(['users']);
            },
            onError: (error) => {
                console.error('Failed to create user:', error);
            }
        }
    );

    const handleSubmit = (formData: CreateUserRequest) => {
        createUserMutation.mutate(formData);
    };

    return (
        <form onSubmit={handleSubmit}>
            {/* Form fields */}
            <button 
                type="submit" 
                disabled={createUserMutation.isPending}
            >
                {createUserMutation.isPending ? 'Creating...' : 'Create User'}
            </button>
        </form>
    );
}
```

### Interceptors

#### Request Interceptor
```typescript
// Authentication interceptor
fetchy.addRequestInterceptor((config) => {
    const authToken = localStorage.getItem('auth_token');
    
    if (authToken) {
        config.headers.Authorization = `Bearer ${authToken}`;
    }
    
    return config;
});

// Locale interceptor
fetchy.addRequestInterceptor((config) => {
    const locale = getCurrentLocale();
    config.headers['Accept-Language'] = locale;
    return config;
});
```

#### Response Interceptor
```typescript
fetchy.addResponseInterceptor((response) => {
    // Log successful responses
    console.log('API Response:', response.status, response.data);
    return response;
});
```

#### Error Interceptor
```typescript
fetchy.addErrorInterceptor((error) => {
    if (error.response?.status === 401) {
        // Handle unauthorized access
        redirectToLogin();
    }
    
    // Log error for monitoring
    logger.error('API Error:', error);
});
```

## Usage Patterns

### Service Layer Pattern

```typescript
// ports.service.ts
import { fetchy } from '@/shared/lib/Fetchy';
import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';

export class PortService {
    private static instance: PortService;

    public static getInstance(): PortService {
        if (!PortService.instance) {
            PortService.instance = new PortService();
        }
        return PortService.instance;
    }

    public async getPorts(params: GetPortsQueryParams): Promise<GetPortsApiResponse> {
        logger.info('[PortService] fetching ports with query params:', params);

        try {
            if (appConfig.get('mockApiData')) {
                return this.fakeFetchPorts(params);
            }
            return this.realFetchPorts(params);
        } catch (error: unknown) {
            logger.error('[PortService] Error fetching ports:', error as Error);
            throw error;
        }
    }

    private async realFetchPorts(params: Partial<GetPortsQueryParams> = {}): Promise<GetPortsApiResponse> {
        const response = await fetchy.get<GetPortsApiResponse>('ports', { params });
        
        // Validate response using Valy
        const validationResult = valy.validate(GetPortsApiResponseSchema, response.data, 'ports');
        if (!validationResult.success) {
            logger.error('[PortService] Invalid response from ports API', validationResult.error);
            throw new Error('Invalid API response format');
        }

        return response.data;
    }

    private fakeFetchPorts(params: Partial<GetPortsQueryParams> = {}): Promise<GetPortsApiResponse> {
        logger.info('[PortService] Using mock data for ports');
        return Promise.resolve(MockPortsData);
    }
}

export const portService = PortService.getInstance();
```

### Component Integration Pattern

```typescript
// PortsList.tsx
import { useFetchyQuery } from '@/shared/lib/Fetchy';
import { portService } from '@/infrastructure/api/ports/port.service';

interface PortsListProps {
    filters: PortFilters;
}

export function PortsList({ filters }: PortsListProps) {
    const {
        data: portsData,
        isLoading,
        error,
        refetch
    } = useFetchyQuery(
        ['ports', filters],
        () => portService.getPorts(filters),
        {
            enabled: true,
            staleTime: 2 * 60 * 1000, // 2 minutes
            retry: (failureCount, error) => {
                // Custom retry logic
                return failureCount < 3 && error.response?.status !== 404;
            }
        }
    );

    if (isLoading) return <LoadingSpinner />;
    if (error) return <ErrorMessage error={error} onRetry={refetch} />;

    return (
        <div className="ports-list">
            {portsData?.data.map(port => (
                <PortCard key={port.id} port={port} />
            ))}
        </div>
    );
}
```

### Error Handling Pattern

```typescript
// Enhanced error handling with custom error types
import { ApiError, FetchyResponse } from '@/shared/lib/Fetchy';

class ApiService {
    async handleApiCall<T>(
        apiCall: () => Promise<FetchyResponse<T>>
    ): Promise<T> {
        try {
            const response = await apiCall();
            return response.data;
        } catch (error) {
            if (this.isApiError(error)) {
                // Handle known API errors
                switch (error.code) {
                    case 'VALIDATION_ERROR':
                        throw new ValidationError(error.message, error.details);
                    case 'NOT_FOUND':
                        throw new NotFoundError(error.message);
                    case 'UNAUTHORIZED':
                        this.handleUnauthorized();
                        throw error;
                    default:
                        throw new ApiError(error.message, error.code);
                }
            }
            
            // Handle network or unexpected errors
            logger.error('Unexpected API error:', error);
            throw new NetworkError('An unexpected error occurred');
        }
    }

    private isApiError(error: unknown): error is ApiError {
        return typeof error === 'object' && 
               error !== null && 
               'code' in error && 
               'message' in error;
    }
}
```

## Configuration Options

### FetchyConfig Interface

```typescript
interface FetchyConfig {
    baseURL: string;           // API base URL
    timeout: number;           // Request timeout in milliseconds
    retries: number;           // Number of retry attempts
    retryDelay: number;        // Delay between retries in milliseconds
    headers?: Record<string, string>; // Default headers
    Logger?: ILogger | null;   // Custom logger instance
}
```

### Default Configuration

```typescript
const DEFAULT_CONFIG: FetchyConfig = {
    baseURL: '',
    timeout: 30000,           // 30 seconds
    retries: 3,
    retryDelay: 1000,         // 1 second
    Logger: null,
    headers: {
        'Content-Type': 'application/json',
        'Accept-Language': 'ar'
    }
};
```

## Best Practices

### 1. Service Layer Architecture
- Create dedicated service classes for each API domain
- Use singleton pattern for service instances
- Implement both real and mock data methods for development

### 2. Type Safety
- Always define TypeScript interfaces for request/response types
- Use generic types with Fetchy methods
- Validate API responses using schema validation

### 3. Error Handling
- Implement comprehensive error handling in service methods
- Use custom error types for different error scenarios
- Log errors appropriately for debugging and monitoring

### 4. React Query Integration
- Use query keys that include relevant parameters
- Set appropriate stale times based on data freshness requirements
- Implement proper loading and error states in components

### 5. Interceptors
- Keep interceptors focused on single responsibilities
- Handle authentication, localization, and logging separately
- Ensure interceptors don't interfere with each other

### 6. Performance Optimization
- Use React Query's caching capabilities effectively
- Implement proper retry strategies
- Consider request deduplication for identical requests

## Testing

### Unit Testing Services

```typescript
// port.service.test.ts
import { portService } from './port.service';
import { fetchy } from '@/shared/lib/Fetchy';

jest.mock('@/shared/lib/Fetchy');
const mockFetchy = fetchy as jest.Mocked<typeof fetchy>;

describe('PortService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should fetch ports successfully', async () => {
        const mockResponse = {
            data: { ports: [{ id: 1, name: 'Port A' }] },
            status: 200,
            statusText: 'OK',
            headers: {}
        };
        
        mockFetchy.get.mockResolvedValue(mockResponse);

        const result = await portService.getPorts({ page: 1 });
        
        expect(mockFetchy.get).toHaveBeenCalledWith('ports', { 
            params: { page: 1 } 
        });
        expect(result).toEqual(mockResponse.data);
    });
});
```

### Integration Testing with React Query

```typescript
// PortsList.test.tsx
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PortsList } from './PortsList';

const createTestQueryClient = () => new QueryClient({
    defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false }
    }
});

describe('PortsList', () => {
    it('should display ports when data loads successfully', async () => {
        const queryClient = createTestQueryClient();
        
        render(
            <QueryClientProvider client={queryClient}>
                <PortsList filters={{}} />
            </QueryClientProvider>
        );

        await waitFor(() => {
            expect(screen.getByText('Port A')).toBeInTheDocument();
        });
    });
});
```

## Migration Guide

### From Axios to Fetchy

```typescript
// Before (Direct Axios)
import axios from 'axios';

const response = await axios.get('/api/users');
const users = response.data;

// After (Fetchy)
import { fetchy } from '@/shared/lib/Fetchy';

const response = await fetchy.get<UsersResponse>('/users');
const users = response.data;
```

### From Fetch API to Fetchy

```typescript
// Before (Fetch API)
const response = await fetch('/api/users', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify(userData)
});
const result = await response.json();

// After (Fetchy)
const response = await fetchy.post<UserResponse>('/users', {
    body: userData
});
const result = response.data;
```

## Troubleshooting

### Common Issues

1. **Request Timeout**: Increase timeout in configuration
2. **CORS Errors**: Configure proper headers and base URL
3. **Authentication Issues**: Verify interceptor implementation
4. **Type Errors**: Ensure proper TypeScript interfaces

### Debug Mode

```typescript
// Enable detailed logging
const debugFetchy = new Fetchy({
    ...config,
    Logger: console // Use console for debugging
});
```

## Advanced Features

### Custom Query Client Configuration

```typescript
import { QueryClientWrapper } from '@/shared/lib/Fetchy/utils/queryClient';

const queryClientWrapper = new QueryClientWrapper(config, logger);

// Advanced query operations
await queryClientWrapper.prefetchQuery(['users'], fetchUsers);
queryClientWrapper.invalidateQueries(['users']);
queryClientWrapper.removeQueries(['users']);
```

### Request/Response Transformation

```typescript
// Transform requests before sending
fetchy.addRequestInterceptor((config) => {
    // Transform request data
    if (config.body && typeof config.body === 'object') {
        config.body = transformRequestData(config.body);
    }
    return config;
});

// Transform responses after receiving
fetchy.addResponseInterceptor((response) => {
    // Transform response data
    response.data = transformResponseData(response.data);
    return response;
});
```

This documentation provides a comprehensive guide for developers working with the Fetchy library, covering everything from basic usage to advanced patterns and best practices.