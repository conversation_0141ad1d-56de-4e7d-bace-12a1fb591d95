# AI Agent Guidelines for Zustand Implementation

## 🤖 Overview

This document provides specific guidelines when implementing, modifying, or creating Zustand stores in the our application. These rules ensure consistency, maintainability, and adherence to established patterns.

## 🎯 Core Implementation Rules

### Rule 1: Always Use Factory Pattern

**MANDATORY**: Every new store MUST follow the factory pattern.

```typescript
// ✅ CORRECT: Factory pattern implementation
const _featureStore = (instanceId: string): StateCreator<FeatureStoreType> => {
    return (set, get): FeatureStoreType => ({
        // Store implementation
    });
};

// ❌ INCORRECT: Direct store creation
const useFeatureStore = create<FeatureStoreType>()((set, get) => ({
    // Store implementation
}));
```

### Rule 2: Consistent File Structure

**MANDATORY**: Follow this exact file structure for all stores:

```typescript
// 1. Imports (grouped and ordered)
import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { persist } from 'zustand/middleware';

// 2. Types and interfaces
interface FeatureStoreType {
    // State properties
    // Action methods
}

// 3. Constants
const DEFAULT_STATE = {
    // Default values
};

// 4. Private factory function (underscore prefix)
const _featureStore = (instanceId: string): StateCreator<FeatureStoreType> => {
    // Implementation
};

// 5. Exports (global instance + factory)
export const useFeatureStore = create<FeatureStoreType>()(
    // Middleware chain
);

export const createFeatureStore = (instanceId: string) => create<FeatureStoreType>()(
    // Middleware chain
);
```

### Rule 3: Middleware Order

**MANDATORY**: Always use this middleware order:

```typescript
// Correct order: subscribeWithSelector → devtools → persist → factory
export const useFeatureStore = create<FeatureStoreType>()(
    subscribeWithSelector(
        devtools(
            persist(
                _featureStore('global'),
                { name: LocalStorageKeys.FEATURE }
            ),
            { name: 'feature-store-global' }
        )
    ),
);
```

### Rule 4: Instance ID Integration

**MANDATORY**: Include instance ID in all logging and DevTools naming:

```typescript
const _featureStore = (instanceId: string): StateCreator<FeatureStoreType> => {
    return (set, get): FeatureStoreType => ({
        actionName: async (params) => {
            try {
                // Action implementation
                logger.info(`${instanceId}: actionName completed`, params);
            } catch (error) {
                logger.error(`${instanceId}: actionName error`, error);
            }
        },
    });
};
```

### Rule 5: TypeScript Compliance

**MANDATORY**: All stores must be fully typed:

```typescript
// ✅ CORRECT: Proper typing
interface FeatureStoreType {
    // State
    items: Item[];
    isLoading: boolean;
    error: string | null;
  
    // Actions
    loadItems: () => Promise<void>;
    addItem: (item: Omit<Item, 'id'>) => void;
    removeItem: (id: string) => void;
    reset: () => void;
}

// ❌ INCORRECT: Using 'any' or missing types
interface BadStoreType {
    data: any; // Never use 'any'
    someAction: (...args: any[]) => any; // Never use 'any'
}
```

## 🔧 Implementation Patterns

### Pattern 1: API Integration Store

**Use this template for stores that interact with APIs:**

```typescript
const _apiStore = (instanceId: string): StateCreator<ApiStoreType> => {
    return (set, get): ApiStoreType => ({
        ...DEFAULT_STATE,

        loadData: async (params = {}) => {
            set((state) => ({ ...state, isLoading: true, error: null }));
            try {
                const response = await apiService.getData(params);
                set((state) => ({
                    ...state,
                    data: response.data,
                    pagination: response.pagination,
                    isLoading: false,
                }));
                logger.info(`${instanceId}: loadData completed`, response);
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                set((state) => ({
                    ...state,
                    error: errorMessage,
                    isLoading: false,
                }));
                logger.error(`${instanceId}: loadData error`, error);
            }
        },

        reset: () => {
            set(() => ({ ...DEFAULT_STATE }));
            logger.info(`${instanceId}: reset completed`);
        },
    });
};
```

### Pattern 2: UI State Store

**Use this template for UI-only state management:**

```typescript
const _uiStore = (instanceId: string): StateCreator<UIStoreType> => {
    return (set, get): UIStoreType => ({
        ...DEFAULT_STATE,

        setActiveTab: (tab: string | null) => {
            set((state) => ({ ...state, activeTab: tab }));
            logger.info(`${instanceId}: setActiveTab -> ${tab}`);
        },

        toggleSidebar: () => {
            set((state) => ({ ...state, sidebarOpen: !state.sidebarOpen }));
            const newState = get().sidebarOpen;
            logger.info(`${instanceId}: toggleSidebar -> ${newState}`);
        },

        reset: () => {
            set(() => ({ ...DEFAULT_STATE }));
            logger.info(`${instanceId}: reset completed`);
        },
    });
};
```

### Pattern 3: Real-time Store (SignalR)

**Use this template for real-time data with SignalR:**

```typescript
const _realtimeStore = (instanceId: string): StateCreator<RealtimeStoreType> => {
    return (set, get): RealtimeStoreType => {
        // Private helper methods
        const _handleDataReceived = (data: DataType) => {
            set((state) => ({
                ...state,
                items: [...state.items, data].slice(-100), // Keep last 100
            }));
            logger.info(`${instanceId}: data received`, data);
        };

        return {
            ...DEFAULT_STATE,

            startListening: () => {
                const subscriptionId = signalRHub.subscribe('DataReceived', _handleDataReceived);
                set((state) => ({ ...state, isListening: true, subscriptionId }));
                logger.info(`${instanceId}: startListening`, { subscriptionId });
            },

            stopListening: () => {
                const { subscriptionId } = get();
                if (subscriptionId) {
                    signalRHub.unsubscribe('DataReceived');
                    set((state) => ({ ...state, isListening: false, subscriptionId: null }));
                    logger.info(`${instanceId}: stopListening`, { subscriptionId });
                }
            },

            reset: () => {
                const { isListening } = get();
                if (isListening) {
                    get().stopListening();
                }
                set(() => ({ ...DEFAULT_STATE }));
                logger.info(`${instanceId}: reset completed`);
            },
        };
    };
};
```

## 🚫 Common Mistakes to Avoid

### Mistake 1: Direct Store Creation

```typescript
// ❌ NEVER do this
const useFeatureStore = create<FeatureStoreType>()((set, get) => ({
    // Direct implementation
}));

// ✅ ALWAYS use factory pattern
const _featureStore = (instanceId: string): StateCreator<FeatureStoreType> => {
    return (set, get): FeatureStoreType => ({
        // Factory implementation
    });
};
```

### Mistake 2: Missing Instance ID in Logs

```typescript
// ❌ NEVER do this
logger.info('Action completed', params);

// ✅ ALWAYS include instance ID
logger.info(`${instanceId}: Action completed`, params);
```

### Mistake 3: Incorrect Middleware Order

```typescript
// ❌ WRONG order
create<StoreType>()(
    devtools(
        subscribeWithSelector(
            persist(_store('id'), { name: 'store' })
        )
    )
);

// ✅ CORRECT order
create<StoreType>()(
    subscribeWithSelector(
        devtools(
            persist(_store('id'), { name: 'store' }),
            { name: 'store-id' }
        )
    )
);
```

### Mistake 4: Missing Error Handling

```typescript
// ❌ NEVER do this
loadData: async () => {
    const data = await apiService.getData(); // No error handling
    set({ data });
},

// ✅ ALWAYS handle errors
loadData: async () => {
    set((state) => ({ ...state, isLoading: true, error: null }));
    try {
        const data = await apiService.getData();
        set((state) => ({ ...state, data, isLoading: false }));
        logger.info(`${instanceId}: loadData completed`);
    } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        set((state) => ({ ...state, error: errorMessage, isLoading: false }));
        logger.error(`${instanceId}: loadData error`, error);
    }
},
```

### Mistake 5: Missing Reset Method

```typescript
// ❌ NEVER forget reset method
interface StoreType {
    data: any[];
    loadData: () => void;
    // Missing reset method
}

// ✅ ALWAYS include reset method
interface StoreType {
    data: any[];
    loadData: () => void;
    reset: () => void; // Always include
}
```

## 📝 Naming Conventions

### File Names

- Use kebab-case: `feature-name.store.ts`
- Always end with `.store.ts`
- Be descriptive: `trip-filters.store.ts`, not `filters.store.ts`

### Store Names

- Factory function: `_featureName` (underscore prefix)
- Global instance: `useFeatureNameStore`
- Factory export: `createFeatureNameStore`
- DevTools name: `feature-name-store-{instanceId}`

### Instance IDs

- Use descriptive names: `'trip-panel-123'`, not `'instance1'`
- Include component context: `'ports-management-panel'`
- For global instances: `'global'` or `'main'`

## 🔍 Code Review Checklist

**Before submitting any Zustand store code, verify:**

### Structure

- [ ] Uses factory pattern with underscore prefix
- [ ] Follows correct file structure order
- [ ] Includes both global instance and factory exports
- [ ] Has proper TypeScript interfaces

### Implementation

- [ ] All actions include instance ID in logs
- [ ] Error handling is implemented for async actions
- [ ] Loading states are managed properly
- [ ] Reset method is included and implemented

### Middleware

- [ ] Correct middleware order (subscribeWithSelector → devtools → persist → factory)
- [ ] DevTools naming includes instance ID
- [ ] Persistence keys use LocalStorageKeys enum when applicable

### Testing

- [ ] Store can be instantiated with different instance IDs
- [ ] All actions work correctly
- [ ] Error scenarios are handled
- [ ] Reset functionality works

## 🚀 Migration Guidelines

### When Modifying Existing Stores

1. **Analyze Current Pattern**: Check if store already uses factory pattern
2. **Preserve Existing API**: Don't break existing component usage
3. **Add Factory Gradually**: Add factory export alongside existing global export
4. **Update Documentation**: Update any related documentation
5. **Test Thoroughly**: Ensure no regressions in existing functionality

### When Creating New Stores

1. **Start with Template**: Use appropriate pattern template from this guide
2. **Define Types First**: Create comprehensive TypeScript interfaces
3. **Implement Factory**: Always use factory pattern from the start
4. **Add Logging**: Include instance ID in all log messages
5. **Test Multiple Instances**: Verify factory creates isolated instances

## 📊 Performance Guidelines

### Memory Management

- Always implement reset methods for cleanup
- Consider instance pooling for frequently created/destroyed stores
- Monitor memory usage with DevTools
- Clean up subscriptions in reset methods

### Selector Optimization

- Use shallow selectors when possible
- Avoid creating new objects in selectors
- Consider memoization for expensive computations
- Use subscribeWithSelector for fine-grained updates

### Bundle Size

- Import only needed Zustand features
- Use tree-shaking friendly imports
- Consider lazy loading for large stores
- Minimize middleware usage in production

## 🔧 Debugging Guidelines

### DevTools Usage

- Always name stores with instance ID
- Use descriptive action names
- Include relevant data in DevTools actions
- Disable DevTools in production builds

### Logging Standards

- Include instance ID in all log messages
- Use appropriate log levels (info, warn, error)
- Log action parameters and results
- Avoid logging sensitive data

### Common Issues

- **State not updating**: Check if using correct selector
- **Multiple instances interfering**: Verify instance isolation
- **Memory leaks**: Ensure proper cleanup in reset methods
- **Performance issues**: Review selector usage and middleware

## 📋 Quick Reference

### Essential Imports

```typescript
import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { persist } from 'zustand/middleware'; // If persistence needed
```

### Basic Factory Template

```typescript
const _storeName = (instanceId: string): StateCreator<StoreType> => {
    return (set, get): StoreType => ({
        ...DEFAULT_STATE,
        // Actions with instanceId logging
        reset: () => set(() => ({ ...DEFAULT_STATE })),
    });
};
```

### Export Pattern

```typescript
export const useStoreNameStore = create<StoreType>()(
    subscribeWithSelector(
        devtools(_storeName('global'), { name: 'store-name-global' })
    ),
);

export const createStoreNameStore = (instanceId: string) =>
    create<StoreType>()(
        subscribeWithSelector(
            devtools(_storeName(instanceId), { name: `store-name-${instanceId}` })
        ),
    );
```

---

**Remember**: These guidelines ensure consistency, maintainability, and scalability across all Zustand implementations in the TTS Frontend application. Always follow these patterns when working with stores.
