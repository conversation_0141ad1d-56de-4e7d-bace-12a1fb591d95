# Task 004: Right Drawer System Overview Component

## Task Overview

**Component System**: Reusable System Cards Architecture
**Priority**: Medium
**Developer Assignment**: Developer 
**Dependencies**: DashboardLayout (Task 001)
**Location**: `src/components/features/dashboard/layout/`

## Component Description

Create a right drawer system that displays system overview cards using a reusable component architecture. The system should be built around generic, configurable components that can accommodate various system types (Fire Alarm, Access Control, CCTV, Gate Barriers) through props rather than hardcoded implementations.

## Key Observations from the UI

Each system card (Fire Alarm, Access Control, CCTV, Gate Barriers) shares a **consistent layout pattern**:

- **Title Section**: System name with corresponding icon
- **Primary Metric**: Main count (total devices, doors, cameras, barriers)
- **Secondary Metrics**: Supporting data (alarms, incidents, open/closed status, unauthorized attempts)
- **Status Highlights**: Critical alerts displayed with red emphasis for immediate attention
- **Visual Consistency**: Uniform spacing, typography, and card structure across all system types

This consistent pattern suggests implementing a **generic `SystemCard` component** that can be configured through props to display any system type, making the dashboard extensible and maintainable.

## Suggested Reusable Components

### 1. SystemCard (Base Component)

**Purpose**: Generic card component for displaying any system type

**Props Structure**:

- `title` (string): System name (e.g., "Fire Alarm", "Access Control")
- `icon` (React component/icon name): Visual identifier for the system
- `primaryMetric` (number): Main count value
- `primaryLabel` (string): Description for primary metric (e.g., "Total devices")
- `secondaryMetrics` (array): Collection of `{label, value, alert?: boolean}` objects

**Responsibilities**:

- Handles consistent layout, spacing, and styling across all system types
- Supports alert highlighting (red emphasis, animations) based on `alert` flag
- Maintains visual hierarchy and responsive behavior

### 2. SystemOverview (Container Component)

**Purpose**: Orchestrates multiple SystemCard instances

**Responsibilities**:

- Maps system types (fire alarm, access control, etc.) into SystemCard instances
- Retrieves data from Zustand store (`useSystemStore`)
- Manages grid layout responsiveness (2x2 grid, stacked on mobile)
- Handles loading states and error boundaries

### 3. AlertBadge (Micro Component)

**Purpose**: Standardized alert emphasis for numeric values

**Props Structure**:

- `value` (number/string): The metric value to display
- `isAlert` (boolean): Triggers red color and animation when true

**Responsibilities**:

- Provides consistent alert styling across Fire, CCTV, Gate systems
- Handles animation states for critical alerts
- Maintains accessibility standards for color-blind users

### 4. MetricDisplay (Micro Component)

**Purpose**: Standardizes label + value pair presentation

**Props Structure**:

- `label` (string): Descriptive text for the metric
- `value` (number/string): The actual metric value
- `highlight` (boolean, optional): Applies emphasis styling

**Responsibilities**:

- Prevents layout logic duplication across cards
- Ensures consistent typography and spacing
- Supports optional highlighting for important metrics

## Zustand Store Architecture

### useSystemStore (Central State Management)

**Purpose**: Centralized state management for all system metrics

**Store Structure**:

- `fireAlarm`: `{totalDevices, activeAlarms}`
- `accessControl`: `{totalDoors, open, closed}`
- `cctv`: `{totalCameras, activeIncidents}`
- `gateBarriers`: `{totalBarriers, unauthorizedAttempts}`

**Key Features**:

- **Real-time Updates**: Supports API polling and WebSocket integration
- **Selective Subscriptions**: Components subscribe only to needed data slices
- **Performance Optimization**: Prevents unnecessary re-renders
- **Extensibility**: Easy to add new system types without code changes

**State Management Benefits**:

- Single source of truth for all system data
- Automatic UI updates when data changes
- Simplified component logic (no local state management)
- Consistent data flow across the application

## Theme and Styling Layer

### CardTheme System

**Purpose**: Consistent visual design across all system cards

**Theme Features**:

- **Dark/Light Mode Support**: Automatic theme switching capability
- **Color Palette**: Standardized colors for alerts, success, warning states
- **Typography Scale**: Consistent font sizes and weights
- **Spacing System**: Uniform padding, margins, and gaps
- **Border Radius**: Consistent corner rounding across components

**Styling Approach**:

- **Utility Class System**: Tailwind CSS or styled-components integration
- **Component Variants**: Pre-defined styles for different card states
- **Responsive Design**: Mobile-first approach with breakpoint consistency
- **Accessibility**: WCAG compliant color contrasts and focus states

## Example Architecture Flow

### Component Hierarchy and Data Flow

**Container Level**:

- `SystemOverview` (main container) renders multiple `SystemCard` instances
- Each `SystemCard` receives system-specific props (title, icon, metrics)
- Data flows from Zustand store → `SystemOverview` → individual `SystemCard`s

**Component Composition**:

- Each `SystemCard` internally uses `MetricDisplay` components for consistent formatting
- Critical alerts utilize `AlertBadge` components for visual emphasis
- All components share the same `CardTheme` for visual consistency

**Data Management**:

- **Single Fetch**: Data is fetched once in Zustand store
- **Selective Subscriptions**: Cards subscribe only to their specific data slice
- **Real-time Updates**: WebSocket or polling updates automatically refresh UI
- **Performance**: No unnecessary re-renders due to selective subscriptions

### Extensibility Benefits

**Future-Proof Design**:

- Adding new system types (PA system, lighting control) requires only:
  - New data structure in Zustand store
  - Props configuration for `SystemCard`
  - No new layout or styling code needed

**Config-Driven Approach**:

- Each system card is just a **configured instance** of the base `SystemCard` component
- System types, metrics, and alerts are defined through data configuration
- Dashboard becomes fully **dynamic** and **maintainable**

**Scalability**:

- Component architecture supports unlimited system types
- Consistent user experience across all system cards
- Simplified testing and maintenance due to reusable components

## Summary

This task establishes a **future-proof, config-driven dashboard architecture** using reusable components that eliminate code duplication and ensure visual consistency across all system types.

### Key Components

**Core Architecture**:

- **SystemCard**: Generic base component configurable for any system type
- **SystemOverview**: Container managing multiple system cards with responsive layout
- **AlertBadge**: Standardized alert emphasis with accessibility support
- **MetricDisplay**: Consistent label-value formatting across all cards

**State Management**:

- **Zustand Store**: Centralized state for all system metrics with real-time updates
- **Selective Subscriptions**: Components subscribe only to needed data slices
- **Performance Optimization**: Prevents unnecessary re-renders through targeted updates

**Design System**:

- **CardTheme**: Consistent visual design supporting dark/light modes
- **Utility Classes**: Tailwind CSS integration for maintainable styling
- **Responsive Design**: Mobile-first approach with consistent breakpoints
- **Accessibility**: WCAG compliant with proper color contrasts and focus states

### Architecture Benefits

**Extensibility**: Adding new system types (PA system, lighting control) requires only data configuration - no new layout or styling code

**Maintainability**: Single source of truth for styling and behavior across all system cards

**Performance**: Optimized data flow with selective subscriptions and minimal re-renders

**Consistency**: Uniform user experience across Fire Alarm, Access Control, CCTV, and Gate Barrier systems

**Future-Proof**: Config-driven approach supports unlimited system types through props rather than hardcoded implementations
