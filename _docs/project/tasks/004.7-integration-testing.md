# Task 004.7: Integration & Testing

## Task Overview

**Component**: Right Drawer System Integration
**Priority**: High
**Developer Assignment**: Developer
**Dependencies**: 
- AlertBadge (Task 004.1)
- MetricDisplay (Task 004.2)
- useSystemStore (Task 004.3)
- SystemCard (Task 004.4)
- SystemOverview (Task 004.5)
- CardTheme System (Task 004.6)
**Location**: `src/components/features/dashboard/layout/RightDrawer/`

## Integration Description

Integrate all components into a complete right drawer system and implement comprehensive testing to ensure reliability, performance, and accessibility. This task validates the entire component architecture and prepares the system for production deployment.

## System Integration Diagram

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Right Drawer System - Complete Integration Flow                             │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                        DashboardLayout                                 │ │
│ │ ┌─────────────────┐                    ┌─────────────────────────────┐ │ │
│ │ │   Main Content  │                    │        RightDrawer          │ │ │
│ │ │                 │                    │ ┌─────────────────────────┐ │ │ │
│ │ │                 │                    │ │    SystemOverview       │ │ │ │
│ │ │                 │                    │ │ ┌─────────┐ ┌─────────┐ │ │ │ │
│ │ │                 │ ◄──── Data ────► │ │ │FireAlarm│ │ Access  │ │ │ │ │
│ │ │                 │      Flow        │ │ │ Card    │ │ Control │ │ │ │ │
│ │ │                 │                    │ │ └─────────┘ └─────────┘ │ │ │ │
│ │ │                 │                    │ │ ┌─────────┐ ┌─────────┐ │ │ │ │
│ │ │                 │                    │ │ │  CCTV   │ │  Gate   │ │ │ │ │
│ │ │                 │                    │ │ │  Card   │ │Barriers │ │ │ │ │
│ │ │                 │                    │ │ └─────────┘ └─────────┘ │ │ │ │
│ │ └─────────────────┘                    │ └─────────────────────────┘ │ │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                      useSystemStore (Zustand)                          │ │
│ │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │
│ │ │ Fire Alarm  │ │Access Control│ │    CCTV     │ │Gate Barriers│       │ │
│ │ │    Data     │ │    Data      │ │    Data     │ │    Data     │       │ │
│ │ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘       │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                    ▲                                       │
│                                    │                                       │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                        API Layer & WebSocket                           │ │
│ │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │
│ │ │   REST API  │ │  WebSocket  │ │   Polling   │ │Error Handler│       │ │
│ │ │  Endpoints  │ │   Client    │ │   Service   │ │   Service   │       │ │
│ │ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘       │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Integration Components

### RightDrawer Container
```typescript
interface RightDrawerProps {
  isOpen?: boolean;
  onClose?: () => void;
  width?: number;
  position?: 'right' | 'left';
  className?: string;
  testId?: string;
}

const RightDrawer: React.FC<RightDrawerProps> = ({
  isOpen = true,
  onClose,
  width = 400,
  position = 'right',
  className,
  testId = 'right-drawer'
}) => {
  return (
    <div 
      className={cn(
        'right-drawer',
        `right-drawer--${position}`,
        { 'right-drawer--open': isOpen },
        className
      )}
      style={{ width: `${width}px` }}
      data-testid={testId}
    >
      <div className="right-drawer__header">
        <h2 className="card-text-title">System Overview</h2>
        {onClose && (
          <button 
            onClick={onClose}
            className="right-drawer__close"
            aria-label="Close drawer"
          >
            <XIcon className="w-5 h-5" />
          </button>
        )}
      </div>
      
      <div className="right-drawer__content">
        <SystemOverview 
          onSystemClick={handleSystemNavigation}
          enableAutoRefresh={true}
          refreshInterval={30000}
        />
      </div>
    </div>
  );
};
```

### DashboardLayout Integration
```typescript
const DashboardLayout: React.FC = () => {
  const [isRightDrawerOpen, setIsRightDrawerOpen] = useState(true);
  
  return (
    <div className="dashboard-layout">
      <div className="dashboard-layout__main">
        {/* Main dashboard content */}
      </div>
      
      <RightDrawer 
        isOpen={isRightDrawerOpen}
        onClose={() => setIsRightDrawerOpen(false)}
        width={420}
      />
    </div>
  );
};
```

## Testing Strategy

### Unit Testing (Jest + React Testing Library)

#### Component Tests
```typescript
// AlertBadge.test.tsx
describe('AlertBadge', () => {
  it('renders normal state correctly', () => {
    render(<AlertBadge value={42} />);
    expect(screen.getByText('42')).toBeInTheDocument();
  });
  
  it('applies alert styling when isAlert is true', () => {
    render(<AlertBadge value={5} isAlert={true} />);
    const badge = screen.getByText('5');
    expect(badge).toHaveClass('alert-badge--pulse');
  });
  
  it('respects reduced motion preferences', () => {
    Object.defineProperty(window, 'matchMedia', {
      value: jest.fn(() => ({ matches: true }))
    });
    render(<AlertBadge value={5} isAlert={true} />);
    // Test that animation is disabled
  });
});

// MetricDisplay.test.tsx
describe('MetricDisplay', () => {
  it('renders horizontal layout by default', () => {
    render(<MetricDisplay label="Total Devices" value={42} />);
    expect(screen.getByText('Total Devices')).toBeInTheDocument();
    expect(screen.getByText('42')).toBeInTheDocument();
  });
  
  it('integrates with AlertBadge for alert states', () => {
    render(
      <MetricDisplay 
        label="Active Alarms" 
        value={5} 
        isAlert={true} 
      />
    );
    const alertValue = screen.getByText('5');
    expect(alertValue).toHaveClass('alert-badge--pulse');
  });
});

// SystemCard.test.tsx
describe('SystemCard', () => {
  const mockProps = {
    title: "Fire Alarm System",
    icon: FireIcon,
    primaryMetric: { label: "Total Devices", value: 42 },
    secondaryMetrics: [
      { label: "Active Alarms", value: 5, isAlert: true }
    ]
  };
  
  it('renders all metrics correctly', () => {
    render(<SystemCard {...mockProps} />);
    expect(screen.getByText('Fire Alarm System')).toBeInTheDocument();
    expect(screen.getByText('Total Devices')).toBeInTheDocument();
    expect(screen.getByText('42')).toBeInTheDocument();
    expect(screen.getByText('Active Alarms')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();
  });
  
  it('handles click events', () => {
    const onCardClick = jest.fn();
    render(<SystemCard {...mockProps} onCardClick={onCardClick} />);
    fireEvent.click(screen.getByRole('button'));
    expect(onCardClick).toHaveBeenCalled();
  });
});
```

#### Store Tests
```typescript
// systemStore.test.ts
describe('useSystemStore', () => {
  beforeEach(() => {
    useSystemStore.getState().resetStore();
  });
  
  it('initializes with default state', () => {
    const state = useSystemStore.getState();
    expect(state.fireAlarm.totalDevices).toBe(0);
    expect(state.fireAlarm.activeAlarms).toBe(0);
  });
  
  it('updates fire alarm data correctly', () => {
    const { updateFireAlarm } = useSystemStore.getState();
    updateFireAlarm({ totalDevices: 42, activeAlarms: 5 });
    
    const state = useSystemStore.getState();
    expect(state.fireAlarm.totalDevices).toBe(42);
    expect(state.fireAlarm.activeAlarms).toBe(5);
  });
  
  it('calculates total alerts correctly', () => {
    const { updateFireAlarm, updateCCTV, updateGateBarriers } = useSystemStore.getState();
    updateFireAlarm({ activeAlarms: 3 });
    updateCCTV({ activeIncidents: 2 });
    updateGateBarriers({ unauthorizedAttempts: 1 });
    
    const totalAlerts = selectors.getTotalAlerts(useSystemStore.getState());
    expect(totalAlerts).toBe(6);
  });
});
```

### Integration Testing

#### Component Integration
```typescript
// SystemOverview.integration.test.tsx
describe('SystemOverview Integration', () => {
  it('renders all system cards with store data', async () => {
    // Mock store data
    const mockStoreData = {
      fireAlarm: { totalDevices: 42, activeAlarms: 5 },
      accessControl: { totalDoors: 28, open: 12, closed: 16 },
      cctv: { totalCameras: 156, activeIncidents: 2 },
      gateBarriers: { totalBarriers: 8, unauthorizedAttempts: 3 }
    };
    
    // Setup store
    act(() => {
      Object.entries(mockStoreData).forEach(([system, data]) => {
        useSystemStore.getState()[`update${capitalize(system)}`](data);
      });
    });
    
    render(<SystemOverview />);
    
    // Verify all cards are rendered
    expect(screen.getByText('Fire Alarm System')).toBeInTheDocument();
    expect(screen.getByText('Access Control')).toBeInTheDocument();
    expect(screen.getByText('CCTV System')).toBeInTheDocument();
    expect(screen.getByText('Gate Barriers')).toBeInTheDocument();
    
    // Verify alert states
    const alertValues = screen.getAllByText(/[0-9]+/);
    const alertElements = alertValues.filter(el => 
      el.classList.contains('alert-badge--pulse')
    );
    expect(alertElements).toHaveLength(3); // Fire, CCTV, Gate alerts
  });
});
```

#### API Integration
```typescript
// api.integration.test.ts
describe('API Integration', () => {
  beforeEach(() => {
    fetchMock.resetMocks();
  });
  
  it('fetches all system data successfully', async () => {
    const mockData = {
      fireAlarm: { totalDevices: 42, activeAlarms: 5 },
      accessControl: { totalDoors: 28, open: 12, closed: 16 },
      cctv: { totalCameras: 156, activeIncidents: 2 },
      gateBarriers: { totalBarriers: 8, unauthorizedAttempts: 3 }
    };
    
    fetchMock.mockResponseOnce(JSON.stringify(mockData));
    
    const { fetchSystemData } = useSystemStore.getState();
    await fetchSystemData();
    
    const state = useSystemStore.getState();
    expect(state.fireAlarm.totalDevices).toBe(42);
    expect(state.accessControl.totalDoors).toBe(28);
  });
  
  it('handles API errors gracefully', async () => {
    fetchMock.mockRejectOnce(new Error('Network error'));
    
    const { fetchSystemData } = useSystemStore.getState();
    await expect(fetchSystemData()).rejects.toThrow('Network error');
    
    // Verify error state is set
    const state = useSystemStore.getState();
    expect(state.fireAlarm.isLoading).toBe(false);
  });
});
```

### End-to-End Testing (Playwright)

```typescript
// rightDrawer.e2e.test.ts
test.describe('Right Drawer System', () => {
  test('displays system overview with real-time updates', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Verify right drawer is visible
    const rightDrawer = page.locator('[data-testid="right-drawer"]');
    await expect(rightDrawer).toBeVisible();
    
    // Verify all system cards are present
    await expect(page.locator('text=Fire Alarm System')).toBeVisible();
    await expect(page.locator('text=Access Control')).toBeVisible();
    await expect(page.locator('text=CCTV System')).toBeVisible();
    await expect(page.locator('text=Gate Barriers')).toBeVisible();
    
    // Test alert animations
    const alertBadge = page.locator('.alert-badge--pulse').first();
    await expect(alertBadge).toHaveCSS('animation-name', 'alert-pulse');
  });
  
  test('navigates to system details on card click', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Click on Fire Alarm card
    await page.click('text=Fire Alarm System');
    
    // Verify navigation
    await expect(page).toHaveURL('/dashboard/fire-alarm');
  });
  
  test('responsive design works on mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/dashboard');
    
    // Verify mobile layout
    const systemGrid = page.locator('.system-grid');
    await expect(systemGrid).toHaveCSS('grid-template-columns', '1fr');
  });
});
```

### Performance Testing

```typescript
// performance.test.ts
describe('Performance Tests', () => {
  it('renders 1000 metric displays without performance issues', () => {
    const startTime = performance.now();
    
    const metrics = Array.from({ length: 1000 }, (_, i) => (
      <MetricDisplay 
        key={i}
        label={`Metric ${i}`}
        value={Math.floor(Math.random() * 100)}
        isAlert={Math.random() > 0.8}
      />
    ));
    
    render(<div>{metrics}</div>);
    
    const endTime = performance.now();
    expect(endTime - startTime).toBeLessThan(100); // 100ms threshold
  });
  
  it('store updates do not cause excessive re-renders', () => {
    const renderCount = jest.fn();
    
    const TestComponent = () => {
      renderCount();
      const fireAlarmData = useSystemStore(state => state.fireAlarm);
      return <div>{fireAlarmData.totalDevices}</div>;
    };
    
    render(<TestComponent />);
    
    // Update unrelated store data
    act(() => {
      useSystemStore.getState().updateAccessControl({ totalDoors: 30 });
    });
    
    // Should not cause re-render
    expect(renderCount).toHaveBeenCalledTimes(1);
  });
});
```

### Accessibility Testing

```typescript
// accessibility.test.ts
describe('Accessibility Tests', () => {
  it('meets WCAG AA standards', async () => {
    const { container } = render(
      <SystemOverview />
    );
    
    const results = await axe(container);
    expect(results).toHaveNoViolations();
  });
  
  it('supports keyboard navigation', () => {
    render(<SystemOverview />);
    
    // Tab through cards
    userEvent.tab();
    expect(screen.getByText('Fire Alarm System')).toHaveFocus();
    
    userEvent.tab();
    expect(screen.getByText('Access Control')).toHaveFocus();
  });
  
  it('provides proper ARIA labels', () => {
    render(
      <SystemCard
        title="Fire Alarm System"
        icon={FireIcon}
        primaryMetric={{ label: "Total Devices", value: 42 }}
        secondaryMetrics={[
          { label: "Active Alarms", value: 5, isAlert: true }
        ]}
      />
    );
    
    const card = screen.getByRole('button');
    expect(card).toHaveAttribute('aria-label', 'Fire Alarm System card');
  });
});
```

## File Structure

```
src/components/features/dashboard/layout/RightDrawer/
├── index.ts
├── RightDrawer.tsx
├── RightDrawer.test.tsx
├── RightDrawer.stories.tsx
└── integration/
    ├── SystemOverview.integration.test.tsx
    ├── api.integration.test.ts
    └── performance.test.ts

tests/
├── e2e/
│   ├── rightDrawer.e2e.test.ts
│   └── accessibility.e2e.test.ts
├── integration/
│   └── fullSystem.integration.test.ts
└── performance/
    └── renderPerformance.test.ts
```

## Success Criteria

- [ ] All components integrate seamlessly without conflicts
- [ ] Right drawer renders correctly in DashboardLayout
- [ ] Real-time data updates work through WebSocket and polling
- [ ] All unit tests pass with >95% code coverage
- [ ] Integration tests validate component interactions
- [ ] E2E tests cover critical user journeys
- [ ] Performance benchmarks meet requirements (<100ms render time)
- [ ] Accessibility tests pass WCAG AA compliance
- [ ] Responsive design works across all device sizes
- [ ] Error handling and loading states function properly
- [ ] Theme switching works without visual glitches
- [ ] Memory leaks are prevented in long-running sessions

## Deployment Checklist

- [ ] All dependencies are properly installed and configured
- [ ] Environment variables are set for API endpoints
- [ ] WebSocket connection is configured for production
- [ ] CSS theme files are properly bundled and optimized
- [ ] TypeScript types are exported for external consumption
- [ ] Storybook documentation is complete and published
- [ ] Performance monitoring is set up for production
- [ ] Error tracking is configured for runtime issues
- [ ] Accessibility audit is completed and documented
- [ ] Browser compatibility is tested across target browsers

## Monitoring and Maintenance

### Performance Monitoring
```typescript
// Performance tracking
const trackRenderPerformance = (componentName: string) => {
  const startTime = performance.now();
  
  return () => {
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    if (renderTime > 16) { // 60fps threshold
      console.warn(`Slow render detected: ${componentName} took ${renderTime}ms`);
    }
  };
};
```

### Error Boundaries
```typescript
class SystemOverviewErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to monitoring service
    console.error('SystemOverview Error:', error, errorInfo);
  }
  
  render() {
    if (this.state.hasError) {
      return <SystemOverviewFallback />;
    }
    
    return this.props.children;
  }
}
```

## Future Enhancements

- **Advanced Analytics**: User interaction tracking and usage patterns
- **A/B Testing**: Framework for testing different layouts and features
- **Internationalization**: Multi-language support for global deployment
- **Advanced Caching**: Service worker integration for offline functionality
- **Real-time Collaboration**: Multi-user real-time updates and notifications