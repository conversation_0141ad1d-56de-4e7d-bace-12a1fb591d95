# Task 004.2: MetricDisplay Component

## Task Overview

**Component**: MetricDisplay (Micro Component)
**Priority**: High
**Developer Assignment**: Developer
**Dependencies**: AlertBadge (Task 004.1)
**Location**: `src/components/ui/MetricDisplay/`

## Component Description

Create a standardized component for displaying label-value pairs with consistent typography and spacing. This component will be used across all system cards to ensure uniform presentation of metrics like "Total Devices: 42" or "Active Alarms: 5".

## UI Drawing

```
┌─────────────────────────────────────────────────────┐
│ MetricDisplay Component Layouts                     │
├─────────────────────────────────────────────────────┤
│                                                     │
│ Horizontal Layout (default):                        │
│ ┌─────────────────────────────────────────────────┐ │
│ │ Total Devices                              42   │ │
│ └─────────────────────────────────────────────────┘ │
│                                                     │
│ Vertical Layout:                                    │
│ ┌─────────────────────────────────────────────────┐ │
│ │ Total Devices                                   │ │
│ │        42                                       │ │
│ └─────────────────────────────────────────────────┘ │
│                                                     │
│ Alert State (horizontal):                           │
│ ┌─────────────────────────────────────────────────┐ │
│ │ Active Alarms                               5   │ │
│ │                                        (red+pulse)│ │
│ └─────────────────────────────────────────────────┘ │
│                                                     │
│ Highlighted State:                                  │
│ ┌─────────────────────────────────────────────────┐ │
│ │ Open Doors                                 12   │ │
│ │                                      (emphasized)│ │
│ └─────────────────────────────────────────────────┘ │
│                                                     │
└─────────────────────────────────────────────────────┘
```

## Props Structure

```typescript
interface MetricDisplayProps {
  label: string;
  value: number | string;
  layout?: 'horizontal' | 'vertical';
  highlight?: boolean;
  isAlert?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  labelClassName?: string;
  valueClassName?: string;
}
```

## Responsibilities

- **Consistent Layout**: Standardizes label-value presentation across all system cards
- **Typography Management**: Ensures consistent font sizes, weights, and spacing
- **Alert Integration**: Uses AlertBadge component for alert states
- **Layout Flexibility**: Supports both horizontal and vertical arrangements
- **Emphasis Support**: Provides highlighting for important metrics
- **Responsive Design**: Adapts to different screen sizes and container widths

## Implementation Requirements

### Layout Specifications
- **Horizontal Layout**: Label on left, value on right with space-between
- **Vertical Layout**: Label on top, value below with center alignment
- **Spacing**: Consistent gap between label and value (0.5rem horizontal, 0.25rem vertical)
- **Alignment**: Proper text alignment for readability

### Typography System
- **Label Styling**: Medium weight, muted color (gray-600/gray-400)
- **Value Styling**: Semibold weight, primary color (gray-900/gray-100)
- **Size Variants**: Small (text-sm), Medium (text-base), Large (text-lg)
- **Hierarchy**: Clear visual hierarchy between label and value

### State Management
- **Normal State**: Standard label and value presentation
- **Highlight State**: Enhanced emphasis for important metrics
- **Alert State**: Integrates AlertBadge for critical values
- **Loading State**: Optional skeleton/placeholder support

### Responsive Behavior
- **Mobile**: Automatic switch to vertical layout on small screens
- **Container Queries**: Adapts based on parent container width
- **Text Overflow**: Proper handling of long labels and values
- **Breakpoints**: Consistent with design system breakpoints

## File Structure

```
src/components/ui/MetricDisplay/
├── index.ts
├── MetricDisplay.tsx
├── MetricDisplay.test.tsx
└── MetricDisplay.stories.tsx
```

## Testing Requirements

- **Unit Tests**: Props handling, layout switching, AlertBadge integration
- **Visual Tests**: Storybook stories for all layouts and states
- **Responsive Tests**: Layout behavior at different screen sizes
- **Accessibility Tests**: Screen reader compatibility, proper semantic structure
- **Integration Tests**: AlertBadge component integration

## Success Criteria

- [ ] Component renders correctly in horizontal and vertical layouts
- [ ] Proper integration with AlertBadge component for alert states
- [ ] Supports all size variants and custom styling
- [ ] Responsive behavior works across different screen sizes
- [ ] Highlight state provides clear visual emphasis
- [ ] Typography follows design system specifications
- [ ] Passes accessibility audits (proper semantic structure)
- [ ] Complete test coverage including AlertBadge integration
- [ ] Storybook documentation with all variants and states

## Usage Examples

```tsx
// Basic horizontal layout
<MetricDisplay 
  label="Total Devices" 
  value={42} 
/>

// Vertical layout with alert
<MetricDisplay 
  label="Active Alarms" 
  value={5} 
  layout="vertical"
  isAlert={true}
/>

// Highlighted metric
<MetricDisplay 
  label="Open Doors" 
  value={12} 
  highlight={true}
  size="lg"
/>

// Custom styling
<MetricDisplay 
  label="Total Cameras" 
  value="156"
  className="border-l-2 border-blue-500 pl-3"
  labelClassName="text-blue-600"
/>
```

## Integration Notes

- **AlertBadge Dependency**: Must import and use AlertBadge component for alert states
- **Design System**: Follows established typography and spacing tokens
- **Theme Support**: Works with both light and dark themes
- **Performance**: Optimized for multiple instances on the same page

## Future Considerations

- **Icon Support**: Potential for adding icons next to labels
- **Tooltip Integration**: Support for additional context on hover
- **Animation**: Smooth transitions between states
- **Localization**: Support for RTL languages and number formatting