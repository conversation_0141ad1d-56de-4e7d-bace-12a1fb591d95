# Dashboard Components Folder Structure Analysis

## Current Project Structure Overview

Based on the existing codebase analysis, the project follows a well-organized component architecture:

```
src/components/
├── common/           # Shared, reusable UI components
│   └── ui/          # Basic UI building blocks (<PERSON><PERSON>, Card, Input, Label)
├── features/        # Feature-specific components
│   ├── auth/        # Authentication components
│   ├── floor-plan/  # Floor plan related components (existing)
│   └── layout/      # Layout components (<PERSON>er, Footer, Layout)
└── pages/           # Page-level components (DashboardPage,AlertDashboardPage, HomePage)
```

## Recommended Component Locations for Dashboard Layout

### 1. Dashboard Layout Components Location

**Recommended Path**: `src/components/features/dashboard/`

**Rationale**:

- Dashboard is a specific feature domain
- Components will be moderately reusable within dashboard context
- Follows existing pattern (similar to `floor-plan/` feature)
- Separates dashboard-specific components from generic layout

### 2. Proposed Dashboard Component Structure

```
src/components/features/dashboard/
├── layout/
│   ├── DashboardLayout.tsx        # Main dashboard container
│   ├── LeftSidebar.tsx           # Navigation sidebar
│   ├── TopBar.tsx                # Header with filters
│   ├── RightDrawer.tsx           # System overview drawer
│   └── ContentBody.tsx           # 2D plan content area
├── components/
│   ├── SystemCard.tsx            # Individual system status cards
│   ├── FilterDropdown.tsx        # Reusable filter component
│   ├── DeviceIndicator.tsx       # Device status markers
│   └── AlertBadge.tsx            # Notification badges
├── types.ts                      # Dashboard-specific types
└── index.ts                      # Export barrel
```

### 3. Alternative Approach (If Components Need Higher Reusability)

If dashboard components might be reused across different features:

```
src/components/common/layout/
├── DashboardLayout.tsx
├── Sidebar.tsx
├── TopBar.tsx
├── Drawer.tsx
└── ScrollableContent.tsx
```

**However, the feature-specific approach is recommended** because:

- Dashboard components have specific business logic
- They're unlikely to be reused outside dashboard context
- Maintains clear separation of concerns
- Follows existing project patterns

## Integration with Existing Structure

### Current Layout Component

- **Location**: `src/components/features/layout/Layout.tsx`
- **Purpose**: Generic app layout with header/footer
- **Usage**: Wraps entire application

### New Dashboard Layout

- **Location**: `src/components/features/dashboard/layout/DashboardLayout.tsx`
- **Purpose**: Specific dashboard layout with sidebars and content area
- **Usage**: Used within dashboard pages only
- **Relationship**: Can be used inside the existing Layout component

## Component Hierarchy Recommendation

```
App Layout (existing)
└── Dashboard Layout (new)
    ├── Left Sidebar
    ├── Top Bar
    ├── Content Body
    └── Right Drawer
```

## Export Strategy

### Main Dashboard Index

```typescript
// src/components/features/dashboard/index.ts
export { default as DashboardLayout } from './layout/DashboardLayout';
export { default as LeftSidebar } from './layout/LeftSidebar';
export { default as TopBar } from './layout/TopBar';
export { default as RightDrawer } from './layout/RightDrawer';
export { default as ContentBody } from './layout/ContentBody';

// Re-export types
export * from './types';
```

### Component-Level Exports

```typescript
// src/components/features/dashboard/layout/index.ts
export { default as DashboardLayout } from './DashboardLayout';
export { default as LeftSidebar } from './LeftSidebar';
export { default as TopBar } from './TopBar';
export { default as RightDrawer } from './RightDrawer';
export { default as ContentBody } from './ContentBody';
```

## Benefits of This Structure

1. **Clear Separation**: Dashboard components are isolated from generic layout
2. **Scalability**: Easy to add more dashboard-specific components
3. **Maintainability**: Components are logically grouped
4. **Consistency**: Follows existing project patterns
5. **Reusability**: Components can be reused within dashboard context
6. **Type Safety**: Dedicated types file for dashboard-specific interfaces

## Implementation Priority

1. **High Priority**: Layout container components (DashboardLayout, sidebars)
2. **Medium Priority**: Content and interaction components
3. **Low Priority**: Utility and helper components

This structure provides a solid foundation for the 3-developer team to work on different components simultaneously without conflicts.
