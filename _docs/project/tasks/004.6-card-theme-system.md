# Task 004.6: CardTheme System

## Task Overview

**Component**: CardTheme System (Design System)
**Priority**: Medium
**Developer Assignment**: Developer
**Dependencies**: None (foundational styling)
**Location**: `src/styles/themes/cardTheme/`

## System Description

Create a comprehensive theme and styling system for all system cards, ensuring visual consistency, accessibility, and support for dark/light modes. This system provides the design foundation for AlertBadge, MetricDisplay, SystemCard, and SystemOverview components.

## Theme Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ CardTheme System Architecture                                               │
├─────────────────────────────────────────────────────────────────────────────┤
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                          Color Palette                                 │ │
│ │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │
│ │ │   Primary   │ │   Success   │ │   Warning   │ │    Alert    │       │ │
│ │ │ Light: #fff │ │ Light: #10b │ │ Light: #f59 │ │ Light: #ef4 │       │ │
│ │ │ Dark: #1f2  │ │ Dark: #059  │ │ Dark: #d97  │ │ Dark: #dc2  │       │ │
│ │ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘       │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                        Typography Scale                                 │ │
│ │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │
│ │ │    Title    │ │   Primary   │ │   Secondary │ │    Caption  │       │ │
│ │ │   18px/24   │ │   16px/20   │ │   14px/18   │ │   12px/16   │       │ │
│ │ │ font-semib  │ │ font-medium │ │ font-normal │ │ font-normal │       │ │
│ │ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘       │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                         Spacing System                                 │ │
│ │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │
│ │ │     XS      │ │     SM      │ │     MD      │ │     LG      │       │ │
│ │ │    4px      │ │    8px      │ │    16px     │ │    24px     │       │ │
│ │ │   0.25rem   │ │   0.5rem    │ │    1rem     │ │   1.5rem    │       │ │
│ │ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘       │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
│ ┌─────────────────────────────────────────────────────────────────────────┐ │
│ │                        Component Variants                              │ │
│ │ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐       │ │
│ │ │   Default   │ │   Hover     │ │   Focus     │ │   Alert     │       │ │
│ │ │ bg-white    │ │ bg-gray-50  │ │ ring-2      │ │ border-red  │       │ │
│ │ │ border-gray │ │ shadow-md   │ │ ring-blue   │ │ bg-red-50   │       │ │
│ │ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘       │ │
│ └─────────────────────────────────────────────────────────────────────────┘ │
│                                                                             │
└─────────────────────────────────────────────────────────────────────────────┘
```

## Color System

### Light Theme Palette
```css
:root {
  /* Primary Colors */
  --card-bg-primary: #ffffff;
  --card-bg-secondary: #f8fafc;
  --card-border: #e2e8f0;
  --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
  
  /* Text Colors */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-muted: #64748b;
  --text-caption: #94a3b8;
  
  /* State Colors */
  --color-success: #10b981;
  --color-success-bg: #ecfdf5;
  --color-warning: #f59e0b;
  --color-warning-bg: #fffbeb;
  --color-alert: #ef4444;
  --color-alert-bg: #fef2f2;
  
  /* Interactive States */
  --card-hover-bg: #f1f5f9;
  --card-focus-ring: #3b82f6;
  --card-active-bg: #e2e8f0;
}
```

### Dark Theme Palette
```css
[data-theme="dark"] {
  /* Primary Colors */
  --card-bg-primary: #1e293b;
  --card-bg-secondary: #0f172a;
  --card-border: #334155;
  --card-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.3);
  
  /* Text Colors */
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-muted: #94a3b8;
  --text-caption: #64748b;
  
  /* State Colors */
  --color-success: #059669;
  --color-success-bg: #064e3b;
  --color-warning: #d97706;
  --color-warning-bg: #92400e;
  --color-alert: #dc2626;
  --color-alert-bg: #991b1b;
  
  /* Interactive States */
  --card-hover-bg: #334155;
  --card-focus-ring: #60a5fa;
  --card-active-bg: #475569;
}
```

## Typography System

```css
/* Typography Scale */
.card-text-title {
  font-size: 1.125rem; /* 18px */
  line-height: 1.5rem; /* 24px */
  font-weight: 600;
  color: var(--text-primary);
}

.card-text-primary {
  font-size: 1rem; /* 16px */
  line-height: 1.25rem; /* 20px */
  font-weight: 500;
  color: var(--text-primary);
}

.card-text-secondary {
  font-size: 0.875rem; /* 14px */
  line-height: 1.125rem; /* 18px */
  font-weight: 400;
  color: var(--text-secondary);
}

.card-text-caption {
  font-size: 0.75rem; /* 12px */
  line-height: 1rem; /* 16px */
  font-weight: 400;
  color: var(--text-caption);
}

.card-text-muted {
  color: var(--text-muted);
}
```

## Spacing System

```css
/* Spacing Tokens */
:root {
  --spacing-xs: 0.25rem; /* 4px */
  --spacing-sm: 0.5rem;  /* 8px */
  --spacing-md: 1rem;    /* 16px */
  --spacing-lg: 1.5rem;  /* 24px */
  --spacing-xl: 2rem;    /* 32px */
  
  /* Card-specific spacing */
  --card-padding: var(--spacing-lg);
  --card-gap: var(--spacing-md);
  --metric-gap: var(--spacing-sm);
  --icon-margin: var(--spacing-sm);
}
```

## Component Variants

### Base Card Styles
```css
.system-card {
  background-color: var(--card-bg-primary);
  border: 1px solid var(--card-border);
  border-radius: 0.75rem; /* 12px */
  box-shadow: var(--card-shadow);
  padding: var(--card-padding);
  transition: all 0.2s ease-in-out;
}

.system-card:hover {
  background-color: var(--card-hover-bg);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
  transform: translateY(-1px);
}

.system-card:focus-within {
  outline: none;
  ring: 2px solid var(--card-focus-ring);
  ring-offset: 2px;
}
```

### Alert Card Variant
```css
.system-card--alert {
  border-color: var(--color-alert);
  background-color: var(--color-alert-bg);
}

.system-card--alert::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: var(--color-alert);
  border-radius: 0.75rem 0 0 0.75rem;
}
```

### Loading Card Variant
```css
.system-card--loading {
  position: relative;
  overflow: hidden;
}

.system-card--loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}
```

## Animation System

```css
/* Alert Pulse Animation */
@keyframes alert-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.alert-badge--pulse {
  animation: alert-pulse 2s ease-in-out infinite;
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .alert-badge--pulse {
    animation: none;
  }
  
  .system-card {
    transition: none;
  }
}

/* Focus Animations */
.focus-ring {
  transition: box-shadow 0.15s ease-in-out;
}

.focus-ring:focus {
  box-shadow: 0 0 0 3px var(--card-focus-ring);
}
```

## Responsive Design System

```css
/* Breakpoints */
:root {
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
}

/* Grid System */
.system-grid {
  display: grid;
  gap: var(--spacing-md);
  grid-template-columns: repeat(2, 1fr);
}

@media (max-width: 768px) {
  .system-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }
  
  .system-card {
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .system-card {
    padding: var(--spacing-sm);
  }
  
  .card-text-title {
    font-size: 1rem;
  }
}
```

## Accessibility Features

```css
/* High Contrast Mode */
@media (prefers-contrast: high) {
  .system-card {
    border-width: 2px;
    border-color: var(--text-primary);
  }
  
  .alert-badge {
    border: 2px solid var(--color-alert);
  }
}

/* Focus Indicators */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Color Blind Support */
.alert-indicator::before {
  content: '⚠️';
  margin-right: var(--spacing-xs);
}
```

## File Structure

```
src/styles/themes/cardTheme/
├── index.ts
├── colors.css
├── typography.css
├── spacing.css
├── components.css
├── animations.css
├── responsive.css
├── accessibility.css
└── variants/
    ├── alert.css
    ├── loading.css
    └── interactive.css
```

## Implementation Requirements

### CSS Custom Properties
- **Theme Switching**: Support for dynamic theme changes
- **Component Isolation**: Scoped variables for component-specific styling
- **Performance**: Minimal CSS bundle size with tree-shaking
- **Maintainability**: Clear naming conventions and organization

### Tailwind Integration
```javascript
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        'card-primary': 'var(--card-bg-primary)',
        'card-secondary': 'var(--card-bg-secondary)',
        'text-primary': 'var(--text-primary)',
        'alert': 'var(--color-alert)'
      },
      spacing: {
        'card': 'var(--card-padding)',
        'metric': 'var(--metric-gap)'
      }
    }
  }
};
```

## Testing Requirements

- **Visual Regression Tests**: Screenshot comparisons for all variants
- **Accessibility Tests**: Color contrast, focus indicators, screen readers
- **Theme Tests**: Light/dark mode switching functionality
- **Responsive Tests**: Layout behavior at different screen sizes
- **Animation Tests**: Reduced motion preferences, performance

## Success Criteria

- [ ] Complete color system with light and dark theme support
- [ ] Typography scale with consistent hierarchy
- [ ] Spacing system with logical progression
- [ ] Component variants for all states (default, hover, focus, alert)
- [ ] Responsive design system with mobile-first approach
- [ ] Accessibility features meeting WCAG AA standards
- [ ] Animation system with reduced motion support
- [ ] Performance optimized CSS with minimal bundle size
- [ ] Integration with Tailwind CSS utility classes
- [ ] Comprehensive documentation and usage examples

## Usage Examples

```css
/* Using theme variables */
.custom-card {
  background-color: var(--card-bg-primary);
  color: var(--text-primary);
  padding: var(--card-padding);
}

/* Using utility classes */
.system-card {
  @apply bg-card-primary text-text-primary p-card;
  @apply border border-card-border rounded-xl;
  @apply shadow-card hover:shadow-lg transition-all;
}
```

## Integration Notes

- **Component Integration**: All system components will use these theme tokens
- **Build Process**: CSS variables are processed during build for optimization
- **Runtime Switching**: Theme switching works without page reload
- **Fallback Support**: Graceful degradation for older browsers

## Future Enhancements

- **Custom Themes**: Support for user-defined color schemes
- **Brand Themes**: Multiple brand variations (corporate, government, etc.)
- **Advanced Animations**: Micro-interactions and state transitions
- **CSS-in-JS**: Optional styled-components integration