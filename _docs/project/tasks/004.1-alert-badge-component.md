# Task 004.1: AlertBadge Component

## Task Overview

**Component**: AlertBadge (Micro Component)
**Priority**: High
**Developer Assignment**: Developer
**Dependencies**: None (foundational component)
**Location**: `src/components/ui/AlertBadge/`

## Component Description

Create a standardized alert emphasis component for numeric values that provides consistent alert styling across all system cards. This is a foundational micro-component that will be used by other components in the right drawer system.

## UI Drawing

```
┌─────────────────────────────────────┐
│ AlertBadge Component States         │
├─────────────────────────────────────┤
│                                     │
│ Normal State:                       │
│ ┌─────────┐                         │
│ │   42    │ (gray text)             │
│ └─────────┘                         │
│                                     │
│ Alert State:                        │
│ ┌─────────┐                         │
│ │   42    │ (red text + pulse)      │
│ └─────────┘                         │
│                                     │
│ Large Alert State:                  │
│ ┌───────────┐                       │
│ │    156    │ (red text + pulse)    │
│ └───────────┘                       │
│                                     │
└─────────────────────────────────────┘
```

## Props Structure

```typescript
interface AlertBadgeProps {
  value: number | string;
  isAlert?: boolean;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}
```

## Responsibilities

- **Alert Styling**: Applies red color and pulse animation when `isAlert` is true
- **Accessibility**: Maintains WCAG compliant color contrasts for color-blind users
- **Animation**: Subtle pulse effect for critical alerts without being distracting
- **Flexibility**: Supports different sizes and custom styling through className
- **Performance**: Lightweight component with minimal re-renders

## Implementation Requirements

### Styling Features
- **Normal State**: Standard text color (gray-600 in light mode, gray-300 in dark mode)
- **Alert State**: Red color (red-500) with subtle pulse animation
- **Size Variants**: Small (text-sm), Medium (text-base), Large (text-lg)
- **Dark Mode**: Automatic theme switching support

### Animation Specifications
- **Pulse Effect**: Gentle opacity animation (0.7 to 1.0) with 2s duration
- **Performance**: CSS-based animations, no JavaScript timers
- **Accessibility**: Respects `prefers-reduced-motion` for users who need it

### Accessibility Requirements
- **Color Contrast**: Minimum 4.5:1 ratio for normal text, 3:1 for large text
- **Screen Readers**: Proper ARIA labels for alert states
- **Focus States**: Clear focus indicators for keyboard navigation
- **Motion Sensitivity**: Respects user's motion preferences

## File Structure

```
src/components/ui/AlertBadge/
├── index.ts
├── AlertBadge.tsx
├── AlertBadge.test.tsx
└── AlertBadge.stories.tsx
```

## Testing Requirements

- **Unit Tests**: Props handling, styling states, accessibility
- **Visual Tests**: Storybook stories for all states and sizes
- **Accessibility Tests**: Screen reader compatibility, keyboard navigation
- **Animation Tests**: Pulse effect behavior, reduced motion support

## Success Criteria

- [ ] Component renders correctly in normal and alert states
- [ ] Pulse animation works smoothly without performance issues
- [ ] Supports all size variants (sm, md, lg)
- [ ] Passes accessibility audits (WCAG AA compliance)
- [ ] Works in both light and dark themes
- [ ] Respects user's motion preferences
- [ ] Complete test coverage (unit + visual + accessibility)
- [ ] Storybook documentation with all variants

## Usage Example

```tsx
// Normal metric display
<AlertBadge value={42} size="md" />

// Critical alert with animation
<AlertBadge value={5} isAlert={true} size="lg" />

// Custom styling
<AlertBadge 
  value="156" 
  isAlert={true} 
  className="font-bold" 
/>
```

## Notes

- This component is foundational and will be used by MetricDisplay and SystemCard components
- Keep implementation lightweight as it may be rendered many times on the dashboard
- Ensure consistent behavior across different browsers and devices
- Consider future extensibility for additional alert types (warning, success)