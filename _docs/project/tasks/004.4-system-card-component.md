# Task 004.4: SystemCard Component

## Task Overview

**Component**: SystemCard (Base Component)
**Priority**: High
**Developer Assignment**: Developer
**Dependencies**:

- AlertBadge (Task 004.1)
- MetricDisplay (Task 004.2)
- useSystemStore (Task 004.3)
  **Location**: `src/components/features/alert-dashboard/layout/SystemCard/`

## Component Description

Create a generic, reusable card component that can display any system type (Fire Alarm, Access Control, CCTV, Gate Barriers) through props configuration. This component serves as the foundation for all system cards in the right drawer.

## UI Drawing

```
┌─────────────────────────────────────────────────────────────┐
│ SystemCard Component Layout                                 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Fire Alarm System Card:                                     │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ┌─────┐ Fire Alarm System                               │ │
│ │ │ 🔥  │                                                 │ │
│ │ └─────┘                                                 │ │
│ │                                                         │ │
│ │ Total Devices                                      42   │ │
│ │ Active Alarms                                       5   │ │
│ │                                                (red+pulse)│ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Access Control System Card:                                 │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ┌─────┐ Access Control                                  │ │
│ │ │ 🚪  │                                                 │ │
│ │ └─────┘                                                 │ │
│ │                                                         │ │
│ │ Total Doors                                        28   │ │
│ │ Open                                               12   │ │
│ │ Closed                                             16   │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ CCTV System Card:                                           │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ┌─────┐ CCTV System                                     │ │
│ │ │ 📹  │                                                 │ │
│ │ └─────┘                                                 │ │
│ │                                                         │ │
│ │ Total Cameras                                     156   │ │
│ │ Active Incidents                                    2   │ │
│ │                                                (red+pulse)│ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Gate Barriers System Card:                                  │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ┌─────┐ Gate Barriers                                   │ │
│ │ │ 🚧  │                                                 │ │
│ │ └─────┘                                                 │ │
│ │                                                         │ │
│ │ Total Barriers                                      8   │ │
│ │ Unauthorized Attempts                               3   │ │
│ │                                                (red+pulse)│ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## Props Structure

```typescript
interface SystemMetric {
  label: string;
  value: number | string;
  isAlert?: boolean;
  highlight?: boolean;
}

interface SystemCardProps {
  title: string;
  icon: React.ComponentType<{ className?: string }> | string; // SvgIcon component
  primaryMetric: SystemMetric;
  secondaryMetrics: SystemMetric[];
  isLoading?: boolean;
  className?: string;
  onCardClick?: () => void;
  testId?: string;
}
```

## System Configuration Examples

```typescript
// Fire Alarm System Configuration
const fireAlarmConfig = {
  title: "Fire Alarm System",
  icon: FireIcon, // or "🔥"
  primaryMetric: {
    label: "Total Devices",
    value: 42
  },
  secondaryMetrics: [
    {
      label: "Active Alarms",
      value: 5,
      isAlert: true
    }
  ]
};

// Access Control System Configuration
const accessControlConfig = {
  title: "Access Control",
  icon: DoorIcon, // or "🚪"
  primaryMetric: {
    label: "Total Doors",
    value: 28
  },
  secondaryMetrics: [
    {
      label: "Open",
      value: 12,
      highlight: true
    },
    {
      label: "Closed",
      value: 16
    }
  ]
};
```

## Responsibilities

- **Generic Layout**: Provides consistent card structure for any system type
- **Icon Management**: Supports both React components and emoji/string icons
- **Metric Display**: Uses MetricDisplay components for consistent formatting
- **Alert Integration**: Integrates AlertBadge for critical values
- **Loading States**: Shows skeleton/spinner during data loading
- **Interaction**: Supports click handlers for navigation or expansion
- **Accessibility**: Proper ARIA labels and keyboard navigation

## Implementation Requirements

### Layout Structure

- **Header Section**: Icon + title with proper spacing and alignment
- **Primary Metric**: Prominent display of main system count
- **Secondary Metrics**: List of additional metrics using MetricDisplay
- **Loading State**: Skeleton placeholders during data fetch
- **Error State**: Fallback display when data is unavailable

### Styling Specifications

- **Card Container**: Rounded corners, shadow, padding, background
- **Header Layout**: Flex layout with icon and title alignment
- **Metric Spacing**: Consistent gaps between metric items
- **Hover Effects**: Subtle hover states for interactive cards
- **Focus States**: Clear focus indicators for keyboard navigation

### Icon Handling

- **React Components**: Support for Heroicons, Lucide, or custom icons
- **String Icons**: Support for emoji or text-based icons
- **Icon Sizing**: Consistent icon dimensions (24x24px default)
- **Icon Colors**: Theme-aware icon coloring

### State Management

- **Loading State**: Skeleton animation while fetching data
- **Error State**: Error message with retry option
- **Empty State**: Placeholder when no data is available
- **Interactive State**: Visual feedback for clickable cards

## File Structure

```
src/components/features/dashboard/layout/SystemCard/
├── index.ts
├── SystemCard.tsx
├── SystemCard.test.tsx
├── SystemCard.stories.tsx
├── components/
│   ├── SystemCardHeader.tsx
│   ├── SystemCardMetrics.tsx
│   └── SystemCardSkeleton.tsx
└── hooks/
    └── useSystemCardData.ts
```

## Testing Requirements

- **Unit Tests**: Props handling, metric display, icon rendering
- **Integration Tests**: MetricDisplay and AlertBadge integration
- **Visual Tests**: Storybook stories for all system types
- **Accessibility Tests**: Screen reader compatibility, keyboard navigation
- **Interaction Tests**: Click handlers, hover states, focus management
- **Loading Tests**: Skeleton states, error handling

## Success Criteria

- [ ] Component renders all four system types correctly
- [ ] Proper integration with MetricDisplay and AlertBadge components
- [ ] Supports both React component and string icons
- [ ] Loading and error states work properly
- [ ] Alert highlighting works for critical metrics
- [ ] Responsive design adapts to different screen sizes
- [ ] Accessibility standards met (WCAG AA compliance)
- [ ] Click interactions work when onCardClick is provided
- [ ] Complete test coverage including edge cases
- [ ] Storybook documentation with all system configurations

## Usage Examples

```tsx
// Fire Alarm System Card
<SystemCard
  title="Fire Alarm System"
  icon={FireIcon}
  primaryMetric={{
    label: "Total Devices",
    value: fireAlarmData.totalDevices
  }}
  secondaryMetrics={[
    {
      label: "Active Alarms",
      value: fireAlarmData.activeAlarms,
      isAlert: fireAlarmData.activeAlarms > 0
    }
  ]}
  isLoading={fireAlarmData.isLoading}
  onCardClick={() => navigate('/fire-alarm')}
/>

// Access Control System Card
<SystemCard
  title="Access Control"
  icon="🚪"
  primaryMetric={{
    label: "Total Doors",
    value: accessControlData.totalDoors
  }}
  secondaryMetrics={[
    {
      label: "Open",
      value: accessControlData.open,
      highlight: true
    },
    {
      label: "Closed",
      value: accessControlData.closed
    }
  ]}
  isLoading={accessControlData.isLoading}
/>
```

## Integration Notes

- **MetricDisplay Integration**: Uses MetricDisplay for all metric presentations
- **AlertBadge Integration**: Passes isAlert prop to MetricDisplay for alert handling
- **Store Integration**: Will be connected to useSystemStore in parent components
- **Theme Integration**: Follows design system for consistent styling

## Performance Considerations

- **Memoization**: React.memo for preventing unnecessary re-renders
- **Icon Optimization**: Lazy loading for React component icons
- **Metric Updates**: Efficient updates when only specific metrics change
- **Animation Performance**: CSS-based animations for smooth interactions

## Future Enhancements

- **Chart Integration**: Support for mini charts or sparklines
- **Status Indicators**: Visual health indicators (green/yellow/red)
- **Expandable Cards**: Collapsible sections for additional details
- **Custom Actions**: Support for action buttons or dropdown menus
