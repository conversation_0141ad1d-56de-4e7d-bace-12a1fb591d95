# Task 005: 2D Floor  Plan Viewer Component

## Task Overview

**Component Name**: FloorPlanViewer
**Priority**: High
**Dependencies**: DashboardLayout (Task 001)
**Location**: `src/components/features/dashboard/layout/FloorPlanViewer.tsx`

## Component Description

Create the main content body component that displays a 2D floor plan image with full scrollable functionality (both vertical and horizontal). This component serves as the primary workspace where users interact with building floor plans, view device locations, and manage incidents. The component should handle large floor plan images efficiently and provide smooth scrolling experience.

## Design Reference

Based on the dashboard layout documentation and screenshot, the content body should:

- Fill the remaining space after sidebar, topbar, and drawer
- Display 2D floor plan images with zoom and pan capabilities
- Show interactive elements (devices, sensors, alerts) overlaid on the plan
- Support both vertical and horizontal scrolling for large floor plans
- Handle responsive behavior for different screen sizes
- Use dark theme background (#1a1a1a)

## Technical Specifications

### Component Interface

```typescript
interface FloorPlanViewerProps {
  className?: string;
  floorPlanUrl?: string;
  floorPlanAlt?: string;
  devices?: DeviceMarker[];
  alerts?: AlertMarker[];
  selectedFloor?: string;
  selectedBuilding?: string;
  onDeviceClick?: (deviceId: string) => void;
  onAlertClick?: (alertId: string) => void;
  onPlanClick?: (coordinates: { x: number; y: number }) => void;
  zoom?: number;
  onZoomChange?: (zoom: number) => void;
  panPosition?: { x: number; y: number };
  onPanChange?: (position: { x: number; y: number }) => void;
  isLoading?: boolean;
  error?: string;
}

// Device marker structure
interface DeviceMarker {
  id: string;
  type: 'fire-alarm' | 'access-control' | 'cctv' | 'gate-barrier';
  position: { x: number; y: number }; // Percentage-based positioning
  status: 'normal' | 'warning' | 'danger' | 'offline';
  label?: string;
  metadata?: Record<string, any>;
}

// Alert marker structure
interface AlertMarker {
  id: string;
  type: 'fire' | 'security' | 'access' | 'system';
  position: { x: number; y: number };
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description?: string;
  timestamp: Date;
  acknowledged?: boolean;
}
```

### Component Structure

```tsx
function FloorPlanViewer({
  className,
  floorPlanUrl,
  floorPlanAlt = 'Floor plan',
  devices = [],
  alerts = [],
  selectedFloor,
  selectedBuilding,
  onDeviceClick,
  onAlertClick,
  onPlanClick,
  zoom = 1,
  onZoomChange,
  panPosition = { x: 0, y: 0 },
  onPanChange,
  isLoading = false,
  error
}: FloorPlanViewerProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });

  return (
    <main 
      className={`content-body ${className || ''}`}
      role="main"
      aria-label="Floor plan viewer"
    >
      {/* Loading State */}
      {isLoading && (
        <div className="loading-overlay">
          <div className="loading-spinner" />
          <span className="loading-text">Loading floor plan...</span>
        </div>
      )}
  
      {/* Error State */}
      {error && (
        <div className="error-overlay">
          <div className="error-content">
            <span className="error-icon">⚠️</span>
            <h3 className="error-title">Failed to load floor plan</h3>
            <p className="error-message">{error}</p>
            <button className="retry-button" onClick={() => window.location.reload()}>
              Retry
            </button>
          </div>
        </div>
      )}
  
      {/* Main Content */}
      {!isLoading && !error && (
        <>
          {/* Zoom Controls */}
          <div className="zoom-controls">
            <button 
              className="zoom-button zoom-in"
              onClick={() => onZoomChange?.(Math.min(zoom * 1.2, 3))}
              aria-label="Zoom in"
              disabled={zoom >= 3}
            >
              +
            </button>
            <span className="zoom-level">{Math.round(zoom * 100)}%</span>
            <button 
              className="zoom-button zoom-out"
              onClick={() => onZoomChange?.(Math.max(zoom / 1.2, 0.5))}
              aria-label="Zoom out"
              disabled={zoom <= 0.5}
            >
              −
            </button>
            <button 
              className="zoom-button zoom-reset"
              onClick={() => {
                onZoomChange?.(1);
                onPanChange?.({ x: 0, y: 0 });
              }}
              aria-label="Reset zoom"
            >
              ⌂
            </button>
          </div>
      
          {/* Floor Plan Container */}
          <div 
            ref={containerRef}
            className={`plan-container ${isDragging ? 'dragging' : ''}`}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
            onWheel={handleWheel}
            onClick={handlePlanClick}
          >
            {/* Floor Plan Image */}
            <div 
              className="plan-wrapper"
              style={{
                transform: `translate(${panPosition.x}px, ${panPosition.y}px) scale(${zoom})`,
                transformOrigin: '0 0'
              }}
            >
              {floorPlanUrl ? (
                <img
                  ref={imageRef}
                  src={floorPlanUrl}
                  alt={floorPlanAlt}
                  className="floor-plan-image"
                  onLoad={handleImageLoad}
                  onError={handleImageError}
                  draggable={false}
                />
              ) : (
                <div className="placeholder-plan">
                  <div className="placeholder-content">
                    <span className="placeholder-icon">🏢</span>
                    <h3 className="placeholder-title">No Floor Plan Available</h3>
                    <p className="placeholder-text">
                      Select a building and floor to view the floor plan
                    </p>
                  </div>
                </div>
              )}
          
              {/* Device Markers */}
              {imageLoaded && devices.map(device => (
                <div
                  key={device.id}
                  className={`device-marker device-${device.type} status-${device.status}`}
                  style={{
                    left: `${device.position.x}%`,
                    top: `${device.position.y}%`
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeviceClick?.(device.id);
                  }}
                  title={device.label || `${device.type} device`}
                  role="button"
                  tabIndex={0}
                  aria-label={`${device.type} device: ${device.label || device.id}`}
                >
                  <div className="marker-icon">
                    {getDeviceIcon(device.type)}
                  </div>
                  {device.status !== 'normal' && (
                    <div className="marker-status" />
                  )}
                </div>
              ))}
          
              {/* Alert Markers */}
              {imageLoaded && alerts.map(alert => (
                <div
                  key={alert.id}
                  className={`alert-marker severity-${alert.severity} ${alert.acknowledged ? 'acknowledged' : ''}`}
                  style={{
                    left: `${alert.position.x}%`,
                    top: `${alert.position.y}%`
                  }}
                  onClick={(e) => {
                    e.stopPropagation();
                    onAlertClick?.(alert.id);
                  }}
                  title={alert.title}
                  role="button"
                  tabIndex={0}
                  aria-label={`${alert.severity} alert: ${alert.title}`}
                >
                  <div className="alert-icon">
                    {getAlertIcon(alert.type)}
                  </div>
                  <div className="alert-pulse" />
                </div>
              ))}
            </div>
          </div>
      
          {/* Floor Plan Info */}
          <div className="plan-info">
            <div className="plan-details">
              {selectedBuilding && (
                <span className="building-info">
                  {selectedBuilding}
                  {selectedFloor && ` - ${selectedFloor}`}
                </span>
              )}
            </div>
            <div className="plan-stats">
              <span className="device-count">
                {devices.length} devices
              </span>
              {alerts.length > 0 && (
                <span className="alert-count">
                  {alerts.length} active alerts
                </span>
              )}
            </div>
          </div>
        </>
      )}
    </main>
  );
}

// Helper functions
function getDeviceIcon(type: string): string {
  const icons = {
    'fire-alarm': '🔥',
    'access-control': '🚪',
    'cctv': '📹',
    'gate-barrier': '🚧'
  };
  return icons[type] || '📍';
}

function getAlertIcon(type: string): string {
  const icons = {
    'fire': '🔥',
    'security': '🚨',
    'access': '🔒',
    'system': '⚠️'
  };
  return icons[type] || '⚠️';
}
```

## CSS Styling Requirements

### Base Styles

```css
/* Main Content Body */
.content-body {
  flex: 1;
  background-color: #1a1a1a;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* Loading State */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #1a1a1a;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #333;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #888;
  font-size: 14px;
}

/* Error State */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.error-content {
  text-align: center;
  max-width: 400px;
  padding: 40px;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
  display: block;
}

.error-title {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.error-message {
  color: #888;
  font-size: 14px;
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.retry-button {
  background-color: #007bff;
  color: #fff;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background-color: #0056b3;
}

/* Zoom Controls */
.zoom-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  background-color: rgba(42, 42, 42, 0.9);
  border-radius: 8px;
  padding: 8px;
  gap: 4px;
  z-index: 5;
  backdrop-filter: blur(8px);
}

.zoom-button {
  width: 32px;
  height: 32px;
  background-color: #333;
  border: none;
  border-radius: 4px;
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.zoom-button:hover:not(:disabled) {
  background-color: #444;
}

.zoom-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.zoom-level {
  color: #fff;
  font-size: 12px;
  font-weight: 500;
  padding: 0 8px;
  min-width: 40px;
  text-align: center;
}

/* Plan Container */
.plan-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  cursor: grab;
  background-color: #1a1a1a;
  background-image: 
    radial-gradient(circle, #333 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

.plan-container.dragging {
  cursor: grabbing;
  user-select: none;
}

.plan-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  transition: transform 0.1s ease-out;
  transform-origin: 0 0;
}

/* Floor Plan Image */
.floor-plan-image {
  display: block;
  max-width: none;
  height: auto;
  user-select: none;
  pointer-events: none;
}

/* Placeholder Plan */
.placeholder-plan {
  width: 800px;
  height: 600px;
  background-color: #2a2a2a;
  border: 2px dashed #444;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.placeholder-content {
  text-align: center;
  padding: 40px;
}

.placeholder-icon {
  font-size: 64px;
  margin-bottom: 20px;
  display: block;
  opacity: 0.5;
}

.placeholder-title {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 12px 0;
}

.placeholder-text {
  color: #888;
  font-size: 14px;
  margin: 0;
  line-height: 1.5;
}

/* Device Markers */
.device-marker {
  position: absolute;
  width: 24px;
  height: 24px;
  transform: translate(-50%, -50%);
  cursor: pointer;
  z-index: 3;
  transition: all 0.2s ease;
}

.device-marker:hover {
  transform: translate(-50%, -50%) scale(1.2);
  z-index: 4;
}

.marker-icon {
  width: 100%;
  height: 100%;
  background-color: #333;
  border: 2px solid #555;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  position: relative;
}

/* Device Status Colors */
.device-marker.status-normal .marker-icon {
  border-color: #28a745;
  background-color: rgba(40, 167, 69, 0.2);
}

.device-marker.status-warning .marker-icon {
  border-color: #ffc107;
  background-color: rgba(255, 193, 7, 0.2);
}

.device-marker.status-danger .marker-icon {
  border-color: #dc3545;
  background-color: rgba(220, 53, 69, 0.2);
}

.device-marker.status-offline .marker-icon {
  border-color: #6c757d;
  background-color: rgba(108, 117, 125, 0.2);
}

.marker-status {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #dc3545;
  border: 1px solid #1a1a1a;
}

/* Alert Markers */
.alert-marker {
  position: absolute;
  width: 32px;
  height: 32px;
  transform: translate(-50%, -50%);
  cursor: pointer;
  z-index: 4;
  transition: all 0.2s ease;
}

.alert-marker:hover {
  transform: translate(-50%, -50%) scale(1.1);
  z-index: 5;
}

.alert-icon {
  width: 100%;
  height: 100%;
  background-color: #dc3545;
  border: 2px solid #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #fff;
  position: relative;
  z-index: 2;
}

/* Alert Severity Colors */
.alert-marker.severity-low .alert-icon {
  background-color: #17a2b8;
}

.alert-marker.severity-medium .alert-icon {
  background-color: #ffc107;
  color: #000;
}

.alert-marker.severity-high .alert-icon {
  background-color: #fd7e14;
}

.alert-marker.severity-critical .alert-icon {
  background-color: #dc3545;
}

.alert-marker.acknowledged .alert-icon {
  opacity: 0.6;
}

.alert-pulse {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background-color: inherit;
  opacity: 0.6;
  animation: pulse 2s infinite;
  z-index: 1;
}

.alert-marker.acknowledged .alert-pulse {
  display: none;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.5);
    opacity: 0.3;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Plan Info */
.plan-info {
  position: absolute;
  bottom: 20px;
  left: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  background-color: rgba(42, 42, 42, 0.9);
  border-radius: 8px;
  padding: 12px 16px;
  backdrop-filter: blur(8px);
  z-index: 5;
}

.plan-details {
  display: flex;
  align-items: center;
  gap: 12px;
}

.building-info {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
}

.plan-stats {
  display: flex;
  align-items: center;
  gap: 16px;
}

.device-count,
.alert-count {
  color: #888;
  font-size: 12px;
}

.alert-count {
  color: #dc3545;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .zoom-controls {
    top: 10px;
    right: 10px;
    padding: 6px;
  }
  
  .zoom-button {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
  
  .plan-info {
    bottom: 10px;
    left: 10px;
    padding: 8px 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .device-marker {
    width: 20px;
    height: 20px;
  }
  
  .alert-marker {
    width: 28px;
    height: 28px;
  }
}
```

## Implementation Requirements

### 1. Pan and Zoom Functionality

```typescript
// Mouse/touch handling for pan
const handleMouseDown = useCallback((e: React.MouseEvent) => {
  if (e.button === 0) { // Left mouse button
    setIsDragging(true);
    setDragStart({
      x: e.clientX - panPosition.x,
      y: e.clientY - panPosition.y
    });
  }
}, [panPosition]);

const handleMouseMove = useCallback((e: React.MouseEvent) => {
  if (isDragging) {
    const newPosition = {
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y
    };
    onPanChange?.(newPosition);
  }
}, [isDragging, dragStart, onPanChange]);

const handleMouseUp = useCallback(() => {
  setIsDragging(false);
}, []);

// Wheel zoom handling
const handleWheel = useCallback((e: React.WheelEvent) => {
  e.preventDefault();
  const delta = e.deltaY > 0 ? 0.9 : 1.1;
  const newZoom = Math.max(0.5, Math.min(3, zoom * delta));
  onZoomChange?.(newZoom);
}, [zoom, onZoomChange]);
```

### 2. Image Loading and Error Handling

```typescript
const handleImageLoad = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
  const img = e.currentTarget;
  setImageDimensions({
    width: img.naturalWidth,
    height: img.naturalHeight
  });
  setImageLoaded(true);
}, []);

const handleImageError = useCallback(() => {
  setImageLoaded(false);
  // Could trigger error state or fallback
}, []);
```

### 3. Coordinate System

```typescript
// Convert screen coordinates to plan coordinates
const screenToPlanCoordinates = useCallback((screenX: number, screenY: number) => {
  if (!containerRef.current || !imageRef.current) return { x: 0, y: 0 };
  
  const containerRect = containerRef.current.getBoundingClientRect();
  const relativeX = screenX - containerRect.left - panPosition.x;
  const relativeY = screenY - containerRect.top - panPosition.y;
  
  const planX = (relativeX / zoom / imageDimensions.width) * 100;
  const planY = (relativeY / zoom / imageDimensions.height) * 100;
  
  return { x: planX, y: planY };
}, [panPosition, zoom, imageDimensions]);

const handlePlanClick = useCallback((e: React.MouseEvent) => {
  if (!isDragging && imageLoaded) {
    const coordinates = screenToPlanCoordinates(e.clientX, e.clientY);
    onPlanClick?.(coordinates);
  }
}, [isDragging, imageLoaded, screenToPlanCoordinates, onPlanClick]);
```

### 4. Keyboard Navigation

```typescript
useEffect(() => {
  const handleKeyDown = (e: KeyboardEvent) => {
    if (!containerRef.current?.contains(document.activeElement)) return;
  
    switch (e.key) {
      case 'ArrowUp':
        e.preventDefault();
        onPanChange?.({ ...panPosition, y: panPosition.y + 20 });
        break;
      case 'ArrowDown':
        e.preventDefault();
        onPanChange?.({ ...panPosition, y: panPosition.y - 20 });
        break;
      case 'ArrowLeft':
        e.preventDefault();
        onPanChange?.({ ...panPosition, x: panPosition.x + 20 });
        break;
      case 'ArrowRight':
        e.preventDefault();
        onPanChange?.({ ...panPosition, x: panPosition.x - 20 });
        break;
      case '+':
      case '=':
        e.preventDefault();
        onZoomChange?.(Math.min(zoom * 1.2, 3));
        break;
      case '-':
        e.preventDefault();
        onZoomChange?.(Math.max(zoom / 1.2, 0.5));
        break;
      case '0':
        e.preventDefault();
        onZoomChange?.(1);
        onPanChange?.({ x: 0, y: 0 });
        break;
    }
  };
  
  document.addEventListener('keydown', handleKeyDown);
  return () => document.removeEventListener('keydown', handleKeyDown);
}, [panPosition, zoom, onPanChange, onZoomChange]);
```

## Integration with DashboardLayout

```tsx
// How it will be used in DashboardLayout
import { FloorPlanViewer } from './FloorPlanViewer';

function DashboardLayout({ ... }) {
  const [floorPlan, setFloorPlan] = useState<string>('');
  const [zoom, setZoom] = useState(1);
  const [panPosition, setPanPosition] = useState({ x: 0, y: 0 });
  
  return (
    <div className="dashboard-layout">
      {/* Other components */}
      <FloorPlanViewer 
        floorPlanUrl={floorPlan}
        devices={deviceMarkers}
        alerts={alertMarkers}
        selectedFloor={selectedFloor}
        selectedBuilding={selectedBuilding}
        onDeviceClick={handleDeviceClick}
        onAlertClick={handleAlertClick}
        onPlanClick={handlePlanClick}
        zoom={zoom}
        onZoomChange={setZoom}
        panPosition={panPosition}
        onPanChange={setPanPosition}
        isLoading={isLoadingPlan}
        error={planError}
      />
    </div>
  );
}
```

## Testing Requirements

### 1. Unit Tests

- Component renders without errors
- Image loading and error states work
- Zoom and pan functionality works
- Device and alert markers render correctly
- Click handlers are called properly

### 2. Integration Tests

- Works correctly within DashboardLayout
- Responsive behavior functions properly
- Keyboard navigation works
- Performance with large images

### 3. Performance Tests

- Large floor plan images load efficiently
- Smooth scrolling and zooming
- Many device markers don't impact performance
- Memory usage is reasonable

## Acceptance Criteria

- [ ] Component renders floor plan images correctly
- [ ] Smooth pan and zoom functionality works
- [ ] Device markers display at correct positions
- [ ] Alert markers show with proper animations
- [ ] Loading and error states are handled
- [ ] Zoom controls function properly
- [ ] Keyboard navigation works
- [ ] Click events are handled correctly
- [ ] Responsive design works on all devices
- [ ] Performance is smooth with large images
- [ ] Accessibility features are complete
- [ ] Component is properly typed with TypeScript
- [ ] CSS follows naming conventions
- [ ] Component integrates with DashboardLayout

## File Structure Output

```
src/components/features/dashboard/layout/
├── FloorPlanViewer.tsx              # Main component
├── FloorPlanViewer.module.css       # Component styles
└── index.ts                     # Updated exports
```

## Usage Example

```tsx
// Standalone usage (for testing)
import { FloorPlanViewer } from '@/components/features/dashboard/layout';

function TestPage() {
  const [zoom, setZoom] = useState(1);
  const [panPosition, setPanPosition] = useState({ x: 0, y: 0 });
  
  const mockDevices = [
    {
      id: '1',
      type: 'fire-alarm' as const,
      position: { x: 25, y: 30 },
      status: 'normal' as const,
      label: 'Fire Detector 001'
    },
    {
      id: '2',
      type: 'cctv' as const,
      position: { x: 75, y: 20 },
      status: 'warning' as const,
      label: 'Camera 001'
    }
  ];
  
  const mockAlerts = [
    {
      id: '1',
      type: 'fire' as const,
      position: { x: 50, y: 60 },
      severity: 'high' as const,
      title: 'Fire alarm activated',
      timestamp: new Date()
    }
  ];
  
  return (
    <div style={{ height: '100vh', display: 'flex' }}>
      <FloorPlanViewer 
        floorPlanUrl="/images/floor-plan-sample.png"
        devices={mockDevices}
        alerts={mockAlerts}
        selectedFloor="Floor 1"
        selectedBuilding="Building A"
        onDeviceClick={(id) => console.log('Device clicked:', id)}
        onAlertClick={(id) => console.log('Alert clicked:', id)}
        onPlanClick={(coords) => console.log('Plan clicked:', coords)}
        zoom={zoom}
        onZoomChange={setZoom}
        panPosition={panPosition}
        onPanChange={setPanPosition}
      />
    </div>
  );
}
```

## Notes for Developer

1. **Performance Focus**: Optimize for large floor plan images and many markers
2. **Smooth Interactions**: Ensure pan and zoom feel responsive and natural
3. **Accessibility**: Implement proper keyboard navigation and screen reader support
4. **Mobile Experience**: Touch gestures should work intuitively
5. **Coordinate System**: Use percentage-based positioning for device markers
6. **Error Handling**: Gracefully handle image loading failures
7. **Memory Management**: Clean up event listeners and avoid memory leaks
8. **Future Extensibility**: Structure should support additional marker types

## Dependencies

- React 18+
- TypeScript
- CSS Modules or Tailwind CSS
- DashboardLayout component (Task 001)

---

**Estimated Development Time**: 2-3 days
**Review Required**: Yes, coordinate with layout and interaction developers
**Documentation**: Update component exports and README
