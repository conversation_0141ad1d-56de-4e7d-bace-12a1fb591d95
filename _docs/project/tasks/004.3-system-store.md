# Task 004.3: System Store (Zustand)

## Task Overview

**Component**: useSystemStore (State Management)
**Priority**: High
**Developer Assignment**: Developer
**Dependencies**: None (foundational store)
**Location**: `src/stores/systemStore.ts`

## Store Description

Create a centralized Zustand store for managing all system metrics data across the dashboard. This store will serve as the single source of truth for Fire Alarm, Access Control, CCTV, and Gate Barrier system data with support for real-time updates.

## Data Architecture Diagram

```
┌─────────────────────────────────────────────────────────────┐
│ useSystemStore - Central State Management                   │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ ┌─────────────────┐  ┌─────────────────┐                   │
│ │   Fire Alarm    │  │ Access Control  │                   │
│ │ ┌─────────────┐ │  │ ┌─────────────┐ │                   │
│ │ │totalDevices │ │  │ │ totalDoors  │ │                   │
│ │ │     42      │ │  │ │     28      │ │                   │
│ │ └─────────────┘ │  │ └─────────────┘ │                   │
│ │ ┌─────────────┐ │  │ ┌─────────────┐ │                   │
│ │ │activeAlarms │ │  │ │    open     │ │                   │
│ │ │      5      │ │  │ │     12      │ │                   │
│ │ └─────────────┘ │  │ └─────────────┘ │                   │
│ └─────────────────┘  │ ┌─────────────┐ │                   │
│                      │ │   closed    │ │                   │
│ ┌─────────────────┐  │ │     16      │ │                   │
│ │      CCTV       │  │ └─────────────┘ │                   │
│ │ ┌─────────────┐ │  └─────────────────┘                   │
│ │ │totalCameras │ │                                        │
│ │ │    156      │ │  ┌─────────────────┐                   │
│ │ └─────────────┘ │  │ Gate Barriers   │                   │
│ │ ┌─────────────┐ │  │ ┌─────────────┐ │                   │
│ │ │activeIncid. │ │  │ │totalBarriers│ │                   │
│ │ │      2      │ │  │ │      8      │ │                   │
│ │ └─────────────┘ │  │ └─────────────┘ │                   │
│ └─────────────────┘  │ ┌─────────────┐ │                   │
│                      │ │unauthorized │ │                   │
│                      │ │     3       │ │                   │
│                      │ └─────────────┘ │                   │
│                      └─────────────────┘                   │
│                                                             │
│ Actions: fetchSystemData, updateSystem, resetStore         │
│ Selectors: getAlertCount, getCriticalSystems               │
└─────────────────────────────────────────────────────────────┘
```

## Store Structure

```typescript
interface SystemState {
  // Fire Alarm System
  fireAlarm: {
    totalDevices: number;
    activeAlarms: number;
    lastUpdated: string;
    isLoading: boolean;
  };
  
  // Access Control System
  accessControl: {
    totalDoors: number;
    open: number;
    closed: number;
    lastUpdated: string;
    isLoading: boolean;
  };
  
  // CCTV System
  cctv: {
    totalCameras: number;
    activeIncidents: number;
    lastUpdated: string;
    isLoading: boolean;
  };
  
  // Gate Barriers System
  gateBarriers: {
    totalBarriers: number;
    unauthorizedAttempts: number;
    lastUpdated: string;
    isLoading: boolean;
  };
  
  // Global state
  isConnected: boolean;
  lastGlobalUpdate: string;
}

interface SystemActions {
  // Data fetching
  fetchSystemData: () => Promise<void>;
  fetchFireAlarmData: () => Promise<void>;
  fetchAccessControlData: () => Promise<void>;
  fetchCCTVData: () => Promise<void>;
  fetchGateBarriersData: () => Promise<void>;
  
  // Data updates
  updateFireAlarm: (data: Partial<SystemState['fireAlarm']>) => void;
  updateAccessControl: (data: Partial<SystemState['accessControl']>) => void;
  updateCCTV: (data: Partial<SystemState['cctv']>) => void;
  updateGateBarriers: (data: Partial<SystemState['gateBarriers']>) => void;
  
  // Utility actions
  resetStore: () => void;
  setConnectionStatus: (connected: boolean) => void;
}
```

## Key Features

### Real-time Updates
- **WebSocket Integration**: Support for real-time data updates
- **Polling Mechanism**: Fallback polling for systems without WebSocket support
- **Connection Management**: Automatic reconnection and error handling
- **Selective Updates**: Update only changed data to minimize re-renders

### Performance Optimization
- **Selective Subscriptions**: Components subscribe only to needed data slices
- **Shallow Comparison**: Prevents unnecessary re-renders with shallow equality
- **Batch Updates**: Groups multiple updates to reduce render cycles
- **Memoized Selectors**: Cached computed values for complex calculations

### Error Handling
- **Loading States**: Individual loading states for each system
- **Error Boundaries**: Graceful error handling with fallback data
- **Retry Logic**: Automatic retry for failed API calls
- **Offline Support**: Cached data when connection is lost

## Selectors and Computed Values

```typescript
// Computed selectors for common use cases
const selectors = {
  // Get total alert count across all systems
  getTotalAlerts: (state: SystemState) => 
    state.fireAlarm.activeAlarms + 
    state.cctv.activeIncidents + 
    state.gateBarriers.unauthorizedAttempts,
  
  // Get systems with critical alerts
  getCriticalSystems: (state: SystemState) => {
    const critical = [];
    if (state.fireAlarm.activeAlarms > 0) critical.push('fireAlarm');
    if (state.cctv.activeIncidents > 0) critical.push('cctv');
    if (state.gateBarriers.unauthorizedAttempts > 0) critical.push('gateBarriers');
    return critical;
  },
  
  // Get system health status
  getSystemHealth: (state: SystemState) => ({
    fireAlarm: state.fireAlarm.activeAlarms === 0 ? 'healthy' : 'alert',
    accessControl: 'healthy', // Access control doesn't have alerts
    cctv: state.cctv.activeIncidents === 0 ? 'healthy' : 'alert',
    gateBarriers: state.gateBarriers.unauthorizedAttempts === 0 ? 'healthy' : 'alert'
  })
};
```

## API Integration

### Endpoint Configuration
```typescript
const API_ENDPOINTS = {
  fireAlarm: '/api/systems/fire-alarm',
  accessControl: '/api/systems/access-control',
  cctv: '/api/systems/cctv',
  gateBarriers: '/api/systems/gate-barriers',
  all: '/api/systems/overview'
};
```

### WebSocket Events
```typescript
const WEBSOCKET_EVENTS = {
  FIRE_ALARM_UPDATE: 'fire_alarm_update',
  ACCESS_CONTROL_UPDATE: 'access_control_update',
  CCTV_UPDATE: 'cctv_update',
  GATE_BARRIERS_UPDATE: 'gate_barriers_update',
  SYSTEM_HEALTH: 'system_health_update'
};
```

## File Structure

```
src/stores/
├── systemStore.ts          # Main Zustand store
├── systemStore.test.ts     # Store unit tests
├── types/
│   └── systemTypes.ts      # TypeScript interfaces
├── api/
│   ├── systemApi.ts        # API integration functions
│   └── websocketClient.ts  # WebSocket client
└── selectors/
    └── systemSelectors.ts  # Computed selectors
```

## Testing Requirements

- **Unit Tests**: Store actions, state updates, selectors
- **Integration Tests**: API calls, WebSocket connections
- **Performance Tests**: Memory usage, update frequency
- **Error Handling Tests**: Network failures, invalid data
- **Mock Data**: Comprehensive test fixtures for all systems

## Success Criteria

- [ ] Store manages all four system types (Fire, Access, CCTV, Gates)
- [ ] Real-time updates work via WebSocket and polling
- [ ] Selective subscriptions prevent unnecessary re-renders
- [ ] Error handling and loading states work correctly
- [ ] API integration functions handle all endpoints
- [ ] Selectors provide computed values efficiently
- [ ] Store persists data appropriately (session/local storage)
- [ ] Performance benchmarks meet requirements (<100ms updates)
- [ ] Complete test coverage including edge cases
- [ ] TypeScript types are comprehensive and accurate

## Usage Examples

```tsx
// Basic store usage in components
const fireAlarmData = useSystemStore(state => state.fireAlarm);
const updateFireAlarm = useSystemStore(state => state.updateFireAlarm);

// Using selectors for computed values
const totalAlerts = useSystemStore(selectors.getTotalAlerts);
const criticalSystems = useSystemStore(selectors.getCriticalSystems);

// Fetching data
const fetchData = useSystemStore(state => state.fetchSystemData);
useEffect(() => {
  fetchData();
}, [fetchData]);
```

## Integration Notes

- **Component Integration**: Will be used by SystemCard and SystemOverview components
- **API Requirements**: Requires backend endpoints for all system types
- **WebSocket Setup**: Needs WebSocket server configuration for real-time updates
- **Performance**: Optimized for frequent updates without UI lag

## Future Considerations

- **Historical Data**: Support for trend analysis and historical metrics
- **Notifications**: Integration with notification system for critical alerts
- **Caching Strategy**: Advanced caching with TTL and invalidation
- **Offline Mode**: Enhanced offline capabilities with sync on reconnect