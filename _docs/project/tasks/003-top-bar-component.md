# Task 003: Top Bar Dropdown Components

## Task Overview

**Component System**: Five Standalone Dropdown Components
**Priority**: Medium
**Developer Assignment**: Developer 
**Dependencies**: DashboardLayout (Task 001)
**Location**: `src/components/features/dashboard/layout/`

## Component Description

Create a system of five independent dropdown components that work together to form the top bar functionality. Each component should be self-contained and reusable, handling its own state and interactions while maintaining consistent visual design.

## Component System Overview

The top bar consists of five independent dropdown components arranged horizontally:

### Left Section (Filter Controls)

1. **Building Selection Dropdown** - Geographic building selection
2. **Floor Selection Dropdown** - Floor navigation within selected building
3. **Alert Period Selection Dropdown** - Time range filtering for alerts

### Right Section (User Controls)

4. **Active Alert Bell Indicator** - Notification center with alert count
5. **User Account Section** - User profile and account management

### Visual Requirements

- Fixed height container (~60px) with dark theme background
- Consistent dropdown styling across all components
- Responsive behavior for different screen sizes
- Clear visual hierarchy between left and right sections

## Component Specifications

### Component 1: Building Selection Dropdown

**Responsibilities:**

- Display available buildings in a searchable dropdown
- Handle building selection and state management
- Provide visual feedback for current selection
- Support keyboard navigation and accessibility

**Visual Requirements:**

- Dropdown trigger showing current building name
- Expandable list with building options
- Search functionality for large building lists
- Clear selection indicator and hover states

### Component 2: Floor Selection Dropdown

**Responsibilities:**

- Display floors available in the selected building
- Handle floor selection with zone-aware navigation
- Update dynamically based on building selection
- Provide hierarchical floor organization

**Visual Requirements:**

- Dropdown showing current floor selection
- Organized floor list with zone groupings
- Disabled state when no building is selected
- Visual hierarchy for zones and floors

### Component 3: Alert Period Selection Dropdown

**Responsibilities:**

- Provide time range options for alert filtering
- Handle period selection and time calculations
- Support both preset and custom time ranges
- Manage active time period state

**Visual Requirements:**

- Dropdown displaying current time period
- List of preset time range options
- Custom date range picker integration
- Clear indication of active time filter

### Component 4: Active Alert Bell Indicator

**Responsibilities:**

- Display current alert count and status
- Handle notification interactions
- Provide visual alert priority indicators
- Manage notification dropdown state

**Visual Requirements:**

- Bell icon with alert count badge
- Color-coded priority indicators
- Dropdown panel for alert details
- Animation for new alert notifications

### Component 5: User Account Section

**Responsibilities:**

- Display user profile information
- Handle user menu interactions
- Provide account management options
- Manage user session state

**Visual Requirements:**

- User avatar or initials display
- Dropdown menu with account options
- Profile information display
- Logout and settings access

## Mockup Data Requirements

### Geographic Hierarchy Structure

The components must support a hierarchical data structure following Saudi Arabia building organization:

```
Building
├── Zone 1 (North Wing)
│   ├── Floor 1
│   │   ├── Room 101
│   │   │   ├── Door 101-A (Main Entry)
│   │   │   └── Door 101-B (Emergency Exit)
│   │   └── Room 102
│   │       └── Door 102-A
│   └── Floor 2
│       ├── Room 201
│       │   ├── Door 201-A
│       │   └── Door 201-B
│       └── Room 202
│           └── Door 202-A
└── Zone 2 (South Wing)
    ├── Floor 1
    │   ├── Room 110
    │   │   └── Door 110-A
    │   └── Room 111
    │       └── Door 111-A
    └── Floor 2
        ├── Room 210
        │   ├── Door 210-A
        │   └── Door 210-B
        └── Room 211
            └── Door 211-A
```

### Data Generation Requirements

**Building Selection Data:**

- Multiple buildings with Arabic and English names
- Building codes and identifiers
- Geographic coordinates for mapping
- Building type classifications (Office, Residential, Commercial)

**Floor Selection Data:**

- Zone-based floor organization (North Wing, South Wing, etc.)
- Floor numbering following Saudi standards
- Floor type indicators (Ground, Mezzanine, Basement)
- Accessibility information for each floor

**Room and Door Data:**

- Room numbering system (Zone-Floor-Room format)
- Door classifications (Main Entry, Emergency Exit, Service)
- Access control levels and permissions
- Room purpose and occupancy information

**Alert Period Data:**

- Preset time ranges (Last Hour, Last 24 Hours, Last Week)
- Custom date range selection capability
- Time zone handling for Saudi Arabia (AST)
- Historical data availability periods

**Alert Notification Data:**

- Alert priority levels (Critical, High, Medium, Low)
- Alert categories (Security, Fire, Access, System)
- Real-time alert counts and timestamps
- Alert source location mapping to hierarchy

## Visual Design Guidelines

### Layout Structure

- **Container Height**: Fixed 60px height for consistent top bar sizing
- **Horizontal Layout**: Left section for filters, right section for user controls
- **Spacing**: Consistent gaps between dropdown components
- **Alignment**: Vertical center alignment for all components

### Dropdown Component Styling

- **Trigger Design**: Rectangular buttons with rounded corners
- **Border Treatment**: Subtle borders with hover state changes
- **Typography**: Clear, readable labels with appropriate font sizing
- **Arrow Indicators**: Consistent dropdown arrow positioning

### Color Scheme

- **Background**: Dark theme (#1a1a1a) for main container
- **Component Backgrounds**: Transparent with hover state changes
- **Text Colors**: High contrast white text on dark backgrounds
- **Border Colors**: Subtle gray borders (#444) with hover variations

### Interactive States

- **Default State**: Neutral appearance with clear component boundaries
- **Hover State**: Background color change and border highlight
- **Active State**: Distinct styling for opened dropdown components
- **Disabled State**: Reduced opacity for unavailable options

### Responsive Behavior

- **Desktop**: Full component spacing and sizing
- **Tablet**: Reduced gaps and component widths
- **Mobile**: Compact layout with touch-friendly sizing
- **Breakpoint Handling**: Smooth transitions between screen sizes

### Accessibility Features

- **Focus Indicators**: Clear visual focus states for keyboard navigation
- **ARIA Labels**: Proper labeling for screen reader compatibility
- **Color Contrast**: WCAG compliant contrast ratios
- **Touch Targets**: Minimum 44px touch target sizes for mobile

## Component Integration Guidelines

### Design Consistency

- **Visual Harmony**: All five components should maintain consistent styling patterns
- **Spacing Standards**: Uniform gaps and padding across all dropdown components
- **Typography Consistency**: Same font families, sizes, and weights throughout
- **Color Coordination**: Consistent use of theme colors and interactive states

### Component Relationships

- **Independent Operation**: Each component functions as a standalone unit
- **State Isolation**: Components maintain their own internal state while sharing global state through Zustand
- **Event Communication**: Clear patterns for component-to-component communication via Zustand stores
- **Data Flow**: Consistent data handling patterns across all components using Zustand state management

### State Management

- **Zustand Integration**: Components should utilize Zustand for centralized state management
- **Global State Sharing**: Shared state for filters, notifications, and user data managed through Zustand stores
- **Local State Management**: Component-specific UI state handled locally while business logic uses Zustand
- **Store Organization**: Separate stores for different concerns (filters, notifications, user management)

### User Experience Standards

- **Intuitive Navigation**: Clear visual hierarchy and logical component ordering
- **Responsive Feedback**: Immediate visual feedback for user interactions
- **Loading States**: Consistent loading indicators for data-dependent components
- **Error Handling**: Graceful error states with clear user messaging

## Implementation Guidelines

### Development Approach

- **Component-First Design**: Build each dropdown as an independent, reusable component
- **Progressive Enhancement**: Start with basic functionality and add advanced features incrementally
- **Mobile-First Responsive**: Design for mobile devices first, then scale up to desktop
- **Accessibility-Driven**: Implement accessibility features from the beginning, not as an afterthought

### Component Architecture

- **Modular Structure**: Each of the five components should be self-contained and reusable
- **Consistent Interfaces**: Standardized props and event handling patterns across all components
- **State Management**: Clear separation between local component state and shared application state
- **Error Boundaries**: Robust error handling to prevent component failures from affecting the entire top bar

### User Experience Goals

- **Intuitive Interaction**: Users should immediately understand how to interact with each component
- **Fast Response Times**: All interactions should provide immediate visual feedback
- **Consistent Behavior**: Similar actions should work the same way across all components
- **Graceful Degradation**: Components should work even when data is unavailable or loading

### Integration Requirements

- **Theme Compatibility**: Components must integrate seamlessly with the existing design system
- **Data Source Flexibility**: Components should work with various data sources and formats
- **Event System**: Clear patterns for communicating between components and parent containers
- **Performance Optimization**: Efficient rendering and minimal re-renders for smooth user experience

## Summary

This task defines the requirements for implementing a **Five Standalone Dropdown Components System** that forms the top navigation bar of the dashboard. The system consists of:

### Component Structure

1. **Building Selection Dropdown** - Hierarchical building navigation
2. **Floor Selection Dropdown** - Floor-level filtering within selected building
3. **Alert Period Selection Dropdown** - Time-based alert filtering
4. **Active Alert Bell Indicator** - Real-time notification display
5. **User Account Section** - User profile and account management

### Key Features

- **Saudi Arabia Geographic Hierarchy** - Structured mockup data following building → zone → floor → room → door hierarchy
- **Independent Components** - Each dropdown functions as a standalone, reusable unit
- **Consistent Design Language** - Unified visual styling and interaction patterns
- **Responsive Layout** - Adaptive design for desktop, tablet, and mobile devices
- **Accessibility Compliance** - WCAG-compliant design with proper ARIA labeling

### Design Focus

This specification emphasizes **UI design requirements** and **component behavior** without implementation code, providing clear guidelines for creating a cohesive, user-friendly navigation system that integrates seamlessly with the dashboard layout.