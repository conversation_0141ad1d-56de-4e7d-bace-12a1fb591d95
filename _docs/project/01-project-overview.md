# Nublar Alert Dashboard System - Project Overview

## Project Description

This Next.js application is a comprehensive alert dashboard system designed for monitoring and managing low-level security and safety systems across building facilities. The system provides real-time monitoring, alert management, and interactive 2D floor plan visualization for multiple building zones.

## System Architecture

### Core Systems Monitored

The dashboard integrates with four primary low-level systems:

1. **Fire Alarm System**

   - Real-time fire detection monitoring
   - Smoke detector status tracking
   - Emergency evacuation alerts
   - Heat sensor monitoring
2. **Access Control System**

   - Door access monitoring
   - Entry/exit logging
   - Unauthorized access alerts
   - Card reader status tracking
3. **CCTV Control System**

   - Camera feed monitoring
   - Motion detection alerts
   - Recording status tracking
   - Camera health monitoring
4. **Gates & Barriers System**

   - Automated gate control
   - Barrier position monitoring
   - Vehicle access logging
   - Mechanical failure alerts

## Geographic Hierarchy Structure

The system follows a hierarchical geographic organization model:

```
Building
├── Zone 1 (North Wing)
│   ├── Floor 1
│   │   ├── Room 101
│   │   │   ├── Door 101-A (Main Entry)
│   │   │   └── Door 101-B (Emergency Exit)
│   │   └── Room 102
│   │       └── Door 102-A
│   └── Floor 2
│       ├── Room 201
│       │   ├── Door 201-A
│       │   └── Door 201-B
│       └── Room 202
│           └── Door 202-A
└── Zone 2 (South Wing)
    ├── Floor 1
    │   ├── Room 110
    │   │   └── Door 110-A
    │   └── Room 111
    │       └── Door 111-A
    └── Floor 2
        ├── Room 210
        │   ├── Door 210-A
        │   └── Door 210-B
        └── Room 211
            └── Door 211-A
```

### Hierarchy Levels

- **Building**: Top-level facility container
- **Zone**: Major building sections (North Wing, South Wing, etc.)
- **Floor**: Individual building levels within each zone
- **Room**: Specific spaces within each floor
- **Door**: Individual access points within each room

## User Interface Components

### Main Dashboard

- **System Overview Panel**: Real-time status of all four core systems
- **Alert Summary**: Current active alerts with priority levels
- **Geographic Navigator**: Interactive building/zone/floor selector
- **Quick Actions**: Emergency controls and system overrides

### 2D Floor Plans

- **Interactive Floor Maps**: Each floor features a detailed 2D architectural plan
- **Device Positioning**: Visual representation of sensors, cameras, and access points
- **Real-time Status Indicators**: Color-coded status markers for each device
- **Clickable Elements**: Interactive zones for detailed device information

### Alert Management

- **Alert Queue**: Chronological list of system alerts
- **Priority Classification**: Critical, High, Medium, Low alert categories
- **Alert Filtering**: System-based and location-based filtering options
- **Alert History**: Historical alert data and resolution tracking

## Alert Popup System

The system features a comprehensive alert popup mechanism that displays detailed information when users click on devices in the 2D floor plans. Each popup provides system-specific information with color-coded states and standardized data fields.

### Popup Color Coding & States

- **Green Border/Accent**: Normal operational state ("Opened" status)
- **Red Border/Accent**: Danger/Alert state ("Fire alarm activated")
- **Blue Border/Accent**: Informational state (Gate barriers, CCTV)
- **Orange Border/Accent**: Warning state (Fire alarm points)

### System-Specific Popup Details

#### 1. Access Control System

**Normal State (Green)**

- **Header**: "Access control" with green accent
- **Status Indicator**: "Opened" (green badge)
- **Standard Fields**:
  - Zone: Floor location and building area
  - Description: Action description ("the door in the floor 1 was opened")
  - Public address: Emergency instruction ("Everyone must proceed to Gate 2")
- **Actions**: Close, Action text buttons

#### 2. Fire Alarm System

**Danger State (Red)**

- **Header**: "Fire alarm activated" with red accent and fire icon
- **Timestamp**: Detection time ("Smoke Detector - 12/9/2025 - 12:00 pm")
- **Standard Fields**:
  - Zone: Floor location and building area
  - Description: Alert details ("Smoke detected near the main conference room")
  - Public address: Emergency evacuation instruction
- **Actions**: Close, Action text buttons

#### 3. Gate Barrier System

**Informational State (Blue)**

- **Header**: "Gate barrier" with blue accent and barrier icon
- **Status**: "Some info" subtitle
- **Standard Fields**:
  - Zone: Floor location and building area
  - Description: Placeholder text ("we will add some description here")
  - Public address: Standard evacuation instruction
- **Actions**: Close, Action text buttons

#### 4. CCTV Control System

**Informational State (Purple)**

- **Header**: "CCTV control" with purple accent and camera icon
- **Live Feed**: Real-time camera view of monitored area
- **Standard Fields**:
  - Zone: Floor location and building area
  - Description: Placeholder text ("we will add some description here")
  - Public address: Standard evacuation instruction
- **Actions**: Close, Watch stream buttons

#### 5. Fire Alarm Point System

**Warning State (Orange)**

- **Header**: "Fire alarm point" with orange accent and alarm icon
- **Status**: "Some info" subtitle
- **Standard Fields**:
  - Zone: Floor location and building area
  - Description: Empty field ("-")
  - Public address: Standard evacuation instruction
- **Actions**: Close, Action text buttons

### Popup Data Structure

All popups follow a standardized data structure:

```typescript
interface AlertPopup {
  systemType: 'access_control' | 'fire_alarm' | 'gate_barrier' | 'cctv' | 'fire_alarm_point';
  state: 'normal' | 'danger' | 'warning' | 'info';
  colorScheme: 'green' | 'red' | 'blue' | 'purple' | 'orange';
  header: {
    title: string;
    icon: string;
    timestamp?: string;
    status?: string;
  };
  content: {
    zone: string;
    description: string;
    publicAddress: string;
    liveStream?: string; // For CCTV systems
  };
  actions: {
    primary: 'Close';
    secondary: 'Action text' | 'Watch stream';
  };
}
```

### Interactive Behavior

- **Device Click**: Clicking any device icon on the 2D floor plan triggers the corresponding popup
- **Real-time Updates**: Popups reflect current device status and can update dynamically
- **Action Buttons**: Secondary actions vary by system type (Action text, Watch stream)
- **Responsive Design**: Popups adapt to different screen sizes while maintaining readability

## Technical Implementation

### Frontend Architecture

- **Framework**: Next.js 15.5.2 with TypeScript
- **State Management**: Zustand for global application state
- **UI Components**: Custom component library with consistent design system
- **Real-time Updates**: WebSocket integration for live alert streaming

### Data Structure

- **Hierarchical Data Model**: Nested structure reflecting geographic organization
- **Device Registry**: Comprehensive database of all monitored devices
- **Alert Schema**: Standardized alert format across all system types
- **User Permissions**: Role-based access control for different user levels

## Key Features

1. **Real-time Monitoring**: Live status updates from all connected systems
2. **Interactive Floor Plans**: Visual representation of building layouts with device positioning
3. **Multi-level Navigation**: Seamless navigation through building hierarchy
4. **Alert Prioritization**: Intelligent alert classification and routing
5. **Historical Data**: Comprehensive logging and reporting capabilities
6. **Mobile Responsive**: Optimized for desktop and mobile device access
7. **Emergency Protocols**: Integrated emergency response procedures

## Future Enhancements

- Integration with additional building systems (HVAC, Lighting)
- Advanced analytics and predictive maintenance
- Mobile application development
- API integration with external emergency services
- Enhanced reporting and compliance features

## Development Status

This project is currently in active development with the core dashboard framework implemented. The system architecture supports scalable expansion for additional building facilities and system integrations.

---

*This document serves as the foundational overview for the Alert Dashboard System. Detailed technical specifications, API documentation, and system-specific alert configurations will be documented in separate technical documents.*
