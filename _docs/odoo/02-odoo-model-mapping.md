# Odoo Model Mapping for Nebular Dashboard System

## Overview

This document maps the TypeScript interfaces from the Nebular Dashboard System to corresponding Odoo model structures using the "nebular" namespace, including field definitions, relationships, and business logic.

## 1. Geographic Hierarchy Models

### 1.1 Building Model (`nebular.building`)

```python
class NebularBuilding(models.Model):
    _name = 'nebular.building'
    _description = 'Building Management'
    _order = 'name'
    _rec_name = 'name'

    # Basic Information
    name = fields.Char(string='Building Name', required=True, index=True)
    short_code = fields.Char(string='Short Code', required=True, size=10, index=True)
    address = fields.Text(string='Address')
    description = fields.Text(string='Description')
  
    # Statistics (computed fields)
    total_floors = fields.Integer(string='Total Floors', compute='_compute_totals', store=True)
    total_rooms = fields.Integer(string='Total Rooms', compute='_compute_totals', store=True)
    total_doors = fields.Integer(string='Total Doors', compute='_compute_totals', store=True)
  
    # Status
    is_active = fields.Boolean(string='Active', default=True)
  
    # Relationships
    zone_ids = fields.One2many('nebular.zone', 'building_id', string='Zones')
    floor_ids = fields.One2many('nebular.floor', 'building_id', string='Floors')
    event_ids = fields.One2many('nebular.event', 'building_id', string='Events')
    marker_ids = fields.One2many('nebular.marker', 'building_id', string='Markers')
    device_ids = fields.One2many('nebular.device', 'building_id', string='Devices')
  
    # Constraints
    _sql_constraints = [
        ('short_code_unique', 'UNIQUE(short_code)', 'Building short code must be unique!'),
        ('name_unique', 'UNIQUE(name)', 'Building name must be unique!')
    ]
  
    @api.depends('zone_ids.floor_ids', 'zone_ids.floor_ids.room_ids', 'zone_ids.floor_ids.room_ids.door_ids')
    def _compute_totals(self):
        for building in self:
            floors = building.zone_ids.mapped('floor_ids')
            rooms = floors.mapped('room_ids')
            doors = rooms.mapped('door_ids')
        
            building.total_floors = len(floors)
            building.total_rooms = len(rooms)
            building.total_doors = len(doors)
```

### 1.2 Zone Model (`nebular.zone`)

```python
class NebularZone(models.Model):
    _name = 'nebular.zone'
    _description = 'Building Zone Management'
    _order = 'building_id, name'
    _rec_name = 'name'

    # Basic Information
    name = fields.Char(string='Zone Name', required=True, index=True)
    zone_code = fields.Char(string='Zone Code', required=True, size=10, index=True)
    description = fields.Text(string='Description')
  
    # Relationships
    building_id = fields.Many2one('nebular.building', string='Building', required=True, ondelete='cascade')
    floor_ids = fields.One2many('nebular.floor', 'zone_id', string='Floors')
    event_ids = fields.One2many('nebular.event', 'zone_id', string='Events')
    marker_ids = fields.One2many('nebular.marker', 'zone_id', string='Markers')
    device_ids = fields.One2many('nebular.device', 'zone_id', string='Devices')
  
    # Status
    is_active = fields.Boolean(string='Active', default=True)
  
    # Computed fields
    total_floors = fields.Integer(string='Total Floors', compute='_compute_totals', store=True)
    total_rooms = fields.Integer(string='Total Rooms', compute='_compute_totals', store=True)
    total_doors = fields.Integer(string='Total Doors', compute='_compute_totals', store=True)
  
    # Display name
    display_name = fields.Char(string='Display Name', compute='_compute_display_name', store=True)
  
    # Constraints
    _sql_constraints = [
        ('zone_code_building_unique', 'UNIQUE(zone_code, building_id)', 
         'Zone code must be unique within a building!')
    ]
  
    @api.depends('name', 'zone_code', 'building_id.name')
    def _compute_display_name(self):
        for zone in self:
            zone.display_name = f"{zone.building_id.name} - {zone.name} ({zone.zone_code})"
  
    @api.depends('floor_ids', 'floor_ids.room_ids', 'floor_ids.room_ids.door_ids')
    def _compute_totals(self):
        for zone in self:
            rooms = zone.floor_ids.mapped('room_ids')
            doors = rooms.mapped('door_ids')
        
            zone.total_floors = len(zone.floor_ids)
            zone.total_rooms = len(rooms)
            zone.total_doors = len(doors)
```

### 1.3 Floor Model (`nebular.floor`)

```python
class NebularFloor(models.Model):
    _name = 'nebular.floor'
    _description = 'Floor Management'
    _order = 'building_id, zone_id, level'
    _rec_name = 'name'

    # Basic Information
    name = fields.Char(string='Floor Name', required=True, index=True)
    level = fields.Integer(string='Floor Level', required=True)
    floor_code = fields.Char(string='Floor Code', required=True, size=10, index=True)
    floor_plan_url = fields.Char(string='Floor Plan URL', default='/plans/floorPlan-1.png')
  
    # Relationships
    building_id = fields.Many2one('nebular.building', string='Building', required=True, ondelete='cascade')
    zone_id = fields.Many2one('nebular.zone', string='Zone', required=True, ondelete='cascade')
    room_ids = fields.One2many('nebular.room', 'floor_id', string='Rooms')
    event_ids = fields.One2many('nebular.event', 'floor_id', string='Events')
    marker_ids = fields.One2many('nebular.marker', 'floor_id', string='Markers')
    device_ids = fields.One2many('nebular.device', 'floor_id', string='Devices')
  
    # Status
    is_active = fields.Boolean(string='Active', default=True)
  
    # Computed fields
    total_rooms = fields.Integer(string='Total Rooms', compute='_compute_totals', store=True)
    total_doors = fields.Integer(string='Total Doors', compute='_compute_totals', store=True)
    display_name = fields.Char(string='Display Name', compute='_compute_display_name', store=True)
  
    # Constraints
    _sql_constraints = [
        ('floor_code_building_unique', 'UNIQUE(floor_code, building_id)', 
         'Floor code must be unique within a building!'),
        ('level_zone_unique', 'UNIQUE(level, zone_id)', 
         'Floor level must be unique within a zone!')
    ]
  
    @api.depends('name', 'floor_code', 'level', 'zone_id.name', 'building_id.short_code')
    def _compute_display_name(self):
        for floor in self:
            floor.display_name = f"{floor.building_id.short_code} - {floor.zone_id.name} - {floor.name} (L{floor.level})"
  
    @api.depends('room_ids', 'room_ids.door_ids')
    def _compute_totals(self):
        for floor in self:
            doors = floor.room_ids.mapped('door_ids')
            floor.total_rooms = len(floor.room_ids)
            floor.total_doors = len(doors)
```

### 1.4 Room Model (`nebular.room`)

```python
class NebularRoom(models.Model):
    _name = 'nebular.room'
    _description = 'Room Management'
    _order = 'floor_id, name'
    _rec_name = 'name'

    # Basic Information
    name = fields.Char(string='Room Name', required=True, index=True)
    room_code = fields.Char(string='Room Code', required=True, size=20, index=True)
    room_type = fields.Selection([
        ('office', 'Office'),
        ('conference', 'Conference Room'),
        ('storage', 'Storage'),
        ('lobby', 'Lobby'),
        ('restroom', 'Restroom'),
        ('utility', 'Utility'),
        ('other', 'Other')
    ], string='Room Type', required=True, default='office')
  
    # Optional Information
    capacity = fields.Integer(string='Capacity (People)')
    area = fields.Float(string='Area (m²)', digits=(10, 2))
  
    # Relationships
    floor_id = fields.Many2one('nebular.floor', string='Floor', required=True, ondelete='cascade')
    building_id = fields.Many2one('nebular.building', string='Building', 
                                  related='floor_id.building_id', store=True, readonly=True)
    zone_id = fields.Many2one('nebular.zone', string='Zone', 
                              related='floor_id.zone_id', store=True, readonly=True)
    door_ids = fields.One2many('nebular.door', 'room_id', string='Doors')
  
    # Status
    is_active = fields.Boolean(string='Active', default=True)
  
    # Computed fields
    total_doors = fields.Integer(string='Total Doors', compute='_compute_total_doors', store=True)
    display_name = fields.Char(string='Display Name', compute='_compute_display_name', store=True)
  
    # Constraints
    _sql_constraints = [
        ('room_code_floor_unique', 'UNIQUE(room_code, floor_id)', 
         'Room code must be unique within a floor!'),
        ('capacity_positive', 'CHECK(capacity >= 0)', 'Capacity must be positive!'),
        ('area_positive', 'CHECK(area >= 0)', 'Area must be positive!')
    ]
  
    @api.depends('name', 'room_code', 'floor_id.display_name')
    def _compute_display_name(self):
        for room in self:
            room.display_name = f"{room.floor_id.display_name} - {room.name} ({room.room_code})"
  
    @api.depends('door_ids')
    def _compute_total_doors(self):
        for room in self:
            room.total_doors = len(room.door_ids)
```

### 1.5 Door Model (`nebular.door`)

```python
class NebularDoor(models.Model):
    _name = 'nebular.door'
    _description = 'Door Access Control'
    _order = 'room_id, name'
    _rec_name = 'name'

    # Basic Information
    name = fields.Char(string='Door Name', required=True, index=True)
    door_code = fields.Char(string='Door Code', required=True, size=20, index=True)
  
    # Door Configuration
    door_type = fields.Selection([
        ('main_entry', 'Main Entry'),
        ('emergency_exit', 'Emergency Exit'),
        ('standard', 'Standard'),
        ('security', 'Security'),
        ('fire_exit', 'Fire Exit')
    ], string='Door Type', required=True, default='standard')
  
    access_level = fields.Selection([
        ('public', 'Public'),
        ('restricted', 'Restricted'),
        ('emergency_only', 'Emergency Only'),
        ('admin_only', 'Admin Only')
    ], string='Access Level', required=True, default='public')
  
    # Status
    status = fields.Selection([
        ('open', 'Open'),
        ('closed', 'Closed'),
        ('locked', 'Locked'),
        ('malfunction', 'Malfunction')
    ], string='Status', required=True, default='closed', index=True)
  
    is_active = fields.Boolean(string='Active', default=True)
    last_status_change = fields.Datetime(string='Last Status Change', default=fields.Datetime.now)
  
    # Relationships
    room_id = fields.Many2one('nebular.room', string='Room', required=True, ondelete='cascade')
    floor_id = fields.Many2one('nebular.floor', string='Floor', 
                               related='room_id.floor_id', store=True, readonly=True)
    building_id = fields.Many2one('nebular.building', string='Building', 
                                  related='room_id.building_id', store=True, readonly=True)
    zone_id = fields.Many2one('nebular.zone', string='Zone', 
                              related='room_id.zone_id', store=True, readonly=True)
  
    # Computed fields
    display_name = fields.Char(string='Display Name', compute='_compute_display_name', store=True)
    is_alert = fields.Boolean(string='Is Alert', compute='_compute_is_alert', store=True)
  
    # Constraints
    _sql_constraints = [
        ('door_code_unique', 'UNIQUE(door_code)', 'Door code must be unique!')
    ]
  
    @api.depends('name', 'door_code', 'room_id.display_name')
    def _compute_display_name(self):
        for door in self:
            door.display_name = f"{door.room_id.display_name} - {door.name} ({door.door_code})"
  
    @api.depends('status', 'door_type')
    def _compute_is_alert(self):
        for door in self:
            # Define alert conditions based on door type and status
            alert_conditions = {
                'malfunction': True,
                'open': door.door_type in ['security', 'emergency_exit'],
                'locked': door.door_type == 'main_entry'
            }
            door.is_alert = alert_conditions.get(door.status, False)
  
    def write(self, vals):
        if 'status' in vals:
            vals['last_status_change'] = fields.Datetime.now()
        return super().write(vals)
```

### 1.6 Device Model (`nebular.device`)

```python
class NebularDevice(models.Model):
    _name = 'nebular.device'
    _description = 'Interactive Floor Plan Devices (Based on Marker)'
    _order = 'floor_id, name'
    _rec_name = 'name'

    # Basic Information (from Marker)
    name = fields.Char(string='Device Name', required=True, index=True)
    title = fields.Char(string='Title', required=True)
    subtitle = fields.Char(string='Subtitle')
    description = fields.Text(string='Description')
    public_address = fields.Text(string='Public Address')
  
    # Device Type (from Marker marker_type)
    device_type = fields.Selection([
        ('fire', 'Fire System'),
        ('door', 'Door'),
        ('gate', 'Gate'),
        ('camera', 'Camera'),
        ('people', 'People Counter'),
        ('sensor', 'Sensor'),
        ('emergency', 'Emergency'),
        ('person', 'Person')
    ], string='Device Type', required=True, index=True)
  
    # Position on floor plan (from Marker)
    position_x = fields.Float(string='Position X', required=True, digits=(10, 2))
    position_y = fields.Float(string='Position Y', required=True, digits=(10, 2))
    position_x_percent = fields.Float(string='Position X (%)', digits=(5, 2))
    position_y_percent = fields.Float(string='Position Y (%)', digits=(5, 2))
  
    # Status and Alert (from Marker)
    status = fields.Char(string='Status', required=True, index=True)
    is_alert = fields.Boolean(string='Is Alert', default=False, index=True)
    alert_timestamp = fields.Datetime(string='Alert Timestamp')
  
    # Optional fields (from Marker)
    count = fields.Integer(string='Count')
    source = fields.Char(string='Source')
  
    # Location References (from Marker)
    building_id = fields.Many2one('nebular.building', string='Building', required=True, ondelete='cascade')
    zone_id = fields.Many2one('nebular.zone', string='Zone', required=True, ondelete='cascade')
    floor_id = fields.Many2one('nebular.floor', string='Floor', required=True, ondelete='cascade')
    zone_display = fields.Char(string='Zone Display')
  
    # Computed fields
    display_name = fields.Char(string='Display Name', compute='_compute_display_name', store=True)
    device_color = fields.Char(string='Device Color', compute='_compute_device_style')
    device_icon = fields.Char(string='Device Icon', compute='_compute_device_style')
  
    @api.depends('title', 'name', 'device_type')
    def _compute_display_name(self):
        for device in self:
            device.display_name = f"[{device.device_type.upper()}] {device.title} ({device.name})"
  
    @api.depends('device_type')
    def _compute_device_style(self):
        """Compute device color and icon based on type"""
        style_map = {
            'fire': {'color': '#ff4444', 'icon': '🔥'},
            'door': {'color': '#4CAF50', 'icon': '🚪'},
            'gate': {'color': '#FF9800', 'icon': '🚧'},
            'camera': {'color': '#2196F3', 'icon': '📹'},
            'people': {'color': '#9C27B0', 'icon': '👤'},
            'person': {'color': '#9C27B0', 'icon': '👤'},
            'sensor': {'color': '#607D8B', 'icon': '📡'},
            'emergency': {'color': '#F44336', 'icon': '🚨'},
        }
    
        for device in self:
            style = style_map.get(device.device_type, {'color': '#000000', 'icon': '❓'})
            device.device_color = style['color']
            device.device_icon = style['icon']
    # Computed fields
    display_name = fields.Char(string='Display Name', compute='_compute_display_name', store=True)
    device_color = fields.Char(string='Device Color', compute='_compute_device_style')
    device_icon = fields.Char(string='Device Icon', compute='_compute_device_style')
  
    @api.depends('title', 'name', 'device_type')
    def _compute_display_name(self):
        for device in self:
            device.display_name = f"[{device.device_type.upper()}] {device.title} ({device.name})"
  
    @api.depends('device_type')
    def _compute_device_style(self):
        """Compute device color and icon based on type"""
        style_map = {
            'fire': {'color': '#ff4444', 'icon': '🔥'},
            'door': {'color': '#4CAF50', 'icon': '🚪'},
            'gate': {'color': '#FF9800', 'icon': '🚧'},
            'camera': {'color': '#2196F3', 'icon': '📹'},
            'people': {'color': '#9C27B0', 'icon': '👤'},
            'person': {'color': '#9C27B0', 'icon': '👤'},
            'sensor': {'color': '#607D8B', 'icon': '📡'},
            'emergency': {'color': '#F44336', 'icon': '🚨'},
        }
    
        for device in self:
            style = style_map.get(device.device_type, {'color': '#000000', 'icon': '❓'})
            device.device_color = style['color']
            device.device_icon = style['icon']
```

## 2. Event System Models

### 2.1 Event Models Class Diagram

```mermaid
classDiagram
    class NebularEvent {
        +int id
        +Many2one building_id
        +char building_code
        +Many2one zoon_id
        +char zoon_code
        +Many2one floor_id
        +char floor_code
        +char system_id        %% e.g., fire, access, cctv, gate, pa, presence
        +char system_name
        +char event_type
        +datetime datetime
        +text message
        +char state
        +char command
        +json data_json        %% computed only for API
        +int severity
        +char res_model        %% linked Odoo model
        +int res_id            %% linked record ID
    }

    class NebularFireEvent {
        +int id
        +Many2one event_id
        +char panel_code
        +char panel_name
        +char zone
        +char loop
        +int node_id
        +char node_code
        +char address
    }

    class NebularAccessEvent {
        +int id
        +Many2one event_id
        +int controller_id
        +char controller_code
        +char controller_name
        +int reader_id
        +char reader_code
        +int card_id
        +char card_code
        +int user_id
        +char user_code
        +char door_id
        +char door_name
        +char result
        +char reason
        +int held_open_seconds
    }

    class NebularCCTVEvent {
        +int id
        +Many2one event_id
        +int camera_id
        +char camera_code
        +char camera_name
        +char location
        +char vendor
        +char model
        +char ip
        +int channel
        +char analytic_type
        +int count
        +char face_id
        +char uri
        +char snapshot_url
        +bool recording
    }

    class NebularGateEvent {
        +int id
        +Many2one event_id
        +int gate_id
        +char gate_code
        +char name
        +char location
        +char status
        +char vehicle_plate
        +char trigger
        +float anpr_conf
    }

    class NebularPAEvent {
        +int id
        +Many2one event_id
        +int zone_id
        +char zone_code
        +char zone_name
        +char location
        +int volume
        +text message
        +char announcement_id
        +char script
        +int duration_sec
    }

    class NebularPresenceEvent {
        +int id
        +Many2one event_id
        +int sensor_id
        +char sensor_code
        +char name
        +char location
        +char vendor
        +char model
        +char type           %% pir/microwave/ultrasonic/camera
        +int count
        +bool occupancy
    }

    %% Relationships
    NebularFireEvent --> NebularEvent : event_id
    NebularAccessEvent --> NebularEvent : event_id
    NebularCCTVEvent --> NebularEvent : event_id
    NebularGateEvent --> NebularEvent : event_id
    NebularPAEvent --> NebularEvent : event_id
    NebularPresenceEvent --> NebularEvent : event_id
```

### 2.2 Main Event Model (`nebular.event`)

```python
class NebularEvent(models.Model):
    _name = 'nebular.event'
    _description = 'System Events and Alerts'
    _order = 'datetime desc'
    _rec_name = 'message'

    # Core fields
    id = fields.Integer(string='ID', readonly=True)
    building_id = fields.Many2one('nebular.building', string='Building', required=True, ondelete='cascade')
    building_code = fields.Char(string='Building Code', required=True, index=True)
    zoon_id = fields.Many2one('nebular.floor', string='Floor', required=True, ondelete='cascade')
    zoon_code = fields.Char(string='Floor Code', required=True, index=True)
    floor_id = fields.Many2one('nebular.floor', string='Floor', required=True, ondelete='cascade')
    floor_code = fields.Char(string='Floor Code', required=True, index=True)
    system_id = fields.Char(string='System ID', required=True, index=True)  # e.g., fire, access, cctv, gate, pa, presence
    system_name = fields.Char(string='System Name', required=True)
    event_type = fields.Char(string='Event Type', required=True, index=True)
    datetime = fields.Datetime(string='Event DateTime', required=True, default=fields.Datetime.now)
    message = fields.Text(string='Message')
    state = fields.Char(string='State', index=True)
    command = fields.Char(string='Command')
    data_json = fields.Json(string='Data JSON', compute='_compute_data_json')  # computed only for API
    severity = fields.Integer(string='Severity', default=0)
    res_model = fields.Char(string='Linked Model')  # linked Odoo model
    res_id = fields.Integer(string='Linked Record ID')  # linked record ID

    @api.depends('system_id')
    def _compute_data_json(self):
        """Compute data_json field for API responses"""
        for event in self:
            data = {}
            # Get system-specific data based on system_id
            if event.system_id == 'fire':
                fire_event = self.env['nebular.fire.event'].search([('event_id', '=', event.id)], limit=1)
                if fire_event:
                    data = {
                        'panel_code': fire_event.panel_code,
                        'panel_name': fire_event.panel_name,
                        'zone': fire_event.zone,
                        'loop': fire_event.loop,
                        'node_id': fire_event.node_id,
                        'node_code': fire_event.node_code,
                        'address': fire_event.address,
                    }
            elif event.system_id == 'access':
                access_event = self.env['nebular.access.event'].search([('event_id', '=', event.id)], limit=1)
                if access_event:
                    data = {
                        'controller_id': access_event.controller_id,
                        'controller_code': access_event.controller_code,
                        'controller_name': access_event.controller_name,
                        'reader_id': access_event.reader_id,
                        'reader_code': access_event.reader_code,
                        'card_id': access_event.card_id,
                        'card_code': access_event.card_code,
                        'user_id': access_event.user_id,
                        'user_code': access_event.user_code,
                        'door_id': access_event.door_id,
                        'door_name': access_event.door_name,
                        'result': access_event.result,
                        'reason': access_event.reason,
                        'held_open_seconds': access_event.held_open_seconds,
                    }
            # Add other system types as needed...
            event.data_json = data

    @api.model
    def create_from_json(self, json_data):
        """Create event from JSON API data"""
        vals = {
            'building_code': json_data.get('buildingCode'),
            'floor_code': json_data.get('floorCode'),
            'system_id': json_data.get('systemId'),
            'system_name': json_data.get('systemName'),
            'event_type': json_data.get('eventType'),
            'datetime': json_data.get('datetime'),
            'message': json_data.get('message'),
            'state': json_data.get('state'),
            'command': json_data.get('command'),
            'severity': json_data.get('severity', 0),
        }
  
        # Create main event
        event = self.create(vals)
  
        # Create system-specific event based on system_id
        data = json_data.get('data', {})
        if vals['system_id'] == 'fire':
            self.env['nebular.fire.event'].create({
                'event_id': event.id,
                'panel_code': data.get('panelCode'),
                'panel_name': data.get('panelName'),
                'zone': data.get('zone'),
                'loop': data.get('loop'),
                'node_id': data.get('nodeId'),
                'node_code': data.get('nodeCode'),
                'address': data.get('address'),
            })
        elif vals['system_id'] == 'access':
            self.env['nebular.access.event'].create({
                'event_id': event.id,
                'controller_id': data.get('controllerId'),
                'controller_code': data.get('controllerCode'),
                'controller_name': data.get('controllerName'),
                'reader_id': data.get('readerId'),
                'reader_code': data.get('readerCode'),
                'card_id': data.get('cardId'),
                'card_code': data.get('cardCode'),
                'user_id': data.get('userId'),
                'user_code': data.get('userCode'),
                'door_id': data.get('doorId'),
                'door_name': data.get('doorName'),
                'result': data.get('result'),
                'reason': data.get('reason'),
                'held_open_seconds': data.get('heldOpenSeconds'),
            })
        # Add other system types as needed...
  
        return event
```

### 2.3 Fire System Events (`nebular.fire.event`)

```python
class NebularFireEvent(models.Model):
    _name = 'nebular.fire.event'
    _description = 'Fire System Events'
    _rec_name = 'panel_name'

    id = fields.Integer(string='ID', readonly=True)
    event_id = fields.Many2one('nebular.event', string='Event', required=True, ondelete='cascade')
    panel_code = fields.Char(string='Panel Code')
    panel_name = fields.Char(string='Panel Name')
    zone = fields.Char(string='Zone')
    loop = fields.Char(string='Loop')
    node_id = fields.Integer(string='Node ID')
    node_code = fields.Char(string='Node Code')
    address = fields.Char(string='Address')
```

### 2.4 Access Control Events (`nebular.access.event`)

```python
class NebularAccessEvent(models.Model):
    _name = 'nebular.access.event'
    _description = 'Access Control Events'
    _rec_name = 'door_name'

    id = fields.Integer(string='ID', readonly=True)
    event_id = fields.Many2one('nebular.event', string='Event', required=True, ondelete='cascade')
    controller_id = fields.Integer(string='Controller ID')
    controller_code = fields.Char(string='Controller Code')
    controller_name = fields.Char(string='Controller Name')
    reader_id = fields.Integer(string='Reader ID')
    reader_code = fields.Char(string='Reader Code')
    card_id = fields.Integer(string='Card ID')
    card_code = fields.Char(string='Card Code')
    user_id = fields.Integer(string='User ID')
    user_code = fields.Char(string='User Code')
    door_id = fields.Char(string='Door ID')
    door_name = fields.Char(string='Door Name')
    result = fields.Char(string='Result')
    reason = fields.Char(string='Reason')
    held_open_seconds = fields.Integer(string='Held Open Seconds')
```

### 2.5 CCTV Events (`nebular.cctv.event`)

```python
class NebularCCTVEvent(models.Model):
    _name = 'nebular.cctv.event'
    _description = 'CCTV Events'
    _rec_name = 'camera_name'

    id = fields.Integer(string='ID', readonly=True)
    event_id = fields.Many2one('nebular.event', string='Event', required=True, ondelete='cascade')
    camera_id = fields.Integer(string='Camera ID')
    camera_code = fields.Char(string='Camera Code')
    camera_name = fields.Char(string='Camera Name')
    location = fields.Char(string='Location')
    vendor = fields.Char(string='Vendor')
    model = fields.Char(string='Model')
    ip = fields.Char(string='IP Address')
    channel = fields.Integer(string='Channel')
    analytic_type = fields.Char(string='Analytic Type')
    count = fields.Integer(string='Count')
    face_id = fields.Char(string='Face ID')
    uri = fields.Char(string='URI')
    snapshot_url = fields.Char(string='Snapshot URL')
    recording = fields.Boolean(string='Recording')
```

### 2.6 Gate Events (`nebular.gate.event`)

```python
class NebularGateEvent(models.Model):
    _name = 'nebular.gate.event'
    _description = 'Gate Events'
    _rec_name = 'name'

    id = fields.Integer(string='ID', readonly=True)
    event_id = fields.Many2one('nebular.event', string='Event', required=True, ondelete='cascade')
    gate_id = fields.Integer(string='Gate ID')
    gate_code = fields.Char(string='Gate Code')
    name = fields.Char(string='Name')
    location = fields.Char(string='Location')
    status = fields.Char(string='Status')
    vehicle_plate = fields.Char(string='Vehicle Plate')
    trigger = fields.Char(string='Trigger')
    anpr_conf = fields.Float(string='ANPR Confidence')
```

### 2.7 Public Address Events (`nebular.pa.event`)

```python
class NebularPAEvent(models.Model):
    _name = 'nebular.pa.event'
    _description = 'Public Address Events'
    _rec_name = 'zone_name'

    id = fields.Integer(string='ID', readonly=True)
    event_id = fields.Many2one('nebular.event', string='Event', required=True, ondelete='cascade')
    zone_id = fields.Integer(string='Zone ID')
    zone_code = fields.Char(string='Zone Code')
    zone_name = fields.Char(string='Zone Name')
    location = fields.Char(string='Location')
    volume = fields.Integer(string='Volume')
    message = fields.Text(string='Message')
    announcement_id = fields.Char(string='Announcement ID')
    script = fields.Char(string='Script')
    duration_sec = fields.Integer(string='Duration (seconds)')
```

### 2.8 Presence Events (`nebular.presence.event`)

```python
class NebularPresenceEvent(models.Model):
    _name = 'nebular.presence.event'
    _description = 'Presence Events'
    _rec_name = 'name'

    id = fields.Integer(string='ID', readonly=True)
    event_id = fields.Many2one('nebular.event', string='Event', required=True, ondelete='cascade')
    sensor_id = fields.Integer(string='Sensor ID')
    sensor_code = fields.Char(string='Sensor Code')
    name = fields.Char(string='Name')
    location = fields.Char(string='Location')
    vendor = fields.Char(string='Vendor')
    model = fields.Char(string='Model')
    type = fields.Char(string='Type')  # pir/microwave/ultrasonic/camera
    count = fields.Integer(string='Count')
    occupancy = fields.Boolean(string='Occupancy')
```

## 3. Marker System Models

### 3.1 Marker Model (`nebular.marker`)

```python
class NebularMarker(models.Model):
    _name = 'nebular.marker'
    _description = 'Interactive Floor Plan Markers'
    _order = 'floor_id, name'
    _rec_name = 'name'

    # Basic Information
    name = fields.Char(string='Marker Name', required=True, index=True)
    title = fields.Char(string='Title', required=True)
    subtitle = fields.Char(string='Subtitle')
    description = fields.Text(string='Description')
    public_address = fields.Text(string='Public Address')
  
    # Marker Type
    marker_type = fields.Selection([
        ('fire', 'Fire System'),
        ('door', 'Door'),
        ('gate', 'Gate'),
        ('camera', 'Camera'),
        ('people', 'People Counter'),
        ('sensor', 'Sensor'),
        ('emergency', 'Emergency'),
        ('person', 'Person')
    ], string='Marker Type', required=True, index=True)
  
    # Position on floor plan
    position_x = fields.Float(string='Position X', required=True, digits=(10, 2))
    position_y = fields.Float(string='Position Y', required=True, digits=(10, 2))
    position_x_percent = fields.Float(string='Position X (%)', digits=(5, 2))
    position_y_percent = fields.Float(string='Position Y (%)', digits=(5, 2))
  
    # Status and Alert
    status = fields.Char(string='Status', required=True, index=True)
    is_alert = fields.Boolean(string='Is Alert', default=False, index=True)
    alert_timestamp = fields.Datetime(string='Alert Timestamp')
  
    # Optional fields
    count = fields.Integer(string='Count')
    source = fields.Char(string='Source')
  
    # Location References
    building_id = fields.Many2one('nebular.building', string='Building', required=True, ondelete='cascade')
    zone_id = fields.Many2one('nebular.zone', string='Zone', required=True, ondelete='cascade')
    floor_id = fields.Many2one('nebular.floor', string='Floor', required=True, ondelete='cascade')
    zone_display = fields.Char(string='Zone Display')
  
    # Computed fields
    display_name = fields.Char(string='Display Name', compute='_compute_display_name', store=True)
    marker_color = fields.Char(string='Marker Color', compute='_compute_marker_style')
    marker_icon = fields.Char(string='Marker Icon', compute='_compute_marker_style')
  
    @api.depends('title', 'name', 'marker_type')
    def _compute_display_name(self):
        for marker in self:
            marker.display_name = f"[{marker.marker_type.upper()}] {marker.title} ({marker.name})"
  
    @api.depends('marker_type')
    def _compute_marker_style(self):
        """Compute marker color and icon based on type"""
        style_map = {
            'fire': {'color': '#ff4444', 'icon': '🔥'},
            'door': {'color': '#4CAF50', 'icon': '🚪'},
            'gate': {'color': '#FF9800', 'icon': '🚧'},
            'camera': {'color': '#2196F3', 'icon': '📹'},
            'people': {'color': '#9C27B0', 'icon': '👤'},
            'person': {'color': '#9C27B0', 'icon': '👤'},
            'sensor': {'color': '#607D8B', 'icon': '📡'},
            'emergency': {'color': '#F44336', 'icon': '🚨'},
        }
    
        for marker in self:
            style = style_map.get(marker.marker_type, {'color': '#000000', 'icon': '❓'})
            marker.marker_color = style['color']
            marker.marker_icon = style['icon']
```

## 4. System Monitoring Models

### 4.1 System Model (`nebular.system`)

```python
class NebularSystem(models.Model):
    _name = 'nebular.system'
    _description = 'System Monitoring'
    _order = 'title'
    _rec_name = 'title'

    # Basic Information
    title = fields.Char(string='System Title', required=True, index=True)
    icon_name = fields.Char(string='Icon Name', required=True)
    icon_color = fields.Char(string='Icon Color', required=True)
  
    # Relationships
    metric_ids = fields.One2many('nebular.system.metric', 'system_id', string='Metrics')
  
    # Computed fields
    has_alerts = fields.Boolean(string='Has Alerts', compute='_compute_has_alerts', store=True)
    alert_count = fields.Integer(string='Alert Count', compute='_compute_alert_count', store=True)
  
    @api.depends('metric_ids.is_alert')
    def _compute_has_alerts(self):
        for system in self:
            system.has_alerts = any(metric.is_alert for metric in system.metric_ids)
  
    @api.depends('metric_ids.is_alert')
    def _compute_alert_count(self):
        for system in self:
            system.alert_count = len(system.metric_ids.filtered('is_alert'))

class NebularSystemMetric(models.Model):
    _name = 'nebular.system.metric'
    _description = 'System Metrics'
    _order = 'system_id, key'
    _rec_name = 'key'

    # Basic Information
    key = fields.Char(string='Metric Key', required=True, index=True)
    value = fields.Char(string='Metric Value', required=True)
    is_alert = fields.Boolean(string='Is Alert', default=False, index=True)
  
    # Relationships
    system_id = fields.Many2one('nebular.system', string='System', required=True, ondelete='cascade')
  
    # Computed fields
    display_name = fields.Char(string='Display Name', compute='_compute_display_name', store=True)
  
    @api.depends('key', 'value', 'system_id.title')
    def _compute_display_name(self):
        for metric in self:
            metric.display_name = f"{metric.system_id.title} - {metric.key}: {metric.value}"
```

## 5. Security and Access Control

### Security Groups

```python
# Security groups to be defined in security/ir.model.access.csv
# and security/nebular_security.xml

# Groups:
# - nebular_user: Basic user access
# - nebular_manager: Manager access
# - nebular_admin: Full administrative access
```

### Record Rules

```xml
<!-- Example record rules for multi-building access -->
<record id="nebular_building_user_rule" model="ir.rule">
    <field name="name">Building Access Rule</field>
    <field name="model_id" ref="model_nebular_building"/>
    <field name="groups" eval="[(4, ref('nebular_group_user'))]"/>
    <field name="domain_force">[('id', 'in', user.building_ids.ids)]</field>
</record>
```

## 6. Data Migration Considerations

### Migration Scripts

1. **Building Hierarchy**: Import buildings → zones → floors → rooms → doors → devices
2. **Relationships**: Ensure proper foreign key relationships
3. **Status Mapping**: Map TypeScript status enums to Odoo selections
4. **Computed Fields**: Trigger recomputation after data import
5. **Constraints**: Handle unique constraints during import

### Data Validation

- Validate geographic hierarchy integrity
- Check position coordinates are within valid ranges
- Ensure status values match defined selections
- Verify timestamp formats and timezone handling

This mapping provides a comprehensive foundation for converting the TypeScript-based Nebular Dashboard System to Odoo models while maintaining data integrity and business logic.
