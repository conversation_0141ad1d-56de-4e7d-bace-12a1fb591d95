# Class Diagram - Alert Dashboard System

## Overview

This document presents a comprehensive class diagram showing the relationships between all entities in the Alert Dashboard System, designed for Odoo model implementation.

## Entity Relationship Diagram

```mermaid
erDiagram
    BUILDING ||--o{ ZONE : contains
    BUILDING ||--o{ FLOOR : "has floors"
    BUILDING ||--o{ EVENT : "generates events"
    BUILDING ||--o{ MARKER : "has markers"
    
    ZONE ||--o{ FLOOR : contains
    ZONE ||--o{ EVENT : "generates events"
    ZONE ||--o{ MARKER : "has markers"
    ZONE }o--|| BUILDING : "belongs to"
    
    FLOOR ||--o{ ROOM : contains
    FLOOR ||--o{ EVENT : "generates events"
    FLOOR ||--o{ MARKER : "has markers"
    FLOOR }o--|| ZONE : "belongs to"
    FLOOR }o--|| BUILDING : "belongs to"
    
    ROOM ||--o{ DOOR : contains
    ROOM }o--|| FLOOR : "belongs to"
    ROOM }o--|| ZONE : "belongs to (computed)"
    ROOM }o--|| BUILDING : "belongs to (computed)"
    
    DOOR }o--|| ROOM : "belongs to"
    DOOR }o--|| FLOOR : "belongs to (computed)"
    DOOR }o--|| ZONE : "belongs to (computed)"
    DOOR }o--|| BUILDING : "belongs to (computed)"
    
    EVENT }o--|| BUILDING : "occurs in"
    EVENT }o--|| ZONE : "occurs in"
    EVENT }o--|| FLOOR : "occurs in"
    
    MARKER }o--|| BUILDING : "positioned in"
    MARKER }o--|| ZONE : "positioned in"
    MARKER }o--|| FLOOR : "positioned in"
    
    SYSTEM ||--o{ METRIC : "has metrics"
    
    BUILDING {
        int id PK
        string name
        string short_code UK
        string address
        text description
        int total_floors "computed"
        int total_rooms "computed"
        int total_doors "computed"
        boolean is_active
        datetime created_at
        datetime updated_at
    }
    
    ZONE {
        int id PK
        string name
        string zone_code
        text description
        int building_id FK
        int total_floors "computed"
        int total_rooms "computed"
        int total_doors "computed"
        boolean is_active
        datetime created_at
        datetime updated_at
    }
    
    FLOOR {
        int id PK
        string name
        int level
        string floor_code
        string floor_plan_url
        int building_id FK
        int zone_id FK
        int total_rooms "computed"
        int total_doors "computed"
        boolean is_active
        datetime created_at
        datetime updated_at
    }
    
    ROOM {
        int id PK
        string name
        string room_code
        string room_type
        int capacity
        float area
        int floor_id FK
        int building_id "computed"
        int zone_id "computed"
        int total_doors "computed"
        boolean is_active
        datetime created_at
        datetime updated_at
    }
    
    DOOR {
        int id PK
        string name
        string door_code UK
        string door_type
        string access_level
        string status
        int room_id FK
        int floor_id "computed"
        int building_id "computed"
        int zone_id "computed"
        boolean is_active
        boolean is_alert "computed"
        datetime last_status_change
        datetime created_at
        datetime updated_at
    }
    
    EVENT {
        int id PK
        string name
        string event_code
        string device_type
        string status
        text description
        text public_address
        boolean is_alert
        datetime timestamp
        int building_id FK
        int zone_id FK
        int floor_id FK
    }
    
    MARKER {
        int id PK
        string name
        string title
        string subtitle
        text description
        text public_address
        string marker_type
        float position_x
        float position_y
        float position_x_percent
        float position_y_percent
        string status
        boolean is_alert
        datetime alert_timestamp
        int count
        string source
        int building_id FK
        int zone_id FK
        int floor_id FK
        string zone_display
        string marker_color "computed"
        string marker_icon "computed"
    }
    
    SYSTEM {
        int id PK
        string title
        string icon_name
        string icon_color
        boolean has_alerts "computed"
        int alert_count "computed"
    }
    
    METRIC {
        int id PK
        string key
        string value
        boolean is_alert
        int system_id FK
    }
```

## Detailed Class Structure

### 1. Geographic Hierarchy Classes

#### Building Class
```python
class AlertBuilding:
    # Primary Key
    id: int (PK)
    
    # Basic Information
    name: str (required, indexed, unique)
    short_code: str (required, indexed, unique, max_length=10)
    address: str (text)
    description: str (text)
    
    # Computed Statistics
    total_floors: int (computed from zones.floors)
    total_rooms: int (computed from zones.floors.rooms)
    total_doors: int (computed from zones.floors.rooms.doors)
    
    # Status
    is_active: bool (default=True)
    
    # Audit Fields
    created_at: datetime (auto)
    updated_at: datetime (auto)
    
    # Relationships
    zone_ids: One2many('alert.zone', 'building_id')
    floor_ids: One2many('alert.floor', 'building_id')
    event_ids: One2many('alert.event', 'building_id')
    marker_ids: One2many('alert.marker', 'building_id')
    
    # Methods
    _compute_totals()
    _check_constraints()
```

#### Zone Class
```python
class AlertZone:
    # Primary Key
    id: int (PK)
    
    # Basic Information
    name: str (required, indexed)
    zone_code: str (required, indexed, max_length=10)
    description: str (text)
    
    # Foreign Keys
    building_id: int (FK to alert.building, required, cascade)
    
    # Computed Statistics
    total_floors: int (computed from floor_ids)
    total_rooms: int (computed from floor_ids.room_ids)
    total_doors: int (computed from floor_ids.room_ids.door_ids)
    
    # Status
    is_active: bool (default=True)
    
    # Audit Fields
    created_at: datetime (auto)
    updated_at: datetime (auto)
    
    # Computed Display
    display_name: str (computed)
    
    # Relationships
    building_id: Many2one('alert.building')
    floor_ids: One2many('alert.floor', 'zone_id')
    event_ids: One2many('alert.event', 'zone_id')
    marker_ids: One2many('alert.marker', 'zone_id')
    
    # Constraints
    _sql_constraints: [('zone_code_building_unique', 'UNIQUE(zone_code, building_id)')]
```

#### Floor Class
```python
class AlertFloor:
    # Primary Key
    id: int (PK)
    
    # Basic Information
    name: str (required, indexed)
    level: int (required)
    floor_code: str (required, indexed, max_length=10)
    floor_plan_url: str (default='/plans/floorPlan-1.png')
    
    # Foreign Keys
    building_id: int (FK to alert.building, required, cascade)
    zone_id: int (FK to alert.zone, required, cascade)
    
    # Computed Statistics
    total_rooms: int (computed from room_ids)
    total_doors: int (computed from room_ids.door_ids)
    
    # Status
    is_active: bool (default=True)
    
    # Audit Fields
    created_at: datetime (auto)
    updated_at: datetime (auto)
    
    # Computed Display
    display_name: str (computed)
    
    # Relationships
    building_id: Many2one('alert.building')
    zone_id: Many2one('alert.zone')
    room_ids: One2many('alert.room', 'floor_id')
    event_ids: One2many('alert.event', 'floor_id')
    marker_ids: One2many('alert.marker', 'floor_id')
    
    # Constraints
    _sql_constraints: [
        ('floor_code_building_unique', 'UNIQUE(floor_code, building_id)'),
        ('level_zone_unique', 'UNIQUE(level, zone_id)')
    ]
```

#### Room Class
```python
class AlertRoom:
    # Primary Key
    id: int (PK)
    
    # Basic Information
    name: str (required, indexed)
    room_code: str (required, indexed, max_length=20)
    room_type: Selection (required, default='office')
    capacity: int (optional, positive)
    area: float (optional, positive, digits=(10,2))
    
    # Foreign Keys
    floor_id: int (FK to alert.floor, required, cascade)
    
    # Related Fields (computed from floor)
    building_id: int (related='floor_id.building_id', stored, readonly)
    zone_id: int (related='floor_id.zone_id', stored, readonly)
    
    # Computed Statistics
    total_doors: int (computed from door_ids)
    
    # Status
    is_active: bool (default=True)
    
    # Audit Fields
    created_at: datetime (auto)
    updated_at: datetime (auto)
    
    # Computed Display
    display_name: str (computed)
    
    # Relationships
    floor_id: Many2one('alert.floor')
    building_id: Many2one('alert.building', related)
    zone_id: Many2one('alert.zone', related)
    door_ids: One2many('alert.door', 'room_id')
    
    # Constraints
    _sql_constraints: [
        ('room_code_floor_unique', 'UNIQUE(room_code, floor_id)'),
        ('capacity_positive', 'CHECK(capacity >= 0)'),
        ('area_positive', 'CHECK(area >= 0)')
    ]
```

#### Door Class
```python
class AlertDoor:
    # Primary Key
    id: int (PK)
    
    # Basic Information
    name: str (required, indexed)
    door_code: str (required, indexed, unique, max_length=20)
    door_type: Selection (required, default='standard')
    access_level: Selection (required, default='public')
    
    # Status Information
    status: Selection (required, default='closed', indexed)
    last_status_change: datetime (default=now)
    
    # Foreign Keys
    room_id: int (FK to alert.room, required, cascade)
    
    # Related Fields (computed from room)
    floor_id: int (related='room_id.floor_id', stored, readonly)
    building_id: int (related='room_id.building_id', stored, readonly)
    zone_id: int (related='room_id.zone_id', stored, readonly)
    
    # Status
    is_active: bool (default=True)
    is_alert: bool (computed from status and type)
    
    # Audit Fields
    created_at: datetime (auto)
    updated_at: datetime (auto)
    
    # Computed Display
    display_name: str (computed)
    
    # Relationships
    room_id: Many2one('alert.room')
    floor_id: Many2one('alert.floor', related)
    building_id: Many2one('alert.building', related)
    zone_id: Many2one('alert.zone', related)
    
    # Constraints
    _sql_constraints: [('door_code_unique', 'UNIQUE(door_code)')]
    
    # Methods
    _compute_is_alert()
    write() # Override to update last_status_change
```

### 2. Event System Classes

#### Event Class
```python
class AlertEvent:
    # Primary Key
    id: int (PK)
    
    # Basic Information
    name: str (required, indexed)
    event_code: str (required, indexed)
    description: str (text)
    public_address: str (text)
    
    # Event Classification
    device_type: Selection (required, indexed)
    status: str (required, indexed) # Flexible for device-specific statuses
    is_alert: bool (default=False, indexed)
    
    # Timing
    timestamp: datetime (required, default=now)
    
    # Location References
    building_id: int (FK to alert.building, required, cascade)
    zone_id: int (FK to alert.zone, required, cascade)
    floor_id: int (FK to alert.floor, required, cascade)
    
    # Computed Display
    display_name: str (computed)
    location_path: str (computed)
    
    # Relationships
    building_id: Many2one('alert.building')
    zone_id: Many2one('alert.zone')
    floor_id: Many2one('alert.floor')
    
    # Methods
    generate_event_code(device_type, sequence_id)
    _compute_display_name()
    _compute_location_path()
```

### 3. Marker System Classes

#### Marker Class
```python
class AlertMarker:
    # Primary Key
    id: int (PK)
    
    # Basic Information
    name: str (required, indexed)
    title: str (required)
    subtitle: str
    description: str (text)
    public_address: str (text)
    
    # Marker Configuration
    marker_type: Selection (required, indexed)
    
    # Position Information
    position_x: float (required, digits=(10,2))
    position_y: float (required, digits=(10,2))
    position_x_percent: float (digits=(5,2))
    position_y_percent: float (digits=(5,2))
    
    # Status and Alert
    status: str (required, indexed)
    is_alert: bool (default=False, indexed)
    alert_timestamp: datetime
    
    # Optional Information
    count: int
    source: str
    zone_display: str
    
    # Location References
    building_id: int (FK to alert.building, required, cascade)
    zone_id: int (FK to alert.zone, required, cascade)
    floor_id: int (FK to alert.floor, required, cascade)
    
    # Computed Display and Style
    display_name: str (computed)
    marker_color: str (computed)
    marker_icon: str (computed)
    
    # Relationships
    building_id: Many2one('alert.building')
    zone_id: Many2one('alert.zone')
    floor_id: Many2one('alert.floor')
    
    # Methods
    _compute_display_name()
    _compute_marker_style()
```

### 4. System Monitoring Classes

#### System Class
```python
class AlertSystem:
    # Primary Key
    id: int (PK)
    
    # Basic Information
    title: str (required, indexed)
    icon_name: str (required)
    icon_color: str (required)
    
    # Computed Alert Information
    has_alerts: bool (computed from metrics)
    alert_count: int (computed from metrics)
    
    # Relationships
    metric_ids: One2many('alert.system.metric', 'system_id')
    
    # Methods
    _compute_has_alerts()
    _compute_alert_count()
```

#### System Metric Class
```python
class AlertSystemMetric:
    # Primary Key
    id: int (PK)
    
    # Basic Information
    key: str (required, indexed)
    value: str (required)
    is_alert: bool (default=False, indexed)
    
    # Foreign Keys
    system_id: int (FK to alert.system, required, cascade)
    
    # Computed Display
    display_name: str (computed)
    
    # Relationships
    system_id: Many2one('alert.system')
    
    # Methods
    _compute_display_name()
```

## Relationship Summary

### Primary Relationships (One-to-Many)
1. **Building → Zone** (1:N)
2. **Building → Floor** (1:N) 
3. **Zone → Floor** (1:N)
4. **Floor → Room** (1:N)
5. **Room → Door** (1:N)
6. **System → Metric** (1:N)

### Reference Relationships (Many-to-One)
1. **Event → Building, Zone, Floor** (N:1 each)
2. **Marker → Building, Zone, Floor** (N:1 each)

### Computed Relationships
1. **Room → Building, Zone** (computed from Floor)
2. **Door → Floor, Building, Zone** (computed from Room)

### Key Constraints
1. **Unique Constraints**: Building.short_code, Door.door_code
2. **Composite Unique**: Zone(zone_code, building_id), Floor(floor_code, building_id), Room(room_code, floor_id)
3. **Hierarchical Integrity**: All geographic entities maintain proper parent-child relationships
4. **Cascade Deletes**: Deleting parent entities cascades to children

This class diagram provides a complete view of the system architecture, showing how all entities relate to each other and the computed fields that maintain data consistency across the hierarchy.