# Nebular Event Management System - API Documentation

## Overview

The Nebular Event Management System provides RESTful API endpoints for managing events across multiple security and building automation systems. This API supports six system types: Fire Alarm, Access Control, CCTV, Gate Barrier, Public Address, and Presence Detection systems.

**Base URL:** `http://localhost:8088`
**API Version:** v1
**Authentication:** Public (no authentication required)
**Content-Type:** `application/json`
**CORS:** Enabled for all origins

---

## System Codes

The API supports the following system codes:

| System Code  | Description               | Event Types                          |
| ------------ | ------------------------- | ------------------------------------ |
| `fire`     | Fire Alarm System         | alarm, fault, test, restore          |
| `access`   | Access Control System     | entry, exit, denied, forced          |
| `cctv`     | CCTV System               | motion, recording, offline, online   |
| `gate`     | Gate Barrier System       | open, close, vehicle_detected, fault |
| `pa`       | Public Address System     | announcement, emergency, test, fault |
| `presence` | Presence Detection System | occupied, vacant, motion, fault      |

---

## API Endpoints

### 1. Get Events List

Retrieve a paginated list of events with optional filtering.

**Endpoint:** `GET /api/v1/events`

#### Query Parameters

| Parameter       | Type    | Required | Description                           | Example                          |
| --------------- | ------- | -------- | ------------------------------------- | -------------------------------- |
| `system_code` | string  | No       | Filter by system type                 | `fire`, `access`, `cctv`   |
| `event_type`  | string  | No       | Filter by event type                  | `alarm`, `entry`, `motion` |
| `building_id` | integer | No       | Filter by building ID                 | `1`                            |
| `floor_id`    | integer | No       | Filter by floor ID                    | `2`                            |
| `date_from`   | string  | No       | Start date (ISO-8601)                 | `2024-01-01T00:00:00Z`         |
| `date_to`     | string  | No       | End date (ISO-8601)                   | `2024-01-31T23:59:59Z`         |
| `limit`       | integer | No       | Max results (default: 100, max: 1000) | `50`                           |
| `offset`      | integer | No       | Pagination offset (default: 0)        | `100`                          |
| `search`      | string  | No       | Search in message field               | `alarm`                        |

#### Example Request

```bash
curl -X GET "http://localhost:8070/api/v1/events?system_code=fire&limit=10&offset=0" \
  -H "Content-Type: application/json"
```

#### Example Response

```json
{
  "ResponseCode": "000",
  "ResponseMessage": "Success",
  "ResponseMessageAR": "نجح",
  "ErrorMessage": "",
  "TotalCount": 150,
  "Events": [
    {
      "buildingId": 1,
      "buildingCode": "HQ-001",
      "floorId": 2,
      "floorCode": "F2",
      "systemCode": "fire",
      "systemName": "Fire Alarm System",
      "eventType": "alarm",
      "datetime": "2024-01-15T10:30:00Z",
      "message": "Fire alarm activated in Zone 1",
      "sourceEventCode": "FP-001",
      "sourceState": "active",
      "state": "alarm",
      "Data": {
        "panelId": 1,
        "panelCode": "FP-01",
        "panelName": "Fire Panel 1",
        "zone": "Zone-1",
        "loop": "Loop-1",
        "nodeId": 15,
        "nodeCode": "FD-015",
        "address": "15"
      }
    }
  ]
}
```

---

### 2. Get Single Event

Retrieve a specific event by its ID.

**Endpoint:** `GET /api/v1/event/{event_id}`

#### Path Parameters

| Parameter    | Type    | Required | Description             |
| ------------ | ------- | -------- | ----------------------- |
| `event_id` | integer | Yes      | Unique event identifier |

#### Example Request

```bash
curl -X GET "http://localhost:8070/api/v1/event/123" \
  -H "Content-Type: application/json"
```

#### Example Response

```json
{
  "ResponseCode": "000",
  "ResponseMessage": "Success",
  "ResponseMessageAR": "نجح",
  "ErrorMessage": "",
  "buildingId": 1,
  "buildingCode": "HQ-001",
  "floorId": 2,
  "floorCode": "F2",
  "systemCode": "access",
  "systemName": "Access Control System",
  "eventType": "entry",
  "datetime": "2024-01-15T10:30:00Z",
  "message": "Card access granted",
  "sourceEventCode": "AC-001",
  "sourceState": "open",
  "state": "normal",
  "Data": {
    "controllerId": 1,
    "controllerCode": "AC-01",
    "controllerName": "Main Entrance Controller",
    "readerId": 5,
    "readerCode": "RD-005",
    "cardId": 1234,
    "cardCode": "CD-12345",
    "userId": 100,
    "userCode": "USR-0100",
    "doorId": "DR-001",
    "doorName": "Main Entrance",
    "result": "granted",
    "reason": "schedule",
    "heldOpenSeconds": 10
  }
}
```

---

### 3. Create Event

Create a new event in the system.

**Endpoint:** `POST /api/v1/event`

#### Request Body

The request body should contain a JSON object with the event data following the common event envelope structure.

#### Required Fields

| Field          | Type    | Description                                          |
| -------------- | ------- | ---------------------------------------------------- |
| `buildingId` | integer | Building identifier                                  |
| `systemCode` | string  | System type (fire, access, cctv, gate, pa, presence) |
| `eventType`  | string  | Event type specific to the system                    |
| `datetime`   | string  | Event timestamp in ISO-8601 format                   |
| `message`    | string  | Event description message                            |

#### Example Request

```bash
curl -X POST "http://localhost:8070/api/v1/event" \
  -H "Content-Type: application/json" \
  -d '{
    "buildingId": 1,
    "buildingCode": "HQ-001",
    "floorId": 2,
    "floorCode": "F2",
    "systemCode": "fire",
    "eventType": "alarm",
    "datetime": "2024-01-15T10:30:00Z",
    "message": "Fire alarm detected in server room",
    "sourceEventCode": "FP-001",
    "sourceState": "active",
    "state": "alarm",
    "Data": {
      "panelId": 1,
      "panelCode": "FP-01",
      "panelName": "Fire Panel 1",
      "zone": "Zone-5",
      "loop": "Loop-2",
      "nodeId": 25,
      "nodeCode": "FD-025",
      "address": "25"
    }
  }'
```

#### Example Response

```json
{
  "ResponseCode": "000",
  "ResponseMessage": "Event created successfully",
  "ResponseMessageAR": "تم إنشاء الحدث بنجاح",
  "ErrorMessage": "",
  "buildingId": 1,
  "buildingCode": "HQ-001",
  "floorId": 2,
  "floorCode": "F2",
  "systemCode": "fire",
  "systemName": "Fire Alarm System",
  "eventType": "alarm",
  "datetime": "2024-01-15T10:30:00Z",
  "message": "Fire alarm detected in server room",
  "sourceEventCode": "FP-001",
  "sourceState": "active",
  "state": "alarm",
  "Data": {
    "panelId": 1,
    "panelCode": "FP-01",
    "panelName": "Fire Panel 1",
    "zone": "Zone-5",
    "loop": "Loop-2",
    "nodeId": 25,
    "nodeCode": "FD-025",
    "address": "25"
  }
}
```

---

### 4. Fast Test Event Generation

Generate test events quickly with minimal parameters for testing purposes.

**Endpoint:** `GET /api/v1/fast_test`

#### Query Parameters

| Parameter       | Type   | Required | Description                            |
| --------------- | ------ | -------- | -------------------------------------- |
| `system_code` | string | Yes      | System type to generate test event for |

#### Example Request

```bash
curl -X GET "http://localhost:8070/api/v1/fast_test?system_code=fire" \
  -H "Content-Type: application/json"
```

#### Example Response

```json
{
  "ResponseCode": "000",
  "ResponseMessage": "Test event created successfully",
  "ResponseMessageAR": "تم إنشاء حدث الاختبار بنجاح",
  "ErrorMessage": "",
  "buildingId": 1,
  "buildingCode": "HQ-001",
  "floorId": 3,
  "floorCode": "F3",
  "systemCode": "fire",
  "systemName": "Fire Alarm System",
  "eventType": "test",
  "datetime": "2024-01-15T14:25:30Z",
  "message": "Test fire event generated automatically",
  "sourceEventCode": "FP-456",
  "sourceState": "active",
  "state": "test",
  "Data": {
    "panelId": 5,
    "panelCode": "FP-05",
    "panelName": "Fire Panel 5",
    "zone": "Zone-12",
    "loop": "Loop-3",
    "nodeId": 78,
    "nodeCode": "FD-078",
    "address": "78"
  }
}
```

---

## System-Specific Data Fields

Each system type includes specific data fields in the `Data` object:

### Fire Alarm System (`fire`)

| Field         | Type    | Description           |
| ------------- | ------- | --------------------- |
| `panelId`   | integer | Fire panel identifier |
| `panelCode` | string  | Fire panel code       |
| `panelName` | string  | Fire panel name       |
| `zone`      | string  | Fire zone identifier  |
| `loop`      | string  | Fire loop identifier  |
| `nodeId`    | integer | Fire device node ID   |
| `nodeCode`  | string  | Fire device code      |
| `address`   | string  | Device address        |

### Access Control System (`access`)

| Field               | Type    | Description                            |
| ------------------- | ------- | -------------------------------------- |
| `controllerId`    | integer | Access controller ID                   |
| `controllerCode`  | string  | Controller code                        |
| `controllerName`  | string  | Controller name                        |
| `readerId`        | integer | Card reader ID                         |
| `readerCode`      | string  | Reader code                            |
| `cardId`          | integer | Access card ID                         |
| `cardCode`        | string  | Card code                              |
| `userId`          | integer | User ID                                |
| `userCode`        | string  | User code                              |
| `doorId`          | string  | Door identifier                        |
| `doorName`        | string  | Door name                              |
| `result`          | string  | Access result (granted/denied/timeout) |
| `reason`          | string  | Access reason                          |
| `heldOpenSeconds` | integer | Door held open duration                |

### CCTV System (`cctv`)

| Field            | Type    | Description       |
| ---------------- | ------- | ----------------- |
| `cameraId`     | integer | Camera identifier |
| `cameraCode`   | string  | Camera code       |
| `cameraName`   | string  | Camera name       |
| `location`     | string  | Camera location   |
| `ip`           | string  | Camera IP address |
| `channel`      | integer | Video channel     |
| `analyticType` | string  | Analytics type    |
| `count`        | integer | Detection count   |
| `recording`    | boolean | Recording status  |

### Gate Barrier System (`gate`)

| Field              | Type    | Description           |
| ------------------ | ------- | --------------------- |
| `gateId`         | integer | Gate identifier       |
| `gateCode`       | string  | Gate code             |
| `gateName`       | string  | Gate name             |
| `status`         | string  | Gate status           |
| `vehiclePlate`   | string  | Vehicle license plate |
| `trigger`        | string  | Trigger type          |
| `anprConfidence` | float   | ANPR confidence level |

### Public Address System (`pa`)

| Field              | Type    | Description         |
| ------------------ | ------- | ------------------- |
| `zoneId`         | integer | PA zone identifier  |
| `zoneCode`       | string  | Zone code           |
| `zoneName`       | string  | Zone name           |
| `volume`         | integer | Volume level        |
| `announcementId` | string  | Announcement ID     |
| `script`         | string  | Announcement script |
| `durationSec`    | integer | Duration in seconds |

### Presence Detection System (`presence`)

| Field          | Type    | Description       |
| -------------- | ------- | ----------------- |
| `sensorId`   | integer | Sensor identifier |
| `sensorCode` | string  | Sensor code       |
| `sensorName` | string  | Sensor name       |
| `sensorType` | string  | Sensor type       |
| `occupancy`  | boolean | Occupancy status  |

---

## Error Handling

The API uses standardized error responses with the following structure:

```json
{
  "ResponseCode": "101",
  "ResponseMessage": "ValidationError",
  "ResponseMessageAR": "خطأ في التحقق",
  "ErrorMessage": "Detailed error description"
}
```

### Common Error Codes

| Code     | Description      | HTTP Status |
| -------- | ---------------- | ----------- |
| `000`  | Success          | 200         |
| `100`  | General Error    | 500         |
| `101`  | Validation Error | 400         |
| `103`  | Unauthorized     | 401         |
| `104`  | Forbidden        | 403         |
| `404`  | Not Found        | 404         |
| `5000` | Custom Error     | 500         |

---

## Integration Examples

### Python Example

```python
import requests
import json
from datetime import datetime

# Base URL
base_url = "http://localhost:8070"

# Create a fire alarm event
event_data = {
    "buildingId": 1,
    "buildingCode": "HQ-001",
    "floorId": 2,
    "floorCode": "F2",
    "systemCode": "fire",
    "eventType": "alarm",
    "datetime": datetime.utcnow().isoformat() + "Z",
    "message": "Fire alarm in server room",
    "sourceEventCode": "FP-001",
    "sourceState": "active",
    "state": "alarm",
    "Data": {
        "panelId": 1,
        "panelCode": "FP-01",
        "panelName": "Fire Panel 1",
        "zone": "Zone-1",
        "loop": "Loop-1",
        "nodeId": 15,
        "nodeCode": "FD-015",
        "address": "15"
    }
}

# Send POST request
response = requests.post(
    f"{base_url}/api/v1/event",
    headers={"Content-Type": "application/json"},
    data=json.dumps(event_data)
)

if response.status_code == 200:
    result = response.json()
    if result["ResponseCode"] == "000":
        print("Event created successfully!")
        print(f"Event ID: {result.get('eventId', 'N/A')}")
    else:
        print(f"Error: {result['ErrorMessage']}")
else:
    print(f"HTTP Error: {response.status_code}")
```

### JavaScript Example

```javascript
// Create an access control event
const eventData = {
    buildingId: 1,
    buildingCode: "HQ-001",
    floorId: 1,
    floorCode: "F1",
    systemCode: "access",
    eventType: "entry",
    datetime: new Date().toISOString(),
    message: "Card access granted",
    sourceEventCode: "AC-001",
    sourceState: "open",
    state: "normal",
    Data: {
        controllerId: 1,
        controllerCode: "AC-01",
        controllerName: "Main Entrance Controller",
        readerId: 5,
        readerCode: "RD-005",
        cardId: 1234,
        cardCode: "CD-12345",
        userId: 100,
        userCode: "USR-0100",
        doorId: "DR-001",
        doorName: "Main Entrance",
        result: "granted",
        reason: "schedule",
        heldOpenSeconds: 10
    }
};

fetch('http://localhost:8070/api/v1/event', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify(eventData)
})
.then(response => response.json())
.then(data => {
    if (data.ResponseCode === "000") {
        console.log('Event created successfully!');
        console.log('Response:', data);
    } else {
        console.error('Error:', data.ErrorMessage);
    }
})
.catch(error => {
    console.error('Network error:', error);
});
```

---

## Rate Limiting and Best Practices

1. **Pagination:** Use `limit` and `offset` parameters for large datasets
2. **Filtering:** Apply appropriate filters to reduce response size
3. **Date Ranges:** Use `date_from` and `date_to` for time-based queries
4. **Error Handling:** Always check `ResponseCode` before processing data
5. **Timestamps:** Use ISO-8601 UTC format for all datetime fields
6. **System Validation:** Ensure `systemCode` and `eventType` combinations are valid

---

## Support

For technical support or questions about the API, please contact the development team or refer to the system documentation.

**API Version:** 1.0
**Last Updated:** January 2024
**Odoo Version:** 18.0
