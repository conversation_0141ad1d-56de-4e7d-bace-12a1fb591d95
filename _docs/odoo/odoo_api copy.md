**Nuplar Realtime Systems — Event Payload & Options (v1)**

This document defines a unified event payload (common envelope) and per‑system options for** **Fire Alarm, Access Control, CCTV, Gate Barrier, Public Address, Presence Sensors.

[]()0) Common Event Envelope (all systems)

**{**
 **"buildingId"**:** **"B-01"**,**
 **"floorId"**:** **"F-03"**,**
 **"systemId"**:** **"fire"**,** **                 **//** **fire|access|cctv|gate|pa|presence
 **"systemName"**:** **"Fire Alarm"**,**
 **"eventType"**:** **"alarm"**,** **               **//** **per-system** **enums** **below
 **"datetime"**:** **"2025-09-18T18:20:59Z"**,** ** **//** **ISO-8601** **UTC
 **"message"**:** **"short human-readable message"**,**
 **"state"**:** **"online"**,** **                 ** **//** **optional** **current** **state** **(per-system)**
 **"command"**:** **null**,** **                     **//** **optional** **command** **ack** **name** **(if** **any)
 **"data"**:** **{** **}** **                         ** **//** **system-specific** **fields
}

**Notes** - Always use** **UTC** **timestamps in the API. UI converts to local. - Keep** **nodeIdstable across vendors for easy joins/drill-down. - Derive** **severity** **from** **eventType** **via a policy table.

[]()1) Fire Alarm (**systemId = "fire"**)

[]()Allowed** **eventType** **values

**•** **Online**

**•** **Offline**

**•** **reset**

**•** **fault**

**•** **other**

**•** **alarm** **(example provided below)**

[]()data** **fields (**filled when** **eventtype** **=** **alarm** **)**

**•** **id** (string)

**•** **name** (string)** **

**•** **zone** (string)** **

**•** **loop** (string)** **

**•** **node** (string)** **

**•** **address** (string)** **

[]()Example (only** **alarm**)**

**{**
 **"buildingId"**:** **"B-01"**,**
 **"floorId"**:** **"F-03"**,**
 **"systemId"**:** **"fire"**,**
 **"systemName"**:** **"Fire Alarm"**,**
 **"eventType"**:** **"alarm"**,**
 **"datetime"**:** **"2025-09-18T18:20:59Z"**,**
 **"message"**:** **"Smoke alarm Zone 1 (Loop 2, Address 17)"**,**
   **"data"**:** **{
   **"id"**:** **"panel-1"**,**
   **"name"**:** **"FA Main"**,**
   **"zone"**:** **"Zone 1"**,**
   **"loop"**:** **"Loop 2"**,**
   **"node"**:** **"detector-17"**,**
   **"address"**:** **"17"
 **}**
}

[]()2) Access Control / Door (**systemId = "access"**)

[]()eventType** **options

**•** **accessGranted**,** **accessDenied,** **doorOpen,** **doorClose,** **heldOpen,** **forcedOpen,** **tamper,** **fault,** **status,** **other

[]()state** **options

**•** **online**,** **offline,** **open,** **close

[]()command** **options

**•** **unlock**,** **lock,** **pulse,** **reset

[]()data** **fields

**•** **doorId**,** **doorName,** **controllerId,** **controllerName,** **vendor,** **model

**•** **cardId**,** **holder,** **result** **(**granted|denied|timeout**)

**•** **reason** (**schedule|invalidCard|expired|antipassback|pinRequired|other**)

**•** **heldOpenSeconds** (integer)

[]()Example

**{**
 **"buildingId"**:** **"B-01"**,**
 **"floorId"**:** **"G"**,**
 **"systemId"**:** **"access"**,**
 **"systemName"**:** **"Access Control"**,**
 **"eventType"**:** **"accessDenied"**,**
 **"datetime"**:** **"2025-09-18T18:20:51Z"**,**
 **"message"**:** **"Card denied at LG-12"**,**
 **"nodeId"**:** **"door:LG-12"**,**
 **"severity"**:** **"info"**,**
 **"state"**:** **"online"**,**
 **"data"**:** **{
   **"doorId"**:** **"LG-12"**,**
   **"doorName"**:** **"Lobby Gate 12"**,**
   **"controllerId"**:** **"ACU-4"**,**
   **"controllerName"**:** **"Main ACU"**,**
   **"vendor"**:** **"Suprema"**,**
   **"model"**:** **"BioStar-2"**,**
   **"cardId"**:** **"CSN-987654"**,**
   **"holder"**:** **"John Doe"**,**
   **"result"**:** **"denied"**,**
   **"reason"**:** **"invalidCard"**,**
   **"heldOpenSeconds"**:** **0
 **}**
}

[]()3) CCTV (**systemId = "cctv"**)

[]()eventType** **options

**•** **alarm**,** **motion,** **smoke,** **fire,** **audioDetection,** **lineCrossing,** **loitering,** **peopleCount,** **faceDetection

[]()command** **options

**•** **popup**,** **ptz,** **reset

[]()data** **fields

**•** **cameraId**,** **cameraName

**•** **analyticType** (**motion|smoke|fire|audio|lineCrossing|loitering|peopleCount|face**)

**•** **count** (int, for peopleCount),** **faceId** **(string)

**•** **uri** (RTSP/HTTP),** **snapshotUrl** **(URL),** **recording** **(bool),** **lastHeartbeat** **(ISO-8601)

[]()Example

**{**
 **"buildingId"**:** **"B-01"**,**
 **"floorId"**:** **"2F"**,**
 **"systemId"**:** **"cctv"**,**
 **"systemName"**:** **"CCTV"**,**
 **"eventType"**:** **"motion"**,**
 **"datetime"**:** **"2025-09-18T18:20:40Z"**,**
 **"message"**:** **"Motion detected CAM-2F-018"**,**
 **"data"**:** **{
   **"cameraId"**:** **"CAM-2F-018"**,**
   **"cameraName"**:** **"Corridor 2F SE"**,**
   **"analyticType"**:** **"motion"**,**
   **"count"**:** **0**,**
   **"uri"**:** **"**rtsp**://example/stream1"**,**
   **"recording"**:** **true**,**
   **"lastHeartbeat"**:** **"2025-09-18T18:20:35Z"
 **}**
}

[]()4) Gate Barrier (**systemId = "gate"**)

[]()eventType** **options

**•** **status**,** **open,** **close,** **blocked,** **fault,** **tamper,** **other

[]()state** **options

**•** **connected**,** **disconnected,** **open,** **close,** **blocked,** **fault

[]()command** **options

**•** **open**,** **close,** **stop,** **reset

[]()data** **fields

**•** **gateId**,** **gateName,** **vehiclePlate,** **trigger** **(e.g.,** **accessGranted),** **anprConfidence** **(0–1)

[]()Example

**{**
 **"buildingId"**:** **"B-01"**,**
 **"floorId"**:** **"G"**,**
 **"systemId"**:** **"gate"**,**
 **"systemName"**:** **"Gate Barrier"**,**
 **"eventType"**:** **"open"**,**
 **"datetime"**:** **"2025-09-18T18:20:35Z"**,**
 **"message"**:** **"Gate West Entry opened"**,**
 **"nodeId"**:** **"gate:W1"**,**
 **"severity"**:** **"info"**,**
 **"state"**:** **"open"**,**
 **"data"**:** **{
   **"gateId"**:** **"GATE-W1"**,**
   **"gateName"**:** **"West Entry"**,**
   **"vehiclePlate"**:** **"ABC-1234"**,**
   **"anprConfidence"**:** **0.92**,**
   **"trigger"**:** **"accessGranted"
 **}**
}

[]()5) Public Address (**systemId = "pa"**)

[]()eventType** **options

**•** **announcementStarted**,** **announcementEnded,** **fault,** **status,** **other

[]()state** **options

**•** **connected**,** **disconnected,** **announcing,** **idle,** **fault

[]()command** **options

**•** **announce**,** **stop,** **reset

[]()data** **fields

**•** **zoneId**,** **zoneName,** **announcementId,** **script,** **durationSec

[]()Example

**{**
 **"buildingId"**:** **"B-01"**,**
 **"floorId"**:** **"G"**,**
 **"systemId"**:** **"pa"**,**
 **"systemName"**:** **"Public Address"**,**
 **"eventType"**:** **"announcementStarted"**,**
 **"datetime"**:** **"2025-09-18T18:20:30Z"**,**
 **"message"**:** **"Fire evacuation message started (Lobby Zone)"**,**
 **"nodeId"**:** **"pa:PA-Z-04"**,**
 **"severity"**:** **"warning"**,**
 **"state"**:** **"announcing"**,**
 **"data"**:** **{
   **"zoneId"**:** **"PA-Z-04"**,**
   **"zoneName"**:** **"Lobby Zone"**,**
   **"announcementId"**:** **"ann-20250918-001"**,**
   **"script"**:** **"fire-evac-en"**,**
   **"durationSec"**:** **45
 **}**
}

[]()6) Presence Sensors (**systemId = "presence"**)

[]()eventType** **options

**•** **presenceDetected**,** **presenceCleared,** **peopleCount,** **fault,** **status,** **other

[]()state** **options

**•** **connected**,** **disconnected,** **occupied,** **clear,** **fault

[]()command** **options

**•** **(usually none)** **reset** if supported

[]()data** **fields

**•** **sensorId**,** **sensorName,** **occupied** **(bool),** **presenceCount** **(int ≥ 0),** **confidence** **(0–1),** **durationSec

[]()Example

**{**
 **"buildingId"**:** **"B-01"**,**
 **"floorId"**:** **"3F"**,**
 **"systemId"**:** **"presence"**,**
 **"systemName"**:** **"Presence Sensors"**,**
 **"eventType"**:** **"presenceDetected"**,**
 **"datetime"**:** **"2025-09-18T18:14:28Z"**,**
 **"message"**:** **"3F Corridor PIR occupied"**,**
 **"nodeId"**:** **"pir:3F-12"**,**
 **"severity"**:** **"info"**,**
 **"state"**:** **"occupied"**,**
 **"data"**:** **{
   **"sensorId"**:** **"PIR-3F-12"**,**
   **"sensorName"**:** **"3F Corridor PIR"**,**
   **"occupied"**:** **true**,**
   **"presenceCount"**:** **2**,**
   **"confidence"**:** **0.93**,**
   **"durationSec"**:** **5
 **}**
}

[]()Implementation Add‑Ons (optional)

**•** **SSE/WebSocket stream** reuses the same envelope for live updates.

**•** **Aggregate snapshot** returns compact per‑system summaries (counts + lastUpdate).

**•** **Commands**: expose** **POST /v1/systems/{systemId}/commands/*** **and emit command acks back on the stream with** **command** **populated.
