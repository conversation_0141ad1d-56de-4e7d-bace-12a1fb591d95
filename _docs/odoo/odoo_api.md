# Nublar Realtime Systems — Event Payload & Options (v1)

This document defines a unified event payload (common envelope) and per-system options for **Fire Alarm, Access Control, CCTV, Gate Barrier, Public Address, and Presence Sensors**.

## Table of Contents

1. [Common Event Envelope](#common-event-envelope)
2. [System-Specific Configurations](#system-specific-configurations)
   - [Fire Alarm System](#fire-alarm-system)
   - [Access Control System](#access-control-system)
   - [CCTV System](#cctv-system)
   - [Gate Barrier System](#gate-barrier-system)
   - [Public Address System](#public-address-system)
   - [Presence Sensors System](#presence-sensors-system)
3. [Implementation Add-Ons](#implementation-add-ons)

---

## Common Event Envelope

All systems use this unified event structure as the base envelope:

```json
{
  "buildingId": 1,
  "buildingCode": "B-01",
  "floorId": 3,
  "floorCode": "F-03",
  "systemId": "fire",                    // fire|access|cctv|gate|pa|presence
  "systemName": "Fire Alarm",
  "eventType": "alarm",                  // per-system enums below
  "datetime": "2025-09-18T18:20:59Z",    // ISO-8601 UTC
  "message": "short human-readable message",
  "state": "online",                     // optional current state (per-system)
  "command": null,                       // optional command ack name (if any)
  "data": {}                             // system-specific fields
}
```

### Important Notes

- **Timestamps**: Always use UTC timestamps in the API. UI converts to local time.
- **Node IDs**: Keep `nodeId` stable across vendors for easy joins/drill-down.
- **Severity**: Derive severity from `eventType` via a policy table.

---

## System-Specific Configurations

### Fire Alarm System

**System ID**: `"fire"`

#### Event Types
- `online`
- `offline`
- `reset`
- `fault`
- `other`
- `alarm` (example provided below)

#### Data Fields
*Populated when `eventType = "alarm"`*

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Panel identifier |
| `panelCode` | string | Panel code |
| `name` | string | Panel name |
| `zone` | string | Zone identifier |
| `loop` | string | Loop identifier |
| `nodeId` | integer | Node identifier |
| `nodeCode` | string | Node code |
| `address` | string | Device address |

#### Example

```json
{
  "buildingId": 1,
  "buildingCode": "B-01",
  "floorId": 3,
  "floorCode": "F-03",
  "systemId": "fire",
  "systemName": "Fire Alarm",
  "eventType": "alarm",
  "datetime": "2025-09-18T18:20:59Z",
  "message": "Smoke alarm Zone 1 (Loop 2, Address 17)",
  "data": {
    "id": 1,
    "panelCode": "panel-1",
    "name": "FA Main",
    "zone": "Zone 1",
    "loop": "Loop 2",
    "nodeId": 17,
    "nodeCode": "detector-17",
    "address": "17"
  }
}
```

---

### Access Control System

**System ID**: `"access"`

#### Event Types
- `accessGranted`
- `accessDenied`
- `doorOpen`
- `doorClose`
- `heldOpen`
- `forcedOpen`
- `tamper`
- `fault`
- `status`
- `other`

#### State Options
- `online`
- `offline`
- `open`
- `close`

#### Command Options
- `unlock`
- `lock`
- `pulse`
- `reset`

#### Data Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Controller identifier |
| `controllerCode` | string | Controller code |
| `name` | string | Controller name |
| `readerId` | integer | Reader identifier |
| `readerCode` | string | Reader code |
| `cardId` | integer | Card identifier |
| `cardCode` | string | Card code |
| `userId` | integer | User identifier |
| `userCode` | string | User code |
| `doorId` | string | Door identifier |
| `doorName` | string | Door name |
| `controllerId` | string | Controller identifier |
| `controllerName` | string | Controller name |
| `vendor` | string | Vendor name |
| `model` | string | Model name |
| `holder` | string | Cardholder name |
| `result` | string | `granted\|denied\|timeout` |
| `reason` | string | `schedule\|invalidCard\|expired\|antipassback\|pinRequired\|other` |
| `heldOpenSeconds` | integer | Duration door held open |

#### Example

```json
{
  "buildingId": 1,
  "buildingCode": "B-01",
  "floorId": 3,
  "floorCode": "F-03",
  "systemId": "access",
  "systemName": "Access Control",
  "eventType": "accessGranted",
  "datetime": "2025-09-18T18:20:59Z",
  "message": "Card access granted at Main Entrance",
  "data": {
    "id": 1,
    "controllerCode": "controller-1",
    "name": "Main Controller",
    "readerId": 2,
    "readerCode": "reader-2",
    "cardId": 12345,
    "cardCode": "card-12345",
    "userId": 789,
    "userCode": "user-789",
    "doorId": "door-1",
    "doorName": "Main Entrance",
    "controllerId": "ACU-4",
    "controllerName": "Main ACU",
    "vendor": "HID",
    "model": "ProxPoint Plus",
    "holder": "John Doe",
    "result": "granted"
  }
}
```

---

### CCTV System

**System ID**: `"cctv"`

#### Event Types
- `alarm`
- `motion`
- `smoke`
- `fire`
- `audioDetection`
- `lineCrossing`
- `loitering`
- `peopleCount`
- `faceDetection`

#### Command Options
- `popup`
- `ptz`
- `reset`

#### Data Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Camera identifier |
| `cameraCode` | string | Camera code |
| `cameraId` | string | Camera identifier |
| `cameraName` | string | Camera name |
| `location` | string | Camera location |
| `vendor` | string | Vendor name |
| `model` | string | Model name |
| `ip` | string | IP address |
| `channel` | integer | Channel number |
| `analyticType` | string | `motion\|smoke\|fire\|audio\|lineCrossing\|loitering\|peopleCount\|face` |
| `count` | integer | People count (for peopleCount events) |
| `faceId` | string | Face identifier |
| `uri` | string | RTSP/HTTP stream URI |
| `snapshotUrl` | string | Snapshot URL |
| `recording` | boolean | Recording status |
| `lastHeartbeat` | string | Last heartbeat timestamp (ISO-8601) |

#### Example

```json
{
  "buildingId": 1,
  "buildingCode": "B-01",
  "floorId": 3,
  "floorCode": "F-03",
  "systemId": "cctv",
  "systemName": "CCTV",
  "eventType": "motion",
  "datetime": "2025-09-18T18:20:59Z",
  "message": "Motion detected at Main Lobby",
  "data": {
    "id": 1,
    "cameraCode": "cam-001",
    "name": "Main Lobby Camera",
    "location": "Building A - Ground Floor Lobby",
    "vendor": "Hikvision",
    "model": "DS-2CD2143G0-I",
    "ip": "*************",
    "channel": 1,
    "analyticType": "motion",
    "count": null
  }
}
```

---

### Gate Barrier System

**System ID**: `"gate"`

#### Event Types
- `status`
- `open`
- `close`
- `blocked`
- `fault`
- `tamper`
- `other`

#### State Options
- `connected`
- `disconnected`
- `open`
- `close`
- `blocked`
- `fault`

#### Command Options
- `open`
- `close`
- `stop`
- `reset`

#### Data Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Gate identifier |
| `gateCode` | string | Gate code |
| `name` | string | Gate name |
| `location` | string | Gate location |
| `vendor` | string | Vendor name |
| `model` | string | Model name |
| `ip` | string | IP address |
| `status` | string | `open\|closed\|opening\|closing\|fault` |
| `gateId` | string | Gate identifier |
| `gateName` | string | Gate name |
| `vehiclePlate` | string | Vehicle license plate |
| `trigger` | string | Trigger event (e.g., `accessGranted`) |
| `anprConfidence` | number | ANPR confidence score (0-1) |

#### Example

```json
{
  "buildingId": 1,
  "buildingCode": "B-01",
  "floorId": 1,
  "floorCode": "G",
  "systemId": "gate",
  "systemName": "Gate Barrier",
  "eventType": "opened",
  "datetime": "2025-09-18T18:20:59Z",
  "message": "Gate opened at Main Entrance",
  "data": {
    "id": 1,
    "gateCode": "gate-001",
    "name": "Main Entrance Gate",
    "location": "Building A - Main Entrance",
    "vendor": "CAME",
    "model": "GARD 4000",
    "ip": "*************",
    "status": "open"
  }
}
```

---

### Public Address System

**System ID**: `"pa"`

#### Event Types
- `announcementStarted`
- `announcementEnded`
- `fault`
- `status`
- `other`

#### State Options
- `connected`
- `disconnected`
- `announcing`
- `idle`
- `fault`

#### Command Options
- `announce`
- `stop`
- `reset`

#### Data Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Zone identifier |
| `zoneCode` | string | Zone code |
| `name` | string | Zone name |
| `location` | string | Zone location |
| `vendor` | string | Vendor name |
| `model` | string | Model name |
| `volume` | integer | Volume level (0-100) |
| `message` | string | Announcement message |
| `zoneId` | string | Zone identifier |
| `zoneName` | string | Zone name |
| `announcementId` | string | Announcement identifier |
| `script` | string | Script identifier |
| `durationSec` | integer | Duration in seconds |

#### Example

```json
{
  "buildingId": 1,
  "buildingCode": "B-01",
  "floorId": 1,
  "floorCode": "G",
  "systemId": "pa",
  "systemName": "Public Address",
  "eventType": "announcementStarted",
  "datetime": "2025-09-18T18:20:30Z",
  "message": "Fire evacuation message started (Lobby Zone)",
  "state": "announcing",
  "data": {
    "id": 1,
    "zoneCode": "PA-Z-04",
    "name": "Lobby Zone",
    "location": "Building A - Ground Floor Lobby",
    "vendor": "TOA",
    "model": "A-2240",
    "volume": 85,
    "message": "Fire evacuation message started",
    "zoneId": "PA-Z-04",
    "zoneName": "Lobby Zone",
    "announcementId": "ann-20250918-001",
    "script": "fire-evac-en",
    "durationSec": 45
  }
}
```

---

### Presence Sensors System

**System ID**: `"presence"`

#### Event Types
- `presenceDetected`
- `presenceCleared`
- `peopleCount`
- `fault`
- `status`
- `other`

#### State Options
- `connected`
- `disconnected`
- `occupied`
- `clear`
- `fault`

#### Command Options
- `reset` (if supported, usually none)

#### Data Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Sensor identifier |
| `sensorCode` | string | Sensor code |
| `name` | string | Sensor name |
| `location` | string | Sensor location |
| `vendor` | string | Vendor name |
| `model` | string | Model name |
| `type` | string | `pir\|microwave\|ultrasonic\|camera` |
| `count` | integer | People count |
| `occupancy` | boolean | Occupancy status |

#### Example

```json
{
  "buildingId": 1,
  "buildingCode": "B-01",
  "floorId": 3,
  "floorCode": "F-03",
  "systemId": "presence",
  "systemName": "Presence Sensors",
  "eventType": "presenceDetected",
  "datetime": "2025-09-18T18:14:28Z",
  "message": "3F Corridor PIR occupied",
  "state": "occupied",
  "data": {
    "id": 1,
    "sensorCode": "PIR-3F-12",
    "name": "3F Corridor PIR",
    "location": "Building A - Third Floor Corridor",
    "vendor": "Steinel",
    "model": "IS 345 MX",
    "type": "pir",
    "count": 2,
    "occupancy": true
  }
}
```

---

## Implementation Add-Ons

### Optional Features

- **SSE/WebSocket Stream**: Reuses the same envelope for live updates
- **Aggregate Snapshot**: Returns compact per-system summaries (counts + lastUpdate)
- **Commands**: Expose `POST /v1/systems/{systemId}/commands/*` and emit command acknowledgments back on the stream with the `command` field populated

### API Endpoints

```
GET  /v1/events                           # Get recent events
POST /v1/systems/{systemId}/commands/*    # Send commands
GET  /v1/systems/{systemId}/status        # Get system status
```

### WebSocket/SSE Integration

Events are streamed in real-time using the same JSON structure defined above, enabling immediate updates to connected clients.
