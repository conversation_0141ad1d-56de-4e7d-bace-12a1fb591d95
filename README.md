# Nublar - Alert Dashboard System

A comprehensive Next.js application for monitoring and managing low-level security and safety systems across building facilities. The system provides real-time monitoring, alert management, and interactive 2D floor plan visualization for multiple building zones.

## System Overview

This dashboard integrates with four primary building systems:

- **🔥 Fire Alarm System** - Real-time fire detection, smoke detector monitoring, emergency alerts
- **🚪 Access Control System** - Door access monitoring, entry/exit logging, unauthorized access alerts
- **📹 CCTV Control System** - Camera feed monitoring, motion detection, recording status
- **🚧 Gates & Barriers System** - Automated gate control, barrier monitoring, vehicle access logging

## Key Features

- **Real-time Monitoring**: Live status updates from all connected systems
- **Interactive Floor Plans**: Visual 2D building layouts with device positioning
- **Alert Management**: Comprehensive alert popup system with priority classification
- **Geographic Navigation**: Hierarchical building/zone/floor/room structure
- **Multi-system Integration**: Unified dashboard for fire, access, CCTV, and barrier systems
- **Mobile Responsive**: Optimized for desktop and mobile access

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn package manager

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Run the development server:

```bash
npm run dev
```

4. Open [http://localhost:3001](http://localhost:3001) to view the dashboard

### Development Tools

Run Storybook for component development:

```bash
npm run storybook
```

Access Storybook at [http://localhost:6007](http://localhost:6007)

## Project Structure

```
src/
├── app/                    # Next.js app router pages
├── components/             # Reusable UI components
│   ├── common/            # Shared components
│   ├── features/          # Feature-specific components
│   └── pages/             # Page-level components
├── infrastructure/         # External integrations
├── shared/                # Shared utilities and types
├── stores/                # Zustand state management
└── stories/               # Storybook stories
```

## Geographic Hierarchy

The system follows a structured geographic organization:

```
Building → Zone → Floor → Room → Door/Device
```

Example:

- Building A
  - North Wing (Zone)
    - Floor 1
      - Room 101
        - Door 101-A (Access Control)
        - Camera 101-C (CCTV)

## Alert System

The dashboard features a comprehensive alert popup system with:

- **Color-coded States**: Green (Normal), Red (Danger), Blue (Info), Orange (Warning)
- **System-specific Popups**: Tailored information for each system type
- **Real-time Updates**: Live status changes and notifications
- **Interactive Actions**: System-specific action buttons

## Technology Stack

- **Frontend**: Next.js 15.5.2 with TypeScript
- **State Management**: Zustand
- **UI Components**: Custom component library
- **Styling**: CSS Modules / Tailwind CSS
- **Development**: Storybook, ESLint, Prettier
- **Real-time**: WebSocket integration (planned)

## Documentation

Detailed project documentation is available in the `_docs/` directory:

- [Project Overview](_docs/project/01-project-overview.md) - Comprehensive system description
- [Dashboard Layout](_docs/project/02-dashboard-layout.md) - UI layout specifications
- [Development Rules](_docs/rules/) - Coding standards and conventions

## Development Status

This project is in active development. Core dashboard framework is implemented with ongoing work on:

- Interactive floor plan components
- Real-time alert system integration
- System-specific monitoring panels
- Mobile responsive optimizations

## Contributing

Please refer to the development rules in `_docs/rules/` for coding standards, naming conventions, and commit guidelines.

---

*For detailed technical specifications and system architecture, see the project documentation in the `_docs/` directory.*
