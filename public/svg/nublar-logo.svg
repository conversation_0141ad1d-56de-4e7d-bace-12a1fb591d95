<svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="buildingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#60A5FA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="aiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main background -->
  <circle cx="24" cy="24" r="24" fill="url(#bgGradient)"/>
  
  <!-- Building structure -->
  <g transform="translate(12, 10)">
    <!-- Main building -->
    <rect x="6" y="8" width="12" height="20" rx="1" fill="url(#buildingGradient)" opacity="0.9"/>
    
    <!-- Left wing -->
    <rect x="2" y="12" width="6" height="16" rx="1" fill="url(#buildingGradient)" opacity="0.7"/>
    
    <!-- Right wing -->
    <rect x="16" y="14" width="6" height="14" rx="1" fill="url(#buildingGradient)" opacity="0.7"/>
    
    <!-- Windows pattern -->
    <g fill="white" opacity="0.8">
      <!-- Main building windows -->
      <rect x="8" y="10" width="1.5" height="1.5" rx="0.2"/>
      <rect x="10.5" y="10" width="1.5" height="1.5" rx="0.2"/>
      <rect x="13" y="10" width="1.5" height="1.5" rx="0.2"/>
      <rect x="15.5" y="10" width="1.5" height="1.5" rx="0.2"/>
      
      <rect x="8" y="13" width="1.5" height="1.5" rx="0.2"/>
      <rect x="10.5" y="13" width="1.5" height="1.5" rx="0.2"/>
      <rect x="13" y="13" width="1.5" height="1.5" rx="0.2"/>
      <rect x="15.5" y="13" width="1.5" height="1.5" rx="0.2"/>
      
      <rect x="8" y="16" width="1.5" height="1.5" rx="0.2"/>
      <rect x="10.5" y="16" width="1.5" height="1.5" rx="0.2"/>
      <rect x="13" y="16" width="1.5" height="1.5" rx="0.2"/>
      <rect x="15.5" y="16" width="1.5" height="1.5" rx="0.2"/>
      
      <!-- Left wing windows -->
      <rect x="3.5" y="14" width="1" height="1" rx="0.2"/>
      <rect x="5.5" y="14" width="1" height="1" rx="0.2"/>
      <rect x="3.5" y="17" width="1" height="1" rx="0.2"/>
      <rect x="5.5" y="17" width="1" height="1" rx="0.2"/>
      
      <!-- Right wing windows -->
      <rect x="17.5" y="16" width="1" height="1" rx="0.2"/>
      <rect x="19.5" y="16" width="1" height="1" rx="0.2"/>
      <rect x="17.5" y="19" width="1" height="1" rx="0.2"/>
      <rect x="19.5" y="19" width="1" height="1" rx="0.2"/>
    </g>
  </g>
  
  <!-- AI Neural Network Pattern -->
  <g transform="translate(8, 8)" opacity="0.6">
    <!-- Neural nodes -->
    <circle cx="4" cy="4" r="1.5" fill="url(#aiGradient)"/>
    <circle cx="28" cy="4" r="1.5" fill="url(#aiGradient)"/>
    <circle cx="16" cy="12" r="1.5" fill="url(#aiGradient)"/>
    <circle cx="4" cy="28" r="1.5" fill="url(#aiGradient)"/>
    <circle cx="28" cy="28" r="1.5" fill="url(#aiGradient)"/>
    
    <!-- Connection lines -->
    <g stroke="url(#aiGradient)" stroke-width="0.8" opacity="0.7">
      <line x1="4" y1="4" x2="16" y2="12"/>
      <line x1="28" y1="4" x2="16" y2="12"/>
      <line x1="16" y1="12" x2="4" y2="28"/>
      <line x1="16" y1="12" x2="28" y2="28"/>
      <line x1="4" y1="4" x2="28" y2="28"/>
      <line x1="28" y1="4" x2="4" y2="28"/>
    </g>
  </g>
  
  <!-- Central "N" monogram -->
  <g transform="translate(20, 18)">
    <rect x="0" y="0" width="2" height="12" fill="white" opacity="0.9"/>
    <rect x="6" y="0" width="2" height="12" fill="white" opacity="0.9"/>
    <rect x="2" y="4" width="4" height="2" fill="white" opacity="0.9" transform="rotate(45 4 5)"/>
  </g>
  
  <!-- Low current circuit elements -->
  <g transform="translate(6, 36)" opacity="0.5">
    <!-- Circuit traces -->
    <rect x="0" y="0" width="36" height="0.5" fill="#10B981"/>
    <rect x="6" y="0" width="0.5" height="4" fill="#10B981"/>
    <rect x="18" y="0" width="0.5" height="4" fill="#10B981"/>
    <rect x="30" y="0" width="0.5" height="4" fill="#10B981"/>
    
    <!-- Connection points -->
    <circle cx="6.25" cy="4" r="1" fill="#10B981"/>
    <circle cx="18.25" cy="4" r="1" fill="#10B981"/>
    <circle cx="30.25" cy="4" r="1" fill="#10B981"/>
  </g>
</svg>