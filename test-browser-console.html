<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nebular API Console Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .status.warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .status.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .console-output {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Nebular Event Management System - Console Test</h1>
        
        <div class="status info">
            <strong>📋 Test Configuration:</strong><br>
            Base URL: <code>http://localhost:8088</code><br>
            Endpoint: <code>/api/v1/events</code><br>
            Full URL: <code>http://localhost:8088/api/v1/events</code>
        </div>

        <div class="test-section">
            <h3>🔧 Setup Instructions</h3>
            <p>This page allows you to test the Nebular API implementation directly in the browser console.</p>
            
            <div class="status warning">
                <strong>⚠️ Server Status:</strong> The API server at <code>http://localhost:8088</code> appears to be offline. 
                Please start the server before running tests.
            </div>

            <h4>Steps to test:</h4>
            <ol>
                <li>Open browser Developer Tools (F12)</li>
                <li>Go to the Console tab</li>
                <li>Run the test commands below</li>
                <li>Check the results in the console</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🧪 Quick Tests</h3>
            <p>Copy and paste these commands into the browser console:</p>

            <h4>1. Test API Connection</h4>
            <div class="code-block">
// Test basic connection
fetch('http://localhost:8088/api/v1/events')
  .then(response => {
    console.log('✅ Connection Status:', response.status);
    return response.json();
  })
  .then(data => console.log('📊 Response Data:', data))
  .catch(error => console.log('❌ Connection Error:', error.message));
            </div>
            <button class="button" onclick="testConnection()">Run Connection Test</button>

            <h4>2. Test GET Events</h4>
            <div class="code-block">
// Test GET events with parameters
fetch('http://localhost:8088/api/v1/events?limit=5&system=fire')
  .then(response => response.json())
  .then(data => {
    console.log('✅ GET Events Success');
    console.log('📊 Events Data:', data);
  })
  .catch(error => console.log('❌ GET Events Error:', error.message));
            </div>
            <button class="button" onclick="testGetEvents()">Run GET Events Test</button>

            <h4>3. Test POST Event (Create)</h4>
            <div class="code-block">
// Test creating a new event
const testEvent = {
  buildingId: 1,
  buildingCode: 'TEST-BLDG',
  floorId: 1,
  floorCode: 'TEST-FLOOR',
  systemCode: 'fire',
  systemName: 'Fire Alarm System',
  eventType: 'test',
  datetime: new Date().toISOString(),
  message: 'Test event from browser console',
  sourceEventCode: 'BROWSER-TEST-001',
  sourceState: 'active',
  state: 'new',
  Data: {
    panelId: 1,
    panelCode: 'TEST-PANEL',
    panelName: 'Test Fire Panel',
    zone: 'Zone A',
    loop: 'Loop 1',
    nodeId: 1,
    nodeCode: 'TEST-NODE',
    address: '*************'
  }
};

fetch('http://localhost:8088/api/v1/events', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(testEvent)
})
.then(response => response.json())
.then(data => {
  console.log('✅ POST Event Success');
  console.log('📊 Created Event:', data);
})
.catch(error => console.log('❌ POST Event Error:', error.message));
            </div>
            <button class="button" onclick="testCreateEvent()">Run Create Event Test</button>

            <h4>4. Test Server Health</h4>
            <div class="code-block">
// Test server health endpoint
fetch('http://localhost:8088/health')
  .then(response => response.json())
  .then(data => {
    console.log('✅ Health Check Success');
    console.log('📊 Server Health:', data);
  })
  .catch(error => console.log('❌ Health Check Error:', error.message));
            </div>
            <button class="button" onclick="testHealth()">Run Health Check</button>
        </div>

        <div class="test-section">
            <h3>🔍 Advanced Testing</h3>
            <p>For more comprehensive testing, use these commands:</p>

            <div class="code-block">
// Run all tests sequentially
async function runAllTests() {
  console.log('🚀 Starting Nebular API Test Suite...\n');
  
  const tests = [
    { name: 'Connection', fn: testConnection },
    { name: 'GET Events', fn: testGetEvents },
    { name: 'Create Event', fn: testCreateEvent },
    { name: 'Health Check', fn: testHealth }
  ];
  
  for (const test of tests) {
    console.log(`🧪 Running ${test.name} test...`);
    try {
      await test.fn();
      console.log(`✅ ${test.name} test completed\n`);
    } catch (error) {
      console.log(`❌ ${test.name} test failed:`, error.message, '\n');
    }
  }
  
  console.log('🏁 All tests completed!');
}

// Run the full test suite
runAllTests();
            </div>
            <button class="button" onclick="runAllTests()">Run All Tests</button>
        </div>

        <div class="test-section">
            <h3>📊 Console Output</h3>
            <p>Test results will appear in the browser console and below:</p>
            <div id="console-output" class="console-output">
                Console output will appear here when tests are run...
            </div>
            <button class="button" onclick="clearOutput()">Clear Output</button>
        </div>

        <div class="test-section">
            <h3>🛠️ Troubleshooting</h3>
            <div class="status error">
                <strong>Common Issues:</strong>
                <ul>
                    <li><strong>CORS Error:</strong> The API server needs to allow cross-origin requests</li>
                    <li><strong>Connection Refused:</strong> The server is not running on localhost:8088</li>
                    <li><strong>404 Not Found:</strong> The /api/v1/events endpoint is not implemented</li>
                    <li><strong>500 Server Error:</strong> There's an issue with the server implementation</li>
                </ul>
            </div>

            <div class="status info">
                <strong>Next Steps:</strong>
                <ol>
                    <li>Start the Nebular API server on port 8088</li>
                    <li>Ensure CORS is configured to allow browser requests</li>
                    <li>Implement the /api/v1/events endpoint</li>
                    <li>Test with the actual React application</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // Console output capture
        const originalLog = console.log;
        const outputDiv = document.getElementById('console-output');

        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            outputDiv.innerHTML += message + '\n';
            outputDiv.scrollTop = outputDiv.scrollHeight;
        };

        // Test functions
        async function testConnection() {
            try {
                const response = await fetch('http://localhost:8088/api/v1/events');
                console.log('✅ Connection Status:', response.status);
                const data = await response.json();
                console.log('📊 Response Data:', data);
            } catch (error) {
                console.log('❌ Connection Error:', error.message);
            }
        }

        async function testGetEvents() {
            try {
                const response = await fetch('http://localhost:8088/api/v1/event?limit=5&system=fire');
                const data = await response.json();
                console.log('✅ GET Events Success');
                console.log('📊 Events Data:', data);
            } catch (error) {
                console.log('❌ GET Events Error:', error.message);
            }
        }

        async function testCreateEvent() {
            const testEvent = {
                buildingId: 1,
                buildingCode: 'TEST-BLDG',
                floorId: 1,
                floorCode: 'TEST-FLOOR',
                systemCode: 'fire',
                systemName: 'Fire Alarm System',
                eventType: 'test',
                datetime: new Date().toISOString(),
                message: 'Test event from browser console',
                sourceEventCode: 'BROWSER-TEST-001',
                sourceState: 'active',
                state: 'new',
                Data: {
                    panelId: 1,
                    panelCode: 'TEST-PANEL',
                    panelName: 'Test Fire Panel',
                    zone: 'Zone A',
                    loop: 'Loop 1',
                    nodeId: 1,
                    nodeCode: 'TEST-NODE',
                    address: '*************'
                }
            };

            try {
                const response = await fetch('http://localhost:8088/api/v1/event', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testEvent)
                });
                const data = await response.json();
                console.log('✅ POST Event Success');
                console.log('📊 Created Event:', data);
            } catch (error) {
                console.log('❌ POST Event Error:', error.message);
            }
        }

        async function testHealth() {
            try {
                const response = await fetch('http://localhost:8088/health');
                const data = await response.json();
                console.log('✅ Health Check Success');
                console.log('📊 Server Health:', data);
            } catch (error) {
                console.log('❌ Health Check Error:', error.message);
            }
        }

        async function runAllTests() {
            console.log('🚀 Starting Nebular API Test Suite...\n');
            
            const tests = [
                { name: 'Connection', fn: testConnection },
                { name: 'GET Events', fn: testGetEvents },
                { name: 'Create Event', fn: testCreateEvent },
                { name: 'Health Check', fn: testHealth }
            ];
            
            for (const test of tests) {
                console.log(`🧪 Running ${test.name} test...`);
                try {
                    await test.fn();
                    console.log(`✅ ${test.name} test completed\n`);
                } catch (error) {
                    console.log(`❌ ${test.name} test failed:`, error.message, '\n');
                }
                // Add delay between tests
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            console.log('🏁 All tests completed!');
        }

        function clearOutput() {
            outputDiv.innerHTML = 'Console output cleared...\n';
        }

        // Initial message
        console.log('🚀 Nebular API Console Test Page Loaded');
        console.log('📋 Ready to test API endpoints');
        console.log('💡 Use the buttons above or run commands manually');
    </script>
</body>
</html>