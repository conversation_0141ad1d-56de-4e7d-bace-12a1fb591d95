import { useBuildings } from '@/infrastructure/api/buildings';

export function BuildingsList() {
    const {
        data: buildingsData,
        isLoading,
        error,
        refetch
    } = useBuildings({
        limit: 10,
        is_active: true
    });

    if (isLoading) return <div>Loading buildings...</div>;
    if (error) return <div>Error: {error.message}</div>;

    return (
        <div className="buildings-list">
            {buildingsData?.data.buildings.map(building => (
                <div key={building.id} className="building-card">
                    <h3>{building.name} ({building.code})</h3>
                    <p>{building.description}</p>
                    <p>Floors: {building.floor_count} | Rooms: {building.room_count}</p>
                </div>
            ))}
        </div>
    );
}