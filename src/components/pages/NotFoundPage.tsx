import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/common/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/common/ui/Card';

interface NotFoundPageProps {
    className?: string;
}

function NotFoundPage({ className }: NotFoundPageProps) {
    return (
        <div className={`flex items-center justify-center min-h-[60vh] ${className || ''}`}>
            <Card className="w-full max-w-md text-center">
                <CardHeader>
                    <div className="text-6xl font-bold text-muted-foreground mb-4">404</div>
                    <CardTitle className="text-2xl">Page Not Found</CardTitle>
                    <CardDescription>
                        The page you&apos;re looking for doesn&apos;t exist or has been moved.
                    </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="space-y-2">
                        <Button asChild className="w-full">
                            <Link href="/">Go Home</Link>
                        </Button>
                        <Button variant="outline" asChild className="w-full">
                            <Link href="/dashboard">Go to Dashboard</Link>
                        </Button>
                    </div>

                    <div className="text-sm text-muted-foreground">
                        If you believe this is an error, please contact support.
                    </div>
                </CardContent>
            </Card>
        </div>
    );
}

export default NotFoundPage;
