/**
 * AlertDashboardPage Component
 * Reusable page component for the alert dashboard
 */

'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { AlertDashboardLayout } from '@/components/features/alert-dashboard';
import { SmartSpinnerInline } from '@/components/common/SmartSpinner';

interface AlertDashboardPageProps {
    className?: string;
}

/**
 * AlertDashboardPage component
 * Provides the complete alert dashboard page with layout and content
 */
function AlertDashboardPage({ className }: AlertDashboardPageProps) {
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        // Loading delay - show spinner for 3 seconds
        const loadingTimer = setTimeout(() => {
            setIsLoading(false);
        }, 3000);

        return () => {
            clearTimeout(loadingTimer);
        };
    }, []);

    return (
        <div className="relative">
            {/* Loading Spinner Overlay */}
            {isLoading && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
                >
                    <motion.div
                        initial={{ scale: 0.8, y: 20 }}
                        animate={{ scale: 1, y: 0 }}
                        className="flex flex-col items-center space-y-4"
                    >
                        <SmartSpinnerInline size={80} speed={1.2} />
                        <motion.p
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            transition={{ delay: 0.5 }}
                            className="text-white text-lg font-medium"
                        >
                            Loading Alert Dashboard...
                        </motion.p>
                    </motion.div>
                </motion.div>
            )}

            <AlertDashboardLayout className={className}>
                {/* Alert dashboard content area */}
            </AlertDashboardLayout>
        </div>
    );
}

export default AlertDashboardPage;
