# Components Architecture

This folder contains all the reusable UI components organized in a scalable structure based on the tts-frontend project patterns.

## Folder Structure

```
components/
├── common/           # Shared, reusable components
│   └── ui/          # Basic UI building blocks
├── features/        # Feature-specific components
│   ├── auth/        # Authentication related components
│   └── layout/      # Layout components (Header, Footer, etc.)
└── pages/           # Page-level components
```

## Organization Principles

### 1. Common Components (`/common`)
Contains generic, reusable UI components that can be used across the entire application:
- **UI Components**: Basic building blocks like Button, Input, Card, Label
- **No Business Logic**: These components are purely presentational
- **Highly Reusable**: Can be used in any context without modification

### 2. Feature Components (`/features`)
Contains components that are specific to particular features or domains:
- **Feature-Specific**: Components related to specific application features
- **Moderate Reusability**: Can be reused within the same feature domain
- **Examples**: LoginForm, UserProfile, ProductCard

### 3. Page Components (`/pages`)
Contains page-level components that represent entire pages or major sections:
- **Page-Level**: Components that represent full pages or major page sections
- **Low Reusability**: Typically used once per page
- **Examples**: HomePage, DashboardPage, NotFoundPage

## Available Components

### Common UI Components
- `Button` - Versatile button component with multiple variants
- `Input` - Form input component with consistent styling
- `Card` - Container component with header, content, and footer
- `Label` - Form label component

### Feature Components
- `Layout` - Main application layout with header and footer
- `Header` - Application header with navigation
- `Footer` - Application footer
- `LoginForm` - Authentication form component

### Page Components
- `HomePage` - Landing page with hero section and features
- `DashboardPage` - Dashboard with stats and quick actions
- `NotFoundPage` - 404 error page

## Usage Examples

### Importing Components

```typescript
// Import from main components index
import { Button, Card, HomePage } from '@/components';

// Import from specific folders
import { Button } from '@/components/common/ui';
import { LoginForm } from '@/components/features';
import { HomePage } from '@/components/pages';
```

### Using UI Components

```typescript
import { Button, Card, CardHeader, CardTitle, CardContent } from '@/components';

function MyComponent() {
    return (
        <Card>
            <CardHeader>
                <CardTitle>Example Card</CardTitle>
            </CardHeader>
            <CardContent>
                <p>This is a card example.</p>
                <Button variant="outline">Click me</Button>
            </CardContent>
        </Card>
    );
}
```

### Using Feature Components

```typescript
import { Layout, LoginForm } from '@/components';

function LoginPage() {
    const handleLogin = (email: string, password: string) => {
        // Handle login logic
    };

    return (
        <Layout>
            <div className="flex justify-center items-center min-h-screen">
                <LoginForm onSubmit={handleLogin} />
            </div>
        </Layout>
    );
}
```

## Design System Integration

### Styling
- All components use **Tailwind CSS** for styling
- Consistent design tokens through CSS variables
- **class-variance-authority** for component variants
- **clsx** and **tailwind-merge** for conditional classes

### Accessibility
- Components follow accessibility best practices
- Proper ARIA attributes and semantic HTML
- Keyboard navigation support
- Screen reader compatibility

### TypeScript
- Full TypeScript support with proper typing
- Generic components where appropriate
- Exported interfaces for component props

## Best Practices

### 1. Component Placement
- **Common**: Place generic, reusable components here
- **Features**: Place domain-specific components here
- **Pages**: Place page-level components here

### 2. Naming Conventions
- Use PascalCase for component names
- Use descriptive names that indicate purpose
- Suffix with component type when helpful (e.g., `LoginForm`, `UserCard`)

### 3. Props Design
- Keep props interfaces simple and focused
- Use composition over configuration
- Provide sensible defaults
- Support className prop for styling flexibility

### 4. Dependencies
- Common components should have minimal dependencies
- Feature components can depend on common components
- Page components can use both common and feature components

## Adding New Components

### 1. Determine the Right Location
- **Generic and reusable** → `/common/ui`
- **Feature-specific** → `/features/{feature-name}`
- **Page-level** → `/pages`

### 2. Follow the Pattern
```typescript
// Component file structure
import React from 'react';
import { cn } from '@/shared/utils';

interface ComponentProps {
    className?: string;
    // other props
}

function Component({ className, ...props }: ComponentProps) {
    return (
        <div className={cn('base-styles', className)} {...props}>
            {/* component content */}
        </div>
    );
}

export default Component;
```

### 3. Update Exports
Add your component to the appropriate `index.ts` file to make it available through the main components export.

## Dependencies

The components rely on these key dependencies:
- `@radix-ui/react-*` - Accessible component primitives
- `class-variance-authority` - Component variant management
- `clsx` - Conditional class names
- `tailwind-merge` - Tailwind class deduplication
- `next/link` - Next.js navigation

Make sure these are installed in your project for the components to work properly.