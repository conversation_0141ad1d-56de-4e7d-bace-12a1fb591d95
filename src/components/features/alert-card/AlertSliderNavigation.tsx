'use client';

import React, { useMemo, useState } from 'react';
import { useEventsStore } from '@/stores/alert.store';
import { motion, AnimatePresence } from 'framer-motion';
import { SvgIcon } from '@/components/common/ui/SvgIcon';

// ---------------------- DetailRow ----------------------
function DetailRow({ label, value }: { label: string; value: React.ReactNode }) {
    return (
        <div className="table-row text-xs">
            <span className="table-cell font-bold text-white whitespace-nowrap rtl:pl-2 ltr:pr-2 py-1">{label}</span>
            <span className="table-cell font-medium break-words whitespace-pre-wrap text-gray-400 py-1">{value}</span>
        </div>
    );
}

// ---------------------- Device Icons ----------------------
const deviceIcons: Record<string, string> = {
    camera: 'camera',
    door: 'door',
    gate: 'gateBarrier',
    fire: 'alert',
};

// ---------------------- AlertSliderNavigation ----------------------
interface AlertSliderNavigationProps {
    onClose: () => void;
}

const AlertSliderNavigation: React.FC<AlertSliderNavigationProps> = ({ onClose }) => {
    const events = useEventsStore((s) => s.events);
    const alertEvents = useMemo(() => events.filter((e) => e.isAlert), [events]);

    const [currentIndex, setCurrentIndex] = useState(0);
    const [direction, setDirection] = useState(0);

    if (alertEvents.length === 0) {
        return (
            <div className="w-[380px] bg-gray-900 text-white rounded-lg shadow-xl border border-gray-700 p-4">
                <p className="text-sm text-gray-300">No alerts available</p>
            </div>
        );
    }

    const next = () => {
        setDirection(1);
        setCurrentIndex((prev) => (prev + 1) % alertEvents.length);
    };

    const prev = () => {
        setDirection(-1);
        setCurrentIndex((prev) => (prev === 0 ? alertEvents.length - 1 : prev - 1));
    };

    const current = alertEvents[currentIndex];

    return (
        <div
            className="relative w-[400px] bg-gray-900 text-white rounded-lg shadow-xl border border-red-500/30 overflow-hidden opacity-90"
            onClick={(e) => e.stopPropagation()}>
            {/* Header */}
            {/* Header */}
            <div className="relative p-4 border-b border-gray-700 flex items-center justify-between">
                {/* Left Arrow */}
                <button
                    onClick={(e) => {
                        e.stopPropagation();
                        prev();
                    }}
                    className="absolute left-2 top-1/2 -translate-y-1/2 p-1 text-gray-400 hover:text-white">
                    <SvgIcon name="leftArrow" size="default" strokeColor="white" />
                </button>

                {/* Title (floats more left now) */}
                <div className="flex items-center gap-3 ml-10">
                    <div className="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                        <SvgIcon name={deviceIcons[current.deviceType] || 'alert'} size="default" strokeColor="white" />
                    </div>
                    <div>
                        <h3 className="font-semibold text-sm text-white max-w-[220px] truncate">{current.name}</h3>
                        <p className="text-gray-400 text-xs">{current.status}</p>
                    </div>
                </div>

                {/* Right Arrow + Close */}
                <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-2">
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            next();
                        }}
                        className="p-1 text-gray-400 hover:text-white">
                        <SvgIcon name="rightArrow" size="default" strokeColor="white" />
                    </button>
                    <button
                        onClick={(e) => {
                            e.stopPropagation();
                            onClose();
                        }}
                        className="p-1 text-gray-400 hover:text-white">
                        <SvgIcon name="minimizeScreen" size="default" strokeColor="white" />
                    </button>
                </div>
            </div>

            {/* Animated Content */}
            <div className="relative px-4 py-3 h-25 overflow-hidden ">
                <AnimatePresence initial={false} custom={direction}>
                    <motion.div
                        key={current.id}
                        custom={direction}
                        initial={{ x: direction > 0 ? 300 : -300, opacity: 0 }}
                        animate={{ x: 0, opacity: 1 }}
                        exit={{ x: direction > 0 ? -300 : 300, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="absolute inset-0">
                        <div className="table w-full p-2">
                            <DetailRow label="Timestamp" value={current.timestamp} />
                            <DetailRow label="Zone" value={current.zoneId} />
                            <DetailRow label="Description" value={current.description} />
                        </div>
                    </motion.div>
                </AnimatePresence>
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between p-3 border-t border-gray-700">
                <span className="text-xs text-red-500">
                    {currentIndex + 1}/{alertEvents.length}
                </span>
                <div>
                    <button
                        onClick={onClose}
                        className="px-4 py-2 mr-2 bg-gray-700 hover:bg-gray-600 text-white text-sm rounded transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500">
                        Close
                    </button>
                    <button className="px-4 py-2 bg-blue-400 hover:bg-blue-700 text-white text-sm rounded transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500">
                        Action text
                    </button>
                </div>
            </div>
        </div>
    );
};

export default AlertSliderNavigation;

// ---------------------- Overlay wrapper ----------------------
export function AlertCarouselOverlay({ onClose }: { onClose: () => void }) {
    return (
        <div className="fixed inset-0 flex items-center justify-center bg-black/50 z-50" onClick={onClose}>
            <AlertSliderNavigation onClose={onClose} />
        </div>
    );
}
