'use client';

import React from 'react';
import Image from 'next/image'; // ✅ Add this import
import NavigationItem from './navigation-item';

interface NavigationItemConfig {
    id: string;
    icon: string;
    label: string;
}

interface VerticalNavigationBarProps {
    activeItem?: string;
    onItemClick?: (itemId: string) => void;
    className?: string;
    customItems?: NavigationItemConfig[];
    showLogo?: boolean;
    logoSrc?: string;
}

const VerticalNavigationBar: React.FC<VerticalNavigationBarProps> = ({
    activeItem,
    onItemClick,
    className = '',
    customItems,
    showLogo = true,
    logoSrc = '/svg/appLogo.svg',
}) => {
    // Default navigation items
    const defaultNavigationItems: NavigationItemConfig[] = [
        { id: 'home', icon: 'home', label: 'Home Dashboard' },
        { id: 'notifications', icon: 'bell', label: 'Notifications & Alerts' },
        { id: 'fire', icon: 'fire', label: 'Fire System Devices' },
        { id: 'exit', icon: 'door', label: 'Logout' },
        { id: 'camera', icon: 'camera', label: 'Camera Monitoring' },
        { id: 'gate', icon: 'gateBarrier', label: 'Gate Management' },
        { id: 'chat', icon: 'chat', label: 'Communication' },
    ];

    const navigationItems: NavigationItemConfig[] = customItems || defaultNavigationItems;

    const handleItemClick = (itemId: string): void => {
        onItemClick?.(itemId);
    };

    return (
        <nav
            className={`
        flex flex-col
        w-20 min-h-screen
        p-4
        gap-12
        ${className}
      `}
            role="navigation"
            aria-label="Main navigation"
            style={{
                width: '80px',
                height: '960px',
                backgroundColor: '#0D131F',
                border: '1px solid #1F2937',
                padding: '16px',
                gap: '48px',
                position: 'fixed',
            }}>
            {/* Top Section - Logo */}
            <div className="flex flex-col items-center">
                {showLogo && (
                    <div className="flex items-center justify-center">
                        <div className="w-10 h-10 flex items-center justify-center">
                            <Image
                                src={logoSrc}
                                alt="Logo"
                                width={40} // ✅ Next.js requires explicit width/height
                                height={40}
                                className="object-contain"
                                onError={(e) => {
                                    // Fallback if image fails to load
                                    const target = e.target as HTMLElement;
                                    target.style.display = 'none';
                                    target.parentElement!.innerHTML = '<div class="w-6 h-6 bg-blue-400 rounded"></div>';
                                }}
                            />
                        </div>
                    </div>
                )}
            </div>

            {/* Middle Section - Navigation Items */}
            <div
                className="flex flex-col items-center"
                style={{
                    gap: '15px',
                    width: '48px',
                    height: '832px',
                    backgroundColor: '#0D131F',
                }}>
                {navigationItems.map((item: NavigationItemConfig) => (
                    <NavigationItem
                        key={item.id}
                        icon={item.icon}
                        label={item.label}
                        isActive={activeItem === item.id}
                        onClick={() => handleItemClick(item.id)}
                    />
                ))}
            </div>
        </nav>
    );
};

export default VerticalNavigationBar;
export type { VerticalNavigationBarProps, NavigationItemConfig };
