'use client';

import React, { useState } from 'react';
import { SvgIcon } from '@/components/common/ui/SvgIcon';

interface NavigationItemProps {
    icon: string;
    label: string;
    isActive?: boolean;
    onClick?: () => void;
    className?: string;
}

const NavigationItem: React.FC<NavigationItemProps> = ({ icon, label, isActive = false, onClick, className = '' }) => {
    const [isHovered, setIsHovered] = useState<boolean>(false);

    // Determine stroke color based on state
    const getStrokeColor = (): string => {
        if (isActive) return '#FFFFFF';
        if (isHovered) return '#5D9DD4';
        return '#9ca3af';
    };

    const handleClick = (): void => {
        if (onClick) {
            onClick();
        }
    };

    return (
        <button
            type="button"
            className={`
        relative flex items-center justify-center
        w-12 h-12 rounded-lg
        transition-all duration-200 ease-in-out
        focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900
        ${
            isActive
                ? 'bg-blue-600 text-white shadow-lg shadow-blue-600/25'
                : isHovered
                  ? 'bg-gray-700 text-gray-100'
                  : 'bg-transparent text-gray-400 hover:text-gray-100'
        }
        ${className}
      `}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            onClick={handleClick}
            aria-label={label}
            title={label}>
            <SvgIcon
                name={icon}
                size="lg"
                color={getStrokeColor()}
                strokeWidth="1.5"
                className={`transition-transform duration-200 ${isActive || isHovered ? 'scale-110' : 'scale-100'}`}
            />

            {/* Active indicator */}
            {isActive && (
                <div className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-1 h-6 bg-blue-400 rounded-r-full" />
            )}
        </button>
    );
};

export default NavigationItem;
export type { NavigationItemProps };
