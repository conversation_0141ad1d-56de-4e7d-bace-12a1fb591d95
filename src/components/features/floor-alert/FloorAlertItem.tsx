import React from 'react';

export interface FloorData {
    id: string;
    name: string;
    floorCode?: string;
    alertCount?: number;
}

interface FloorItemProps {
    floor: FloorData;
    isSelected: boolean;
    onClick: (floorId: string) => void;
}

const FloorItem: React.FC<FloorItemProps> = ({ floor, isSelected, onClick }) => {
    return (
        <button
            type="button"
            aria-pressed={isSelected}
            onClick={() => onClick(floor.id)}
            className={[
                'w-full h-12 flex items-center justify-between text-left',
                'rounded-[8px] border px-2 py-3 text-white',
                'transition-all duration-200 ease-in-out',
                isSelected
                    ? 'bg-[#1F2937] border-[#5C9DD5] scale-105 shadow-md'
                    : 'bg-[#21263380] border-[#1F2937] hover:scale-105 hover:shadow-md hover:bg-[#232b3a] hover:border-[#5C9DD5]',
            ].join(' ')}>
            <span className="font-poppins text-[16px] font-bold leading-none">{floor.name}</span>

            {typeof floor.alertCount === 'number' && floor.alertCount > 0 && (
                <span className="min-w-6 h-6 px-2 bg-red-500 text-white rounded-full flex items-center justify-center text-xs font-medium">
                    {floor.alertCount}
                </span>
            )}
        </button>
    );
};

export default FloorItem;
