'use client';

import React from 'react';
import { SvgIcon } from '../common/ui/SvgIcon';
import Image from 'next/image';

// Enhanced type definitions with comprehensive customization
interface InfoRowData {
    label: string;
    value: string | number;
    show?: boolean;
    labelColor?: string;
    valueColor?: string;
    icon?: string;
    badge?: {
        text: string;
        color: string;
        bgColor: string;
    };
}

interface ButtonConfig {
    text: string;
    onClick: () => void;
    variant?: 'primary' | 'secondary' | 'danger' | 'success';
    disabled?: boolean;
    loading?: boolean;
    icon?: string;
}

interface ImageConfig {
    src: string;
    alt: string;
    onClick?: () => void;
    className?: string;
    width?: number;
    height?: number;
}

interface StatusBadge {
    text: string;
    variant: 'success' | 'danger' | 'warning' | 'info';
    show: boolean;
}

interface SubHeaderConfig {
    text?: string;
    date?: string;
    textColor?: string;
    dateColor?: string;
    separator?: string;
    showText?: boolean;
    showDate?: boolean;
}

interface AlertCardProps {
    // Header Configuration
    title?: string;
    subHeader?: SubHeaderConfig;
    icon?: string;
    iconColor?: string;
    iconContainerColor?: string;
    headerTextColor?: string;
    showHeader?: boolean;
    showCloseX?: boolean;

    // Status Badge (positioned near X button in header)
    statusBadge?: StatusBadge;

    // People Count (positioned in footer left)
    peopleCount?: number;
    showPeopleCount?: boolean;
    peopleCountLabel?: string;
    peopleCountIcon?: string;

    // Custom Badges (in header)
    customBadges?: Array<{
        text: string;
        color: string;
        bgColor: string;
        icon?: string;
    }>;

    // Body Content - Dynamic Info Rows
    infoRows?: InfoRowData[];

    // Body Content - Image Configuration
    imageConfig?: ImageConfig;

    // Legacy props for backward compatibility
    zone?: string;
    timestamp?: string;
    description?: string;
    publicAddress?: string;
    showZone?: boolean;
    showDescription?: boolean;
    showPublicAddress?: boolean;

    // Footer Configuration
    buttons?: ButtonConfig[];
    showFooter?: boolean;
    showButtons?: boolean;
    buttonAlignment?: 'left' | 'center' | 'right';
    footerBackgroundColor?: string;

    // Style & Layout
    type?: 'fire_activated' | 'security' | 'emergency' | 'access' | 'gate' | 'cctv' | 'fire_point' | 'custom';
    borderColor?: string;
    backgroundColor?: string;
    headerBackgroundColor?: string;
    bodyBackgroundColor?: string;
    maxWidth?: string;
    className?: string;

    // Animation & Interaction
    animated?: boolean;
    clickable?: boolean;
    onClick?: () => void;
    onCloseX?: () => void;

    // Legacy event handlers for backward compatibility
    onClose?: () => void;
    onAction?: () => void;
}

const AlertCard: React.FC<AlertCardProps> = ({
    // Header defaults
    title = 'Alert',
    subHeader = { text: 'Some info', showText: true, showDate: false },
    icon = 'alert',
    iconColor = 'white',
    iconContainerColor,
    showHeader = true,
    showCloseX = true,

    // Status Badge
    statusBadge,

    // People Count
    peopleCount,
    showPeopleCount = false,
    peopleCountLabel = 'people',

    // Custom Badges
    customBadges = [],

    // Body Content
    infoRows = [],
    imageConfig,

    // Legacy props
    zone,
    timestamp,
    description,
    publicAddress,
    showZone = true,
    showDescription = true,
    showPublicAddress = true,

    // Footer
    buttons = [],
    showFooter = true,
    showButtons = true,
    buttonAlignment = 'right',

    // Style & Layout
    type = 'fire',
    borderColor,
    backgroundColor = '#212633',
    maxWidth = '500px',
    className = '',

    // Animation & Interaction
    animated = false,
    clickable = false,
    onClick,
    onCloseX,

    // Legacy handlers
    onClose,
    onAction,
}) => {
    const getTypeStyles = (alertType: string) => {
        const typeStyles = {
            fire_point: {
                border: '#E87027',
                text: 'text-[#E87027]',
                iconBg: 'bg-[#E87027]',
                header: 'text-white',
                accent: '#E87027',
            },
            security: {
                border: '#E87027',
                text: 'text-[#E87027]',
                iconBg: 'bg-[#E87027]',
                header: 'text-white',
                accent: '#E87027',
            },
            emergency: {
                border: '#E87027',
                text: 'text-[#E87027]',
                iconBg: 'bg-[#E87027]',
                header: 'text-white',
                accent: '#E87027',
            },
            access: {
                border: '#10BCAD',
                text: 'text-[#10BCAD]',
                iconBg: 'bg-[#10BCAD]',
                header: 'text-white',
                accent: '#10BCAD',
            },
            gate: {
                border: '#5C9DD5',
                text: 'text-[#5C9DD5]',
                iconBg: 'bg-[#5C9DD5]',
                header: 'text-white',
                accent: '#5C9DD5',
            },
            cctv: {
                border: '#877BD7',
                text: 'text-[#877BD7]',
                iconBg: 'bg-[#877BD7]',
                header: 'text-white',
                accent: '#877BD7',
            },
            fire_activated: {
                border: '#EC4747',
                text: 'text-[#EC4747]',
                iconBg: 'bg-[#EC4747]',
                header: 'text-white',
                accent: '#EC4747',
            },
            custom: {
                border: '#6B7280',
                text: 'text-gray-400',
                iconBg: 'bg-gray-500',
                header: 'text-white',
                accent: 'gray',
            },
        };

        return typeStyles[alertType as keyof typeof typeStyles] || typeStyles.fire_point;
    };

    const styles = getTypeStyles(type);

    // Resolve final colors
    const finalBorderColor = borderColor || styles.border;
    const finalIconContainerColor = iconContainerColor || styles.iconBg;

    // Build legacy info rows for backward compatibility
    const legacyInfoRows: InfoRowData[] = [];

    if (showZone && zone) {
        legacyInfoRows.push({
            label: 'Zone:',
            value: timestamp ? `${zone} - ${timestamp}` : zone,
            show: true,
        });
    }
    if (showDescription && description) {
        legacyInfoRows.push({
            label: 'Description:',
            value: description,
            show: true,
        });
    }

    if (showPublicAddress && publicAddress) {
        legacyInfoRows.push({
            label: 'Public address:',
            value: publicAddress,
            show: true,
        });
    }

    const allInfoRows = [...legacyInfoRows, ...infoRows];

    // Build legacy buttons for backward compatibility
    const legacyButtons: ButtonConfig[] = [];

    if (onClose) {
        legacyButtons.push({
            text: 'Close',
            onClick: onClose,
            variant: 'secondary',
        });
    }

    if (onAction) {
        legacyButtons.push({
            text: 'Action text',
            onClick: onAction,
            variant: 'primary',
        });
    }

    const allButtons = buttons.length > 0 ? buttons : legacyButtons;

    const getButtonStyles = (variant: ButtonConfig['variant']) => {
        const buttonStyles = {
            primary: '#5D90D4',
            secondary: '#334155',
            danger: '#EF4444',
            success: '#10B981',
        };
        return buttonStyles[variant || 'primary'];
    };

    const getAlignmentClass = (alignment: typeof buttonAlignment) => {
        const alignmentClasses = {
            left: 'justify-start',
            center: 'justify-center',
            right: 'justify-end',
        };
        return alignmentClasses[alignment];
    };

    // Render SubHeader
    const renderSubHeader = () => {
        if (!subHeader || (!subHeader.showText && !subHeader.showDate)) return null;

        const parts = [];

        if (subHeader.showText && subHeader.text) {
            parts.push(
                <span key="text" className={subHeader.textColor || 'text-gray-400'}>
                    {subHeader.text}
                </span>,
            );
        }

        if (subHeader.showDate && subHeader.date) {
            parts.push(
                <span key="date" className={subHeader.dateColor || 'text-gray-500'}>
                    {subHeader.date}
                </span>,
            );
        }

        const separator = subHeader.separator || ' - ';

        return (
            <p className="text-xs leading-none">
                {parts.reduce(
                    (prev, curr, index) => (index === 0 ? [curr] : [...prev, separator, curr]),
                    [] as React.ReactNode[],
                )}
            </p>
        );
    };

    return (
        <div
            className={`
                flex flex-col
                w-full
                ${animated ? 'transition-all duration-300 hover:shadow-2xl' : ''}
                ${clickable ? 'cursor-pointer hover:scale-105' : ''}
                ${className}
                overflow-hidden
                rounded-lg
                shadow-xl
            `}
            style={{
                maxWidth: maxWidth,
                backgroundColor: backgroundColor,
                minHeight: '332px',
                border: `1px solid ${finalBorderColor}`,
            }}
            onClick={clickable ? onClick : undefined}>
            {/* HEADER - Horizontal layout, 80px height */}
            {showHeader && (
                <div
                    className="flex items-center justify-between flex-shrink-0"
                    style={{
                        height: '80px',
                        paddingTop: '16px',
                        paddingRight: '16px',
                        paddingBottom: '8px',
                        paddingLeft: '16px',
                        borderBottom: `1px solid #444D5C`,
                    }}>
                    {/* Left side - Icon, Title, SubHeader, Custom Badges */}
                    <div className="flex items-center flex-1 min-w-0" style={{ gap: '12px' }}>
                        <div
                            className={`${finalIconContainerColor} rounded-full flex items-center justify-center flex-shrink-0`}
                            style={{ width: '32px', height: '32px' }}>
                            <SvgIcon name={icon} size="default" strokeColor={iconColor} />
                        </div>
                        <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 flex-wrap mb-1">
                                <h3
                                    className="truncate"
                                    style={{
                                        color: styles.accent,
                                        fontFamily: 'Poppins',
                                        fontWeight: 600,
                                        fontSize: '16px',
                                        lineHeight: '100%',
                                        letterSpacing: '0%',
                                    }}>
                                    {title}
                                </h3>

                                {/* Custom Badges */}
                                {customBadges.map((badge, index) => (
                                    <div
                                        key={index}
                                        className="flex items-center gap-1 text-xs px-2 py-1 rounded-full flex-shrink-0"
                                        style={{
                                            backgroundColor: badge.bgColor,
                                            color: badge.color,
                                        }}>
                                        {badge.icon && (
                                            <SvgIcon name={badge.icon} size="sm" strokeColor="currentColor" />
                                        )}
                                        <span>{badge.text}</span>
                                    </div>
                                ))}
                            </div>
                            {renderSubHeader()}
                        </div>
                    </div>

                    {/* Right side - Status Badge and Close X Button */}
                    <div className="flex items-center flex-shrink-0 ml-2" style={{ gap: '6px' }}>
                        {/* Status Badge - Tag style: 65x29px, 4px radius, 4px padding */}
                        {statusBadge?.show && (
                            <div
                                className="flex items-center justify-center flex-shrink-0"
                                style={{
                                    width: '65px',
                                    height: '29px',
                                    backgroundColor: '#76FE9233',
                                    // dark green bg
                                    borderRadius: '4px',
                                    padding: '4px',
                                }}>
                                <span
                                    className="text-center"
                                    style={{
                                        color: '#76FE92', // bright green text
                                        fontFamily: 'Poppins',
                                        fontWeight: 400,
                                        fontSize: '14px',
                                        lineHeight: '100%',
                                    }}>
                                    {statusBadge.text}
                                </span>
                            </div>
                        )}

                        {/* Close X Button - 24x24px */}
                        {showCloseX && onCloseX && (
                            <button
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onCloseX();
                                }}
                                className="hover:bg-gray-700 rounded transition-colors flex-shrink-0 flex items-center justify-center"
                                style={{
                                    width: '24px',
                                    height: '24px',
                                }}
                                aria-label="Close">
                                <SvgIcon name="cancel" size="lg" strokeColor="white" />
                            </button>
                        )}
                    </div>
                </div>
            )}

            {/* BODY - Vertical layout, 160px height, 16px padding */}
            <div
                className="flex-1 flex flex-col"
                style={{
                    minHeight: '160px',
                    padding: '16px',
                    gap: '12px',
                }}>
                {/* Image - 468x220px with 8px radius */}
                {imageConfig && (
                    <div className="flex-shrink-0">
                        <Image
                            src={imageConfig.src}
                            alt={imageConfig.alt}
                            width={imageConfig.width || 468}
                            height={imageConfig.height || 220}
                            className={`object-cover rounded-lg bg-black transition-opacity ${
                                imageConfig.onClick ? 'cursor-pointer hover:opacity-90' : ''
                            } ${imageConfig.className || ''}`}
                            onClick={imageConfig.onClick}
                        />
                    </div>
                )}

                {/* Text Container - 468x48px with 23px gap */}
                {allInfoRows.length > 0 && (
                    <div
                        className="flex-1"
                        style={{
                            width: '468px',
                            minHeight: '48px',
                            gap: '23px',
                        }}>
                        {allInfoRows.map(
                            (row, index) =>
                                row.show !== false && (
                                    <div key={index} className="flex justify-between items-start mb-3">
                                        <div className="flex items-center gap-2 flex-shrink-0">
                                            {row.icon && (
                                                <SvgIcon name={row.icon} size="sm" strokeColor="currentColor" />
                                            )}
                                            <span
                                                className={row.labelColor || 'text-white'}
                                                style={{
                                                    fontFamily: 'Poppins',
                                                    fontWeight: 600,
                                                    fontSize: '16px',
                                                    lineHeight: '100%',
                                                    letterSpacing: '0%',
                                                    width: '140px',
                                                    height: '24px',
                                                    verticalAlign: 'middle',
                                                }}>
                                                {row.label}
                                            </span>
                                        </div>
                                        <div className="text-right flex-1 ml-4 flex items-center justify-end gap-2 min-w-0">
                                            <span
                                                className={row.valueColor || 'text-[#B8BDBE]'}
                                                style={{
                                                    fontFamily: 'Poppins',
                                                    fontWeight: 400,
                                                    fontSize: '16px',
                                                    lineHeight: '100%',
                                                    letterSpacing: '0%',
                                                    width: '305px',
                                                    height: '48px',
                                                    verticalAlign: 'middle',
                                                    wordWrap: 'break-word',
                                                }}>
                                                {row.value}
                                            </span>
                                            {row.badge && (
                                                <span
                                                    className="text-xs px-2 py-1 rounded-full flex-shrink-0"
                                                    style={{
                                                        backgroundColor: row.badge.bgColor,
                                                        color: row.badge.color,
                                                    }}>
                                                    {row.badge.text}
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                ),
                        )}
                    </div>
                )}
            </div>

            {/* FOOTER - Horizontal layout, 68px height */}
            {showFooter && (showPeopleCount || (showButtons && allButtons.length > 0)) && (
                <div
                    className="flex items-center justify-between flex-shrink-0"
                    style={{
                        height: '68px',
                        paddingTop: '12px',
                        paddingRight: '8px',
                        paddingBottom: '12px',
                        paddingLeft: '8px',
                        gap: '12px',
                        backgroundColor: '#141D2E',
                        borderTop: `1px solid #1F2937`,
                        borderBottomLeftRadius: '8px',
                        borderBottomRightRadius: '8px',
                    }}>
                    {/* People Count Card - Based on Figma design */}
                    <div
                        className="flex items-center"
                        style={{
                            width: '174px',
                            height: '37px',
                            padding: '4px 10px',
                            gap: '12px',
                            backgroundColor: 'rgba(91, 157, 213, 0.2)',
                            borderRadius: '8px',
                            border: '1px solid #5C9DD5',
                        }}>
                        {/* People Count Card - Fixed 150x21 */}
                        <div
                            className="flex items-center"
                            style={{
                                width: '150px',
                                height: '21px',
                                padding: '0 8px',
                                gap: '4px',
                            }}>
                            {/* Circle with number */}
                            <div
                                className="flex items-center justify-center"
                                style={{
                                    width: '20px',
                                    height: '20px',
                                    backgroundColor: '#5C9DD5',
                                    borderRadius: '50%',
                                }}>
                                <span
                                    style={{
                                        color: 'white',
                                        fontFamily: 'Poppins',
                                        fontWeight: 400,
                                        fontSize: '12px',
                                        lineHeight: '100%',
                                        textAlign: 'center',
                                    }}>
                                    {peopleCount}
                                </span>
                            </div>

                            {/* Label text */}
                            <span
                                style={{
                                    color: '#5C9DD5',
                                    fontFamily: 'Poppins',
                                    fontWeight: 500,
                                    fontSize: '14px',
                                    lineHeight: '100%',
                                    textAlign: 'center',
                                    alignItems: 'center',
                                }}>
                                {peopleCountLabel}
                            </span>
                        </div>
                    </div>

                    {/* Right side - Action Buttons */}
                    <div className={`flex gap-3 ${getAlignmentClass(buttonAlignment)} flex-shrink-0`}>
                        {showButtons &&
                            allButtons.map((button, index) => (
                                <button
                                    key={index}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        button.onClick();
                                    }}
                                    disabled={button.disabled || button.loading}
                                    className="text-white transition-colors focus:outline-none focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 flex-shrink-0"
                                    style={{
                                        height: '36px',
                                        borderRadius: '4px',
                                        paddingTop: '16px',
                                        paddingRight: '8px',
                                        paddingBottom: '16px',
                                        paddingLeft: '8px',
                                        backgroundColor: getButtonStyles(button.variant),
                                        fontSize: '14px',
                                        fontWeight: 500,
                                    }}>
                                    {button.loading && (
                                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                                    )}
                                    {button.icon && !button.loading && (
                                        <SvgIcon name={button.icon} size="sm" strokeColor="white" />
                                    )}
                                    {button.text}
                                </button>
                            ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export default AlertCard;
