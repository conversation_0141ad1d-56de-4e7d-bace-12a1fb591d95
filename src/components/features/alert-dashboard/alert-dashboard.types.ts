/**
 * Alert Dashboard Types
 * TypeScript interfaces and types for alert dashboard components
 */

import { ReactNode } from 'react';

/**
 * Props for the main AlertDashboardLayout component
 */
export interface AlertDashboardLayoutProps {
    children?: ReactNode;
    className?: string;
    rightDrawerOpen?: boolean;
    onRightDrawerToggle?: () => void;
}

/**
 * Props for LeftSidebar component
 */
export interface LeftSidebarProps {
    className?: string;
}

/**
 * Props for TopBar component
 */
export interface TopBarProps {
    className?: string;
}

/**
 * Props for RightDrawer component
 */
export interface RightDrawerProps {
    isOpen?: boolean;
    onToggle?: () => void;
    className?: string;
}

/**
 * Props for ContentBody component
 */
export interface ContentBodyProps {
    children?: ReactNode;
    className?: string;
}
