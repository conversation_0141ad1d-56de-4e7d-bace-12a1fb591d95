'use client';

import React, { useEffect } from 'react';
import { useSystemsStore } from '@/stores/system.store';
import SystemCard from '../SystemCard/SystemCard';
import { cn } from '@/shared/utils';
import FloorAlertList from '@/components/features/floor-alert/FloorAlertList';
import { BuildingDropdown } from '@/components/common/BuildingDropdown';

interface SystemOverviewProps {
    className?: string;
}

/**
 * SystemOverview Component
 * Displays all systems using SystemCard components
 * Data is fetched from the systems store
 */
function SystemOverview({ className }: SystemOverviewProps) {
    const { systems, isLoading, loadSystems } = useSystemsStore();

    // Event handlers
    const handleBuildingChange = (building: string) => {
        console.log('Selected building:', building);
    };

    const handleFloorSelect = (floorId: string) => {
        console.log('Selected floor:', floorId);
    };

    // Load systems data on component mount
    useEffect(() => {
        loadSystems();
    }, [loadSystems]);

    if (isLoading) {
        return (
            <div className={cn('flex items-center justify-center p-8', className)}>
                <div className="text-gray-400">Loading systems...</div>
            </div>
        );
    }

    if (systems.length === 0) {
        return (
            <div className={cn('flex items-center justify-center p-8', className)}>
                <div className="text-gray-400">No systems available</div>
            </div>
        );
    }

    return (
        <div className={cn('', className)}>
            {/* Systems Section */}
            <div className="text-white font-poppins font-bold text-[18px] leading-[100%] mb-3">System Overview</div>
            <div className="space-y-2 mb-6">
                {systems.map((system, index) => (
                    <SystemCard
                        key={`${system.title}-${index}`}
                        title={system.title}
                        iconName={system.iconName}
                        iconColor={system.iconColor}
                        metrics={system.metrics}
                    />
                ))}
            </div>

            {/* Floor List Section */}
            <div className="space-y-3">
                {/* Building Selection Header and Dropdown on same row */}
                <div className="flex items-center justify-between">
                    <div className="text-white font-poppins font-semibold text-[18px] leading-none">Floors Alerts</div>
                    <BuildingDropdown />
                </div>

                {/* Floor List */}
                <FloorAlertList
                    onBuildingChange={handleBuildingChange}
                    onFloorSelect={handleFloorSelect}
                    initialSelectedFloor="1"
                />
            </div>
        </div>
    );
}

export default SystemOverview;
