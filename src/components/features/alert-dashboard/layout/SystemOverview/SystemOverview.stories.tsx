import type { Meta, StoryObj } from '@storybook/react';
import SystemOverview from './SystemOverview';

const meta: Meta<typeof SystemOverview> = {
    title: 'Features/AlertDashboard/SystemOverview',
    component: SystemOverview,
    parameters: {
        layout: 'padded',
        backgrounds: {
            default: 'dark',
            values: [
                { name: 'dark', value: '#0d131f' },
                { name: 'light', value: '#ffffff' },
            ],
        },
    },
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {
        className: 'max-w-md',
    },
};

export const WithCustomClassName: Story = {
    args: {
        className: 'max-w-lg p-6',
    },
};

export const InDrawerContext: Story = {
    args: {},
    decorators: [
        (Story) => (
            <div className="bg-[#0d131ff2] p-4 min-h-screen">
                <div className="text-white text-lg font-semibold mb-4">System Overview</div>
                <Story />
            </div>
        ),
    ],
};
