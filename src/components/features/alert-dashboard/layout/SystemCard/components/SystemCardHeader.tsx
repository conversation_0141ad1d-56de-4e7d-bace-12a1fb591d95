import React from 'react';
import { cn } from '@/shared/utils';
import { SvgIcon } from '@/components/common/ui/SvgIcon';
import { SystemCardHeaderProps } from '../system-card.types';

function SystemCardHeader({ title, iconName, iconColor, icon, className }: SystemCardHeaderProps) {
    const renderIcon = () => {
        // Priority: iconName (SVG) > icon (React component or emoji)
        if (iconName) {
            return (
                <div className="w-6 h-6">
                    <SvgIcon name={iconName} size="lg" color={iconColor} />
                </div>
            );
        }

        if (typeof icon === 'string') {
            return <div className="w-6 h-6 text-lg ">{icon}</div>;
        }

        if (icon && typeof icon !== 'string') {
            const IconComponent = icon;
            return (
                <div className="w-6 h-6 text-lg">
                    <IconComponent className="w-8 h-8 text-gray-600" />
                </div>
            );
        }

        // Fallback icon
        return (
            <div className="w-14 h-14 bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl border border-gray-200/50">
                <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
            </div>
        );
    };

    return (
        <div className={cn('flex items-center gap-1.5', className)}>
            {renderIcon()}
            <div>
                <h3 className="text-lg font-medium text-white">{title}</h3>
            </div>
        </div>
    );
}

export default SystemCardHeader;
