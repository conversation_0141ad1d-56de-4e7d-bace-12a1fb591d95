import type { Meta, StoryObj } from '@storybook/react';
import SystemCard from './SystemCard';

const meta: Meta<typeof SystemCard> = {
    title: 'Features/Alert Dashboard/SystemCard',
    component: SystemCard,
    parameters: {
        layout: 'padded',
        docs: {
            description: {
                component: 'A reusable system card component that displays system information with metrics and alerts.',
            },
        },
    },
    argTypes: {
        title: {
            control: 'text',
            description: 'The title of the system card',
        },
        iconName: {
            control: 'select',
            options: ['fire', 'door', 'camera', 'gateBarrier', 'building', 'bell'],
            description: 'SVG icon name from public/svg/',
        },
        iconColor: {
            control: 'color',
            description: 'Color for the icon',
        },
        metrics: {
            control: 'object',
            description: 'Array of metrics to display',
        },
    },
};

export default meta;
type Story = StoryObj<typeof SystemCard>;

// Fire Alarm System Story
export const FireAlarmSystem: Story = {
    args: {
        title: 'Fire Alarm System',
        iconName: 'fire',
        iconColor: '#ff4444',
        metrics: [
            {
                key: 'Total Devices',
                value: '42',
                isAlert: false,
            },
            {
                key: 'Active Alarms',
                value: '5',
                isAlert: true,
            },
        ],
    },
};

// Access Control System Story
export const AccessControlSystem: Story = {
    args: {
        title: 'Access Control',
        iconName: 'door',
        iconColor: '#4444ff',
        metrics: [
            {
                key: 'Total Doors',
                value: '28',
                isAlert: false,
            },
            {
                key: 'Open',
                value: '12',
                isAlert: false,
            },
            {
                key: 'Closed',
                value: '16',
                isAlert: false,
            },
        ],
    },
};

// CCTV System Story
export const CCTVSystem: Story = {
    args: {
        title: 'CCTV System',
        iconName: 'camera',
        iconColor: '#44ff44',
        metrics: [
            {
                key: 'Total Cameras',
                value: '156',
                isAlert: false,
            },
            {
                key: 'Active Incidents',
                value: '2',
                isAlert: true,
            },
        ],
    },
};

// Gate Barriers System Story
export const GateBarriersSystem: Story = {
    args: {
        title: 'Gate Barriers',
        iconName: 'gateBarrier',
        iconColor: '#ffaa44',
        metrics: [
            {
                key: 'Total Barriers',
                value: '8',
                isAlert: false,
            },
            {
                key: 'Unauthorized Attempts',
                value: '3',
                isAlert: true,
            },
        ],
    },
};

// With Emoji Icons
export const WithEmojiIcons: Story = {
    args: {
        title: 'Fire Alarm System',
        iconName: 'fire',
        iconColor: '#ff4444',
        metrics: [
            {
                key: 'Total Devices',
                value: '42',
                isAlert: false,
            },
            {
                key: 'Active Alarms',
                value: '5',
                isAlert: true,
            },
        ],
    },
};

// Loading State
export const LoadingState: Story = {
    args: {
        title: 'Fire Alarm System',
        iconName: 'fire',
        iconColor: '#ff4444',
        metrics: [
            {
                key: 'Total Devices',
                value: '42',
                isAlert: false,
            },
            {
                key: 'Active Alarms',
                value: '5',
                isAlert: true,
            },
        ],
    },
};

// Interactive Card
export const InteractiveCard: Story = {
    args: {
        title: 'Fire Alarm System',
        iconName: 'fire',
        iconColor: '#ff4444',
        metrics: [
            {
                key: 'Total Devices',
                value: '42',
                isAlert: false,
            },
            {
                key: 'Active Alarms',
                value: '5',
                isAlert: true,
            },
        ],
    },
};

// All Systems Grid - Mixed Icons (SVG + Emoji)
export const AllSystemsGrid: Story = {
    render: () => (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-6xl">
            <SystemCard
                title="Fire Alarm System"
                iconName="fire"
                iconColor="#ff4444"
                metrics={[
                    { key: 'Total Devices', value: '42', isAlert: false },
                    { key: 'Active Alarms', value: '5', isAlert: true },
                ]}
            />
            <SystemCard
                title="Access Control"
                iconName="door"
                iconColor="#4444ff"
                metrics={[
                    { key: 'Total Doors', value: '28', isAlert: false },
                    { key: 'Open', value: '12', isAlert: false },
                    { key: 'Closed', value: '16', isAlert: false },
                ]}
            />
            <SystemCard
                title="CCTV System"
                iconName="camera"
                iconColor="#44ff44"
                metrics={[
                    { key: 'Total Cameras', value: '156', isAlert: false },
                    { key: 'Active Incidents', value: '2', isAlert: true },
                ]}
            />
            <SystemCard
                title="Gate Barriers"
                iconName="gateBarrier"
                iconColor="#ffaa44"
                metrics={[
                    { key: 'Total Barriers', value: '8', isAlert: false },
                    { key: 'Unauthorized Attempts', value: '3', isAlert: true },
                ]}
            />
        </div>
    ),
};

// Building Management System - Additional Example
export const BuildingManagement: Story = {
    args: {
        title: 'Building Management',
        iconName: 'building',
        iconColor: '#aa44ff',
        metrics: [
            {
                key: 'Total Floors',
                value: '25',
                isAlert: false,
            },
            {
                key: 'Occupied',
                value: '23',
                isAlert: false,
            },
            {
                key: 'Maintenance',
                value: '2',
                isAlert: true,
            },
        ],
    },
};
