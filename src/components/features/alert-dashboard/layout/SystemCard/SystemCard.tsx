import React from 'react';
import { cn } from '@/shared/utils';

// Import types
import { System as SystemCardProps } from '@/infrastructure/api/systems/types';
import SystemCardHeader from './components/SystemCardHeader';
import SystemCardMetrics from './components/SystemCardMetrics';

// SystemCard Component
function SystemCard({ title, iconName, iconColor, metrics }: SystemCardProps) {
    return (
        <div
            className={cn(
                'bg-[#212633] rounded-[8px] shadow-sm border-1 border-[#1F2937] py-4 px-2 transition-all duration-300',
                'hover:shadow-lg hover:shadow-gray-200/50 hover:border-gray-300/60',
            )}>
            {/* Header Section */}
            <SystemCardHeader title={title} iconName={iconName} iconColor={iconColor} className="mb-3" />

            {/* Metrics Section */}
            <SystemCardMetrics primaryMetric={metrics} />
        </div>
    );
}

export default SystemCard;
