import React from 'react';
import { Metric } from '@/infrastructure/api/systems/types';

/**
 * System card header props interface
 */
export interface SystemCardHeaderProps {
    title: string;
    iconName?: string;
    iconColor?: string;
    icon?: React.ComponentType<{ className?: string }> | string;
    className?: string;
}

/**
 * System card metrics props interface
 */
export interface SystemCardMetricsProps {
    primaryMetric: Metric[];
    className?: string;
}
