'use client';

import React from 'react';
import BuildingDropdown from '../../../common/BuildingDropdown';
import FloorDropdown from '../../../common/FloorDropdown';
import CalendarDropdown from '../../../common/CalendarDropdown';

/**
 * TopBar Component
 * Redesigned header for the alert dashboard (dropdowns left, right section simplified)
 */
const TopBar: React.FC = () => {
    return (
        <div
            className="bg-[#0d131ff2] border border-gray-700 flex items-center justify-center"
            style={{ gridArea: 'topbar', height: '80px', minWidth: '600px' }}>
            <div className="flex items-center justify-between w-full" style={{ padding: '16px 19px 16px 19px' }}>
                {/* Left: Dropdowns (start of row, horizontal, 20px gap) */}
                <div className="flex items-center gap-[20px] ">
                    <BuildingDropdown />
                    <FloorDropdown />
                    <CalendarDropdown />
                </div>

                {/* Right: Notification and Avatar (no container, smaller gap, aligned right) */}
                <div className="flex items-center gap-[6px]">
                    {/* Notification Icon (with hover/animation) */}
                    <div className="flex items-center justify-center w-[40px] h-[40px] relative transition-all duration-200 ease-in-out hover:scale-110 hover:shadow-lg hover:bg-[#232b3a] rounded-full cursor-pointer">
                        <svg width="22" height="22" viewBox="0 0 24 24" fill="none">
                            <path
                                d="M12 2C9.243 2 7 4.243 7 7v3.764c0 .538-.214 1.055-.595 1.436L5.293 13.313A1 1 0 0 0 6 15h12a1 1 0 0 0 .707-1.687l-1.112-1.113A2.03 2.03 0 0 1 17 10.764V7c0-2.757-2.243-5-5-5zm0 20a2.978 2.978 0 0 0 2.816-2H9.184A2.978 2.978 0 0 0 12 22z"
                                fill="#fff"
                            />
                            <circle cx="20" cy="3" r="5" fill="#FF4B4B" stroke="#fff" strokeWidth="1.5" />
                        </svg>
                    </div>
                    {/* Avatar (with hover/animation) */}
                    <div className="flex items-center justify-center bg-[#444D5C] text-white font-semibold text-base rounded-full w-[40px] h-[40px] transition-all duration-200 ease-in-out hover:scale-110 hover:shadow-lg hover:bg-[#232b3a] cursor-pointer">
                        HA
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TopBar;
