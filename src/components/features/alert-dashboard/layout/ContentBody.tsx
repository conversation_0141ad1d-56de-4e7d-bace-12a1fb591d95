'use client';

import React from 'react';
import { ContentBodyProps } from '../alert-dashboard.types';

/**
 * ContentBody Component
 * Main content area wrapper for the alert dashboard
 */
function ContentBody({ children, className }: ContentBodyProps) {
    return (
        <div
            className={`h-full overflow-y-auto bg-gray-900 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800 ${className || ''}`}
            style={{ gridArea: 'content' }}>
            {children}
        </div>
    );
}

export default ContentBody;
