'use client';

import React from 'react';
import { LeftSidebarProps } from '../alert-dashboard.types';
import VerticalNavigationBar from '../../vertical-navigation-bar/vertical-nav-bar';

/**
 * LeftSidebar Component
 * Navigation sidebar for the alert dashboard
 */

function LeftSidebar(className: LeftSidebarProps) {
    return (
        <div className={`w-16 ${className || ''}`} style={{ gridArea: 'sidebar' }}>
            <VerticalNavigationBar
                // activeItem={activeItem}
                // onItemClick={onItemClick}
                showLogo={true}
                logoSrc="/svg/appLogo.svg"
                className="bg-transparent border-none"
            />
        </div>
    );
}

export default LeftSidebar;
