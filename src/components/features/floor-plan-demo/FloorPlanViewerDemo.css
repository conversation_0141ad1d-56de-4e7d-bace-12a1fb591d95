/* Main Content Body */
.content-body {
    flex: 1;
    background-color: #1a1a1a;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0;
    width: 100%;
    height: 100vh;
}

/* Fullscreen Mode */
.content-body.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background-color: #000;
}

/* Loading State */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #1a1a1a;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #333;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.loading-text {
    color: #888;
    font-size: 14px;
}

/* Error State */
.error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.error-content {
    text-align: center;
    max-width: 400px;
    padding: 40px;
}

.error-icon {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
}

.error-title {
    color: #fff;
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 12px 0;
}

.error-message {
    color: #888;
    font-size: 14px;
    margin: 0 0 24px 0;
    line-height: 1.5;
}

.retry-button {
    background-color: #007bff;
    color: #fff;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.retry-button:hover {
    background-color: #0056b3;
}

/* Zoom Controls */
.zoom-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    background-color: rgba(42, 42, 42, 0.9);
    border-radius: 8px;
    padding: 8px;
    gap: 4px;
    z-index: 5;
    backdrop-filter: blur(8px);
}

.zoom-button {
    width: 32px;
    height: 32px;
    background-color: #333;
    border: none;
    border-radius: 4px;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.zoom-button:hover:not(:disabled) {
    background-color: #444;
}

.zoom-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.zoom-level {
    color: #fff;
    font-size: 12px;
    font-weight: 500;
    padding: 0 8px;
    min-width: 40px;
    text-align: center;
}

/* Plan Container */
.plan-container {
    flex: 1;
    position: relative;
    overflow: auto;
    cursor: grab;
    background-color: #1a1a1a;
    background-image: radial-gradient(circle, #333 1px, transparent 1px);
    background-size: 20px 20px;
    background-position:
        0 0,
        10px 10px;
    scrollbar-width: thin;
    scrollbar-color: #444 #1a1a1a;
}

/* Webkit scrollbar styling */
.plan-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.plan-container::-webkit-scrollbar-track {
    background: #1a1a1a;
}

.plan-container::-webkit-scrollbar-thumb {
    background: #444;
    border-radius: 4px;
}

.plan-container::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.plan-container.fullscreen-container {
    background-color: #000;
}

.plan-container.dragging {
    cursor: grabbing;
    user-select: none;
    overflow: hidden;
}

.plan-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    transition: transform 0.1s ease-out;
    transform-origin: 0 0;
    min-width: 100%;
    min-height: 100%;
}

/* Floor Plan Image */
.floor-plan-image {
    display: block;
    max-width: none;
    height: auto;
    user-select: none;
    pointer-events: none;
}

/* Placeholder Plan */
.placeholder-plan {
    width: 800px;
    height: 600px;
    background-color: #2a2a2a;
    border: 2px dashed #444;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.placeholder-content {
    text-align: center;
    padding: 40px;
}

.placeholder-title {
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    margin: 16px 0 12px 0;
}

.placeholder-text {
    color: #888;
    font-size: 14px;
    margin: 0;
    line-height: 1.5;
}

/* Device Markers */
.device-marker {
    position: absolute;
    width: 24px;
    height: 24px;
    transform: translate(-50%, -50%);
    cursor: pointer;
    z-index: 3;
    transition: all 0.2s ease;
}

.device-marker:hover {
    transform: translate(-50%, -50%) scale(1.2);
    z-index: 4;
}

.marker-icon {
    width: 100%;
    height: 100%;
    background-color: #333;
    border: 2px solid #555;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    position: relative;
}

/* Device Status Colors */
.device-marker.status-normal .marker-icon {
    border-color: #28a745;
    background-color: rgba(40, 167, 69, 0.2);
}

.device-marker.status-warning .marker-icon {
    border-color: #ffc107;
    background-color: rgba(255, 193, 7, 0.2);
}

.device-marker.status-danger .marker-icon {
    border-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.2);
}

.device-marker.status-offline .marker-icon {
    border-color: #6c757d;
    background-color: rgba(108, 117, 125, 0.2);
}

.marker-status {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #dc3545;
    border: 1px solid #1a1a1a;
}

/* Alert Markers */
.alert-marker {
    position: absolute;
    width: 32px;
    height: 32px;
    transform: translate(-50%, -50%);
    cursor: pointer;
    z-index: 4;
    transition: all 0.2s ease;
}

.alert-marker:hover {
    transform: translate(-50%, -50%) scale(1.1);
    z-index: 5;
}

.alert-icon {
    width: 100%;
    height: 100%;
    background-color: #dc3545;
    border: 2px solid #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #fff;
    position: relative;
    z-index: 2;
}

/* Alert Severity Colors */
.alert-marker.severity-low .alert-icon {
    background-color: #17a2b8;
}

.alert-marker.severity-medium .alert-icon {
    background-color: #ffc107;
    color: #000;
}

.alert-marker.severity-high .alert-icon {
    background-color: #fd7e14;
}

.alert-marker.severity-critical .alert-icon {
    background-color: #dc3545;
}

.alert-marker.acknowledged .alert-icon {
    opacity: 0.6;
}

.alert-pulse {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 50%;
    background-color: inherit;
    opacity: 0.6;
    animation: pulse 2s infinite;
    z-index: 1;
}

.alert-marker.acknowledged .alert-pulse {
    display: none;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.5);
        opacity: 0.3;
    }
    100% {
        transform: scale(2);
        opacity: 0;
    }
}

/* Plan Info */
.plan-info {
    position: absolute;
    bottom: 20px;
    left: 20px;
    display: flex;
    align-items: center;
    gap: 20px;
    background-color: rgba(42, 42, 42, 0.9);
    border-radius: 8px;
    padding: 12px 16px;
    backdrop-filter: blur(8px);
    z-index: 5;
}

.plan-details {
    display: flex;
    align-items: center;
    gap: 12px;
}

.building-info {
    color: #fff;
    font-size: 14px;
    font-weight: 500;
}

.plan-stats {
    display: flex;
    align-items: center;
    gap: 16px;
}

.device-count,
.alert-count {
    color: #888;
    font-size: 12px;
}

.alert-count {
    color: #dc3545;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .zoom-controls {
        top: 10px;
        right: 10px;
        padding: 6px;
    }

    .zoom-button {
        width: 28px;
        height: 28px;
        font-size: 14px;
    }

    .plan-info {
        bottom: 10px;
        left: 10px;
        padding: 8px 12px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .device-marker {
        width: 20px;
        height: 20px;
    }

    .alert-marker {
        width: 28px;
        height: 28px;
    }
}
