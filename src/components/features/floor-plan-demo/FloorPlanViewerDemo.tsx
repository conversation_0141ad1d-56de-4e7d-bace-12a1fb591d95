'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import Image from 'next/image';
import { SvgIcon } from '@/components/common/ui/SvgIcon';
import './FloorPlanViewerDemo.css';
import { cn } from '@/shared/utils';

// Device marker structure
export interface DeviceMarker {
    id: string;
    type: 'fire-alarm' | 'access-control' | 'cctv' | 'gate-barrier';
    position: { x: number; y: number }; // Percentage-based positioning
    status: 'normal' | 'warning' | 'danger' | 'offline';
    label?: string;
    metadata?: Record<string, unknown>;
}

// Alert marker structure
export interface AlertMarker {
    id: string;
    type: 'fire' | 'security' | 'access' | 'system';
    position: { x: number; y: number };
    severity: 'low' | 'medium' | 'high' | 'critical';
    title: string;
    description?: string;
    timestamp: Date;
    acknowledged?: boolean;
}

export interface FloorPlanViewerDemoProps {
    className?: string;
    floorPlanUrl?: string;
    floorPlanAlt?: string;
    devices?: DeviceMarker[];
    alerts?: AlertMarker[];
    selectedFloor?: string;
    selectedBuilding?: string;
    onDeviceClick?: (deviceId: string) => void;
    onAlertClick?: (alertId: string) => void;
    onPlanClick?: (coordinates: { x: number; y: number }) => void;
    zoom?: number;
    onZoomChange?: (zoom: number) => void;
    panPosition?: { x: number; y: number };
    onPanChange?: (position: { x: number; y: number }) => void;
    isLoading?: boolean;
    error?: string;
}

// Helper functions
function getDeviceIcon(type: string): string {
    const icons = {
        'fire-alarm': 'fire',
        'access-control': 'door',
        cctv: 'camera',
        'gate-barrier': 'gateBarrier',
    };
    return icons[type as keyof typeof icons] || 'building';
}

function getAlertIcon(type: string): string {
    const icons = {
        fire: 'fire',
        security: 'bell',
        access: 'door',
        system: 'building',
    };
    return icons[type as keyof typeof icons] || 'bell';
}

export function FloorPlanViewerDemo({
    className,
    floorPlanUrl,
    floorPlanAlt = 'Floor plan',
    devices = [],
    alerts = [],
    selectedFloor,
    selectedBuilding,
    onDeviceClick,
    onAlertClick,
    onPlanClick,
    zoom = 1,
    onZoomChange,
    panPosition = { x: 0, y: 0 },
    onPanChange,
    isLoading = false,
    error,
}: FloorPlanViewerDemoProps) {
    const containerRef = useRef<HTMLDivElement>(null);
    const imageRef = useRef<HTMLImageElement>(null);
    const [isDragging, setIsDragging] = useState(false);
    const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
    const [imageLoaded, setImageLoaded] = useState(false);
    const [imageDimensions, setImageDimensions] = useState({ width: 0, height: 0 });
    const [isFullscreen, setIsFullscreen] = useState(false);

    // Mouse/touch handling for pan
    const handleMouseDown = useCallback(
        (e: React.MouseEvent) => {
            if (e.button === 0) {
                // Left mouse button
                setIsDragging(true);
                setDragStart({
                    x: e.clientX - panPosition.x,
                    y: e.clientY - panPosition.y,
                });
            }
        },
        [panPosition],
    );

    const handleMouseMove = useCallback(
        (e: React.MouseEvent) => {
            if (isDragging) {
                const newPosition = {
                    x: e.clientX - dragStart.x,
                    y: e.clientY - dragStart.y,
                };
                onPanChange?.(newPosition);
            }
        },
        [isDragging, dragStart, onPanChange],
    );

    const handleMouseUp = useCallback(() => {
        setIsDragging(false);
    }, []);

    // Wheel zoom handling
    const handleWheel = useCallback(
        (e: React.WheelEvent) => {
            e.preventDefault();
            const delta = e.deltaY > 0 ? 0.9 : 1.1;
            const newZoom = Math.max(0.1, Math.min(5, zoom * delta));
            onZoomChange?.(newZoom);
        },
        [zoom, onZoomChange],
    );

    // Image loading and error handling
    const handleImageLoad = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
        const img = e.currentTarget;
        setImageDimensions({
            width: img.naturalWidth,
            height: img.naturalHeight,
        });
        setImageLoaded(true);
    }, []);

    const handleImageError = useCallback(() => {
        setImageLoaded(false);
    }, []);

    // Fullscreen functionality
    const toggleFullscreen = useCallback(async () => {
        if (!containerRef.current) return;

        try {
            if (!isFullscreen) {
                const element = containerRef.current as HTMLElement & {
                    webkitRequestFullscreen?: () => Promise<void>;
                    msRequestFullscreen?: () => Promise<void>;
                };
                if (element.requestFullscreen) {
                    await element.requestFullscreen();
                } else if (element.webkitRequestFullscreen) {
                    await element.webkitRequestFullscreen();
                } else if (element.msRequestFullscreen) {
                    await element.msRequestFullscreen();
                }
            } else {
                const doc = document as Document & {
                    webkitExitFullscreen?: () => Promise<void>;
                    msExitFullscreen?: () => Promise<void>;
                };
                if (doc.exitFullscreen) {
                    await doc.exitFullscreen();
                } else if (doc.webkitExitFullscreen) {
                    await doc.webkitExitFullscreen();
                } else if (doc.msExitFullscreen) {
                    await doc.msExitFullscreen();
                }
            }
        } catch (error) {
            console.warn('Fullscreen operation failed:', error);
        }
    }, [isFullscreen]);

    // Listen for fullscreen changes
    useEffect(() => {
        const handleFullscreenChange = () => {
            const doc = document as Document & {
                webkitFullscreenElement?: Element;
                msFullscreenElement?: Element;
            };
            const isCurrentlyFullscreen = !!(
                doc.fullscreenElement ||
                doc.webkitFullscreenElement ||
                doc.msFullscreenElement
            );
            setIsFullscreen(isCurrentlyFullscreen);
        };

        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('msfullscreenchange', handleFullscreenChange);

        return () => {
            document.removeEventListener('fullscreenchange', handleFullscreenChange);
            document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
            document.removeEventListener('msfullscreenchange', handleFullscreenChange);
        };
    }, []);

    // Convert screen coordinates to plan coordinates
    const screenToPlanCoordinates = useCallback(
        (screenX: number, screenY: number) => {
            if (!containerRef.current || !imageRef.current) return { x: 0, y: 0 };

            const containerRect = containerRef.current.getBoundingClientRect();
            const relativeX = screenX - containerRect.left - panPosition.x;
            const relativeY = screenY - containerRect.top - panPosition.y;

            const planX = (relativeX / zoom / imageDimensions.width) * 100;
            const planY = (relativeY / zoom / imageDimensions.height) * 100;

            return { x: planX, y: planY };
        },
        [panPosition, zoom, imageDimensions],
    );

    const handlePlanClick = useCallback(
        (e: React.MouseEvent) => {
            if (!isDragging && imageLoaded) {
                const coordinates = screenToPlanCoordinates(e.clientX, e.clientY);
                onPlanClick?.(coordinates);
            }
        },
        [isDragging, imageLoaded, screenToPlanCoordinates, onPlanClick],
    );

    // Keyboard navigation
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (!containerRef.current?.contains(document.activeElement)) return;

            switch (e.key) {
                case 'ArrowUp':
                    e.preventDefault();
                    onPanChange?.({ ...panPosition, y: panPosition.y + 20 });
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    onPanChange?.({ ...panPosition, y: panPosition.y - 20 });
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    onPanChange?.({ ...panPosition, x: panPosition.x + 20 });
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    onPanChange?.({ ...panPosition, x: panPosition.x - 20 });
                    break;
                case '+':
                case '=':
                    e.preventDefault();
                    onZoomChange?.(Math.min(zoom * 1.2, 5));
                    break;
                case '-':
                    e.preventDefault();
                    onZoomChange?.(Math.max(zoom / 1.2, 0.1));
                    break;
                case '0':
                    e.preventDefault();
                    onZoomChange?.(1);
                    onPanChange?.({ x: 0, y: 0 });
                    break;
                case 'f':
                case 'F':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
            }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [panPosition, zoom, onPanChange, onZoomChange, toggleFullscreen]);

    return (
        <main
            className={cn('content-body', className, isFullscreen && 'fullscreen-mode')}
            role="main"
            aria-label="Floor plan viewer">
            {/* Loading State */}
            {isLoading && (
                <div className="loading-overlay">
                    <div className="loading-spinner" />
                    <span className="loading-text">Loading floor plan...</span>
                </div>
            )}

            {/* Error State */}
            {error && (
                <div className="error-overlay">
                    <div className="error-content">
                        <span className="error-icon">⚠️</span>
                        <h3 className="error-title">Failed to load floor plan</h3>
                        <p className="error-message">{error}</p>
                        <button className="retry-button" onClick={() => window.location.reload()}>
                            Retry
                        </button>
                    </div>
                </div>
            )}

            {/* Main Content */}
            {!isLoading && !error && (
                <>
                    {/* Zoom Controls */}
                    <div className="zoom-controls">
                        <button
                            className="zoom-button zoom-in"
                            onClick={() => onZoomChange?.(Math.min(zoom * 1.2, 5))}
                            aria-label="Zoom in"
                            disabled={zoom >= 5}>
                            <SvgIcon name="add" size="sm" color="white" />
                        </button>
                        <span className="zoom-level">{Math.round(zoom * 100)}%</span>
                        <button
                            className="zoom-button zoom-out"
                            onClick={() => onZoomChange?.(Math.max(zoom / 1.2, 0.1))}
                            aria-label="Zoom out"
                            disabled={zoom <= 0.1}>
                            <SvgIcon name="minus" size="sm" color="white" />
                        </button>
                        <button
                            className="zoom-button zoom-reset"
                            onClick={() => {
                                onZoomChange?.(1);
                                onPanChange?.({ x: 0, y: 0 });
                            }}
                            aria-label="Reset zoom">
                            <SvgIcon name="maximize" size="sm" color="white" />
                        </button>
                        <button
                            className="zoom-button fullscreen-toggle"
                            onClick={toggleFullscreen}
                            aria-label={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}>
                            <SvgIcon name={isFullscreen ? 'minimize' : 'maximizeScreen'} size="sm" color="white" />
                        </button>
                    </div>

                    {/* Floor Plan Container */}
                    <div
                        ref={containerRef}
                        className={cn(
                            'plan-container',
                            isDragging && 'dragging',
                            isFullscreen && 'fullscreen-container',
                        )}
                        onMouseDown={handleMouseDown}
                        onMouseMove={handleMouseMove}
                        onMouseUp={handleMouseUp}
                        onMouseLeave={handleMouseUp}
                        onWheel={handleWheel}
                        onClick={handlePlanClick}
                        tabIndex={0}>
                        {/* Floor Plan Image */}
                        <div
                            className="plan-wrapper"
                            style={{
                                transform: `translate(${panPosition.x}px, ${panPosition.y}px) scale(${zoom})`,
                                transformOrigin: '0 0',
                            }}>
                            {floorPlanUrl ? (
                                <Image
                                    ref={imageRef}
                                    src={floorPlanUrl}
                                    alt={floorPlanAlt}
                                    className="floor-plan-image"
                                    onLoad={handleImageLoad}
                                    onError={handleImageError}
                                    draggable={false}
                                    width={800}
                                    height={600}
                                    unoptimized
                                />
                            ) : (
                                <div className="placeholder-plan">
                                    <div className="placeholder-content">
                                        <SvgIcon name="building" size="xl" color="#666" />
                                        <h3 className="placeholder-title">No Floor Plan Available</h3>
                                        <p className="placeholder-text">
                                            Select a building and floor to view the floor plan
                                        </p>
                                    </div>
                                </div>
                            )}

                            {/* Device Markers */}
                            {imageLoaded &&
                                devices.map((device) => (
                                    <div
                                        key={device.id}
                                        className={cn(
                                            'device-marker',
                                            `device-${device.type}`,
                                            `status-${device.status}`,
                                        )}
                                        style={{
                                            left: `${device.position.x}%`,
                                            top: `${device.position.y}%`,
                                        }}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            onDeviceClick?.(device.id);
                                        }}
                                        title={device.label || `${device.type} device`}
                                        role="button"
                                        tabIndex={0}
                                        aria-label={`${device.type} device: ${device.label || device.id}`}>
                                        <div className="marker-icon">
                                            <SvgIcon name={getDeviceIcon(device.type)} size="sm" color="white" />
                                        </div>
                                        {device.status !== 'normal' && <div className="marker-status" />}
                                    </div>
                                ))}

                            {/* Alert Markers */}
                            {imageLoaded &&
                                alerts.map((alert) => (
                                    <div
                                        key={alert.id}
                                        className={cn(
                                            'alert-marker',
                                            `severity-${alert.severity}`,
                                            alert.acknowledged && 'acknowledged',
                                        )}
                                        style={{
                                            left: `${alert.position.x}%`,
                                            top: `${alert.position.y}%`,
                                        }}
                                        onClick={(e) => {
                                            e.stopPropagation();
                                            onAlertClick?.(alert.id);
                                        }}
                                        title={alert.title}
                                        role="button"
                                        tabIndex={0}
                                        aria-label={`${alert.severity} alert: ${alert.title}`}>
                                        <div className="alert-icon">
                                            <SvgIcon name={getAlertIcon(alert.type)} size="sm" color="white" />
                                        </div>
                                        <div className="alert-pulse" />
                                    </div>
                                ))}
                        </div>
                    </div>

                    {/* Floor Plan Info */}
                    <div className="plan-info">
                        <div className="plan-details">
                            {selectedBuilding && (
                                <span className="building-info">
                                    {selectedBuilding}
                                    {selectedFloor && ` - ${selectedFloor}`}
                                </span>
                            )}
                        </div>
                        <div className="plan-stats">
                            <span className="device-count">{devices.length} devices</span>
                            {alerts.length > 0 && <span className="alert-count">{alerts.length} active alerts</span>}
                        </div>
                    </div>
                </>
            )}
        </main>
    );
}

FloorPlanViewerDemo.displayName = 'FloorPlanViewerDemo';
