'use client';

import { useSvgCleanPaths } from '@/hooks/useSvgPaths';
import { Group, Circle, Path } from 'react-konva';

interface SvgIconProps {
    x: number;
    y: number;
    fileName: string;
    radius?: number;
    circleFill?: string;
    circleStroke?: string;
    pathFill?: string;
    pathStroke?: string;
    onClick?: () => void;
}

export function Marker({
    x,
    y,
    fileName,
    radius = 30,
    circleFill = 'lightblue',
    circleStroke = 'black',
    pathFill = '',
    pathStroke = 'black',
    onClick,
}: SvgIconProps) {
    const paths = useSvgCleanPaths(fileName);

    // ✅ special case for "people"
    const isPeople = fileName === 'people' || fileName === 'person';
    const pathScaleX = isPeople ? 0.6 : 1;
    const pathScaleY = isPeople ? 0.6 : 1;
    const pathOffsetX = isPeople ? 18 : 12;
    const pathOffsetY = isPeople ? 12 : 12;

    return (
        <Group x={x} y={y} onClick={onClick}>
            {/* Circle always rendered behind */}
            <Circle
                radius={radius}
                fill={circleFill}
                stroke={circleStroke}
                opacity={0.9}
                name="device-marker"
                scaleX={0.7}
                scaleY={0.7}
            />

            {/* Paths rendered on top */}
            {paths.map((p, i) => (
                <Path
                    key={i}
                    data={p.d}
                    fill={pathFill}
                    stroke={pathStroke}
                    scaleX={pathScaleX}
                    scaleY={pathScaleY}
                    offsetX={pathOffsetX}
                    offsetY={pathOffsetY}
                />
            ))}
        </Group>
    );
}
