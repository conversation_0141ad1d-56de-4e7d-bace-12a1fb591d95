'use client';

import React, { useState } from 'react';
import { PlacedMarker } from './types';

interface PlacedIconComponentProps {
    icon: PlacedMarker;
    onRemove: () => void;
}

export function PlacedIconComponent({ icon, onRemove }: PlacedIconComponentProps) {
    const [showTooltip, setShowTooltip] = useState(false);

    const getIconEmoji = (type: string) => {
        switch (type) {
            case 'door':
                return '🚪';
            case 'camera':
                return '📹';
            case 'fire':
                return '🔥';
            case 'gate':
                return '🚧';
            default:
                return '📍';
        }
    };

    const getStateStyles = (state: string) => {
        switch (state) {
            case 'normal':
                return {
                    bg: 'bg-green-100',
                    border: 'border-green-500',
                    text: 'text-green-700',
                };
            case 'active':
                return {
                    bg: 'bg-blue-100',
                    border: 'border-blue-500',
                    text: 'text-blue-700',
                };
            case 'warning':
                return {
                    bg: 'bg-yellow-100',
                    border: 'border-yellow-500',
                    text: 'text-yellow-700',
                };
            case 'error':
                return {
                    bg: 'bg-red-100',
                    border: 'border-red-500',
                    text: 'text-red-700',
                };
            default:
                return {
                    bg: 'bg-gray-100',
                    border: 'border-gray-500',
                    text: 'text-gray-700',
                };
        }
    };

    const styles = getStateStyles(icon.status);

    return (
        <div
            className="absolute transform -translate-x-1/2 -translate-y-1/2 z-10"
            style={{
                left: `${icon.positionX}%`,
                top: `${icon.positionY}%`,
            }}
            onMouseEnter={() => setShowTooltip(true)}
            onMouseLeave={() => setShowTooltip(false)}>
            {/* Icon */}
            <div
                className={`
          relative w-8 h-8 rounded-full border-2 cursor-pointer
          ${styles.bg} ${styles.border} ${styles.text}
          hover:scale-110 transition-transform duration-200
          flex items-center justify-center
          shadow-lg hover:shadow-xl
        `}
                onClick={(e) => {
                    e.stopPropagation();
                    if (confirm(`Remove this ${icon.type} icon?`)) {
                        onRemove();
                    }
                }}
                title={`Click to remove ${icon.type} (${icon.status})`}>
                <span className="text-lg">{getIconEmoji(icon.type)}</span>
            </div>

            {/* Tooltip */}
            {showTooltip && (
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-20">
                    <div className="bg-gray-900 text-white text-xs rounded-lg px-3 py-2 whitespace-nowrap shadow-lg">
                        <div className="font-semibold">{icon.type.charAt(0).toUpperCase() + icon.type.slice(1)}</div>
                        <div className="text-gray-300">
                            State: {icon.status.charAt(0).toUpperCase() + icon.status.slice(1)}
                        </div>
                        <div className="text-gray-400 text-xs mt-1">
                            Position: {icon.positionX.toFixed(1)}%, {icon.positionY.toFixed(1)}%
                        </div>
                        <div className="text-gray-400 text-xs">Click to remove</div>

                        {/* Tooltip arrow */}
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2">
                            <div className="border-4 border-transparent border-t-gray-900"></div>
                        </div>
                    </div>
                </div>
            )}

            {/* Pulse animation for active state */}
            {icon.status === 'motion_detected' && (
                <div className="absolute inset-0 rounded-full border-2 border-blue-400 animate-ping opacity-75"></div>
            )}

            {/* Warning pulse for warning state */}
            {icon.status === 'maintenance_required' && (
                <div className="absolute inset-0 rounded-full border-2 border-yellow-400 animate-pulse opacity-75"></div>
            )}

            {/* Error pulse for error state */}
            {(icon.status === 'alarm_triggered' || icon.status === 'offline') && (
                <div className="absolute inset-0 rounded-full border-2 border-red-400 animate-bounce opacity-75"></div>
            )}
        </div>
    );
}
