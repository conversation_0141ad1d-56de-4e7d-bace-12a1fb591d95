'use client';

import React from 'react';
import { MarkerType, ColorState, IconConfig, ColorStateConfig } from './types';

const ICON_CONFIGS: IconConfig[] = [
    {
        type: 'door',
        label: 'Door',
        icon: '🚪',
        description: 'Entry/exit points',
    },
    {
        type: 'camera',
        label: 'Camera',
        icon: '📹',
        description: 'Security cameras',
    },
    {
        type: 'fire',
        label: 'Fire Alarm',
        icon: '🔥',
        description: 'Fire detection systems',
    },
    {
        type: 'gate',
        label: 'Gate',
        icon: '🚧',
        description: 'Access control gates',
    },
];

const COLOR_STATE_CONFIGS: ColorStateConfig[] = [
    {
        state: 'normal',
        label: 'Normal',
        color: 'text-green-600',
        bgColor: 'bg-green-100',
        description: 'Operating normally',
    },
    {
        state: 'active',
        label: 'Active',
        color: 'text-blue-600',
        bgColor: 'bg-blue-100',
        description: 'Currently active/in use',
    },
    {
        state: 'warning',
        label: 'Warning',
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-100',
        description: 'Requires attention',
    },
    {
        state: 'error',
        label: 'Error',
        color: 'text-red-600',
        bgColor: 'bg-red-100',
        description: 'Malfunction or offline',
    },
];

interface IconSelectorProps {
    selectedIconType: MarkerType;
    selectedColorState: ColorState;
    onIconTypeChange: (type: MarkerType) => void;
    onColorStateChange: (state: ColorState) => void;
}

export function IconSelector({
    selectedIconType,
    selectedColorState,
    onIconTypeChange,
    onColorStateChange,
}: IconSelectorProps) {
    return (
        <div className="space-y-6">
            {/* Icon Type Selection */}
            <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Icon Type</h3>
                <div className="grid grid-cols-2 gap-2">
                    {ICON_CONFIGS.map((config) => (
                        <button
                            key={config.type}
                            onClick={() => onIconTypeChange(config.type)}
                            className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                                selectedIconType === config.type
                                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                                    : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                            }`}
                            title={config.description}>
                            <div className="text-2xl mb-1">{config.icon}</div>
                            <div className="text-xs font-medium">{config.label}</div>
                        </button>
                    ))}
                </div>
            </div>

            {/* Color State Selection */}
            <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Color State</h3>
                <div className="space-y-2">
                    {COLOR_STATE_CONFIGS.map((config) => (
                        <button
                            key={config.state}
                            onClick={() => onColorStateChange(config.state)}
                            className={`w-full p-3 rounded-lg border-2 transition-all duration-200 text-left ${
                                selectedColorState === config.state
                                    ? `border-gray-400 ${config.bgColor} ${config.color}`
                                    : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                            }`}
                            title={config.description}>
                            <div className="flex items-center justify-between">
                                <div>
                                    <div className="font-medium">{config.label}</div>
                                    <div className="text-xs text-gray-500">{config.description}</div>
                                </div>
                                <div
                                    className={`w-3 h-3 rounded-full ${
                                        config.state === 'normal'
                                            ? 'bg-green-500'
                                            : config.state === 'active'
                                              ? 'bg-blue-500'
                                              : config.state === 'warning'
                                                ? 'bg-yellow-500'
                                                : 'bg-red-500'
                                    }`}
                                />
                            </div>
                        </button>
                    ))}
                </div>
            </div>

            {/* Current Selection Preview */}
            <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-semibold text-gray-700 mb-2">Current Selection</h4>
                <div className="flex items-center space-x-3">
                    <div className="text-2xl">{ICON_CONFIGS.find((c) => c.type === selectedIconType)?.icon}</div>
                    <div>
                        <div className="font-medium text-gray-900">
                            {ICON_CONFIGS.find((c) => c.type === selectedIconType)?.label}
                        </div>
                        <div
                            className={`text-sm ${
                                COLOR_STATE_CONFIGS.find((c) => c.state === selectedColorState)?.color
                            }`}>
                            {COLOR_STATE_CONFIGS.find((c) => c.state === selectedColorState)?.label}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
