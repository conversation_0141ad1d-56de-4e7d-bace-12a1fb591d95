'use client';

import React, { useState, useRef } from 'react';
import Image from 'next/image';
import { MarkerSelector } from './MarkerSelector';
import { PlacedMarkerComponent } from './PlacedMarkerComponent';
import { CoordinateDisplay } from './CoordinateDisplay';
import { MarkerType, PlacedMarker } from './types';
import { getBuildingById } from '@/infrastructure/api/geography/buildings/mock-buildings';
import { getZoneById } from '@/infrastructure/api/geography/zones/mock-zones';
import { getFloorById } from '@/infrastructure/api/geography/floors/mock-floors';

export function FloorPlanEditor() {
    const [selectedMarkerType, setSelectedMarkerType] = useState<MarkerType | null>(null);
    const [placedMarkers, setPlacedMarkers] = useState<PlacedMarker[]>([]);
    const [currentCoordinates, setCurrentCoordinates] = useState<{ x: number; y: number } | null>(null);
    const [buildingId, setBuildingId] = useState<number>(1);
    const [floorId, setFloorId] = useState<number>(1);
    const [zoneId, setZoneId] = useState<number>(1);
    const imageRef = useRef<HTMLImageElement>(null);

    const handleImageClick = (event: React.MouseEvent<HTMLImageElement>) => {
        if (!selectedMarkerType || !imageRef.current) return;

        const rect = imageRef.current.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        // Calculate percentage coordinates
        const percentageX = (x / rect.width) * 100;
        const percentageY = (y / rect.height) * 100;

        const newMarker: PlacedMarker = {
            id: Date.now().toString(),
            name: `${selectedMarkerType.charAt(0).toUpperCase() + selectedMarkerType.slice(1)} ${placedMarkers.length + 1}`,
            type: selectedMarkerType,
            positionX: percentageX,
            positionY: percentageY,
            exact_x: x,
            exact_y: y,
            status: 'normal',
            isAlert: false,
            floorId: floorId.toString(),
            zoneId: `zone-${zoneId}`,
            buildingId: buildingId.toString(),
            description: `${selectedMarkerType} marker placed at ${x.toFixed(0)}px, ${y.toFixed(0)}px`,
            lastUpdated: new Date().toISOString(),
            metadata: {
                placedBy: 'user',
                placedAt: new Date().toISOString(),
            },
        };

        setPlacedMarkers((prev) => [...prev, newMarker]);
    };

    const handleImageMouseMove = (event: React.MouseEvent<HTMLImageElement>) => {
        if (!imageRef.current) return;

        const rect = imageRef.current.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;

        setCurrentCoordinates({ x, y });
    };

    const handleImageMouseLeave = () => {
        setCurrentCoordinates(null);
    };

    const removeMarker = (markerId: string | number) => {
        setPlacedMarkers((prev) => prev.filter((marker) => marker.id !== markerId));
    };

    const clearAllMarkers = () => {
        if (confirm('Are you sure you want to remove all markers?')) {
            setPlacedMarkers([]);
        }
    };

    const exportCoordinates = () => {
        // Get smart data from mock data
        const building = getBuildingById(buildingId);
        const zone = getZoneById(zoneId);
        const floor = getFloorById(floorId);

        const exportData = {
            markers: placedMarkers.map((marker, index) => {
                // Generate smart marker name based on type and index
                const markerTypePrefix =
                    marker.type === 'fire'
                        ? 'fire'
                        : marker.type === 'door'
                          ? 'door'
                          : marker.type === 'camera'
                            ? 'camera'
                            : marker.type === 'gate'
                              ? 'gate'
                              : marker.type === 'people'
                                ? 'people'
                                : marker.type;

                const markerNumber = String(index + 1).padStart(3, '0');
                const smartName = `${markerTypePrefix}-${markerNumber}`;

                // Generate smart title based on type
                const getSmartTitle = (type: string, name: string) => {
                    switch (type) {
                        case 'fire':
                            return `Fire Detector FD-${markerNumber}`;
                        case 'door':
                            return index === 0
                                ? 'Main Entrance'
                                : index === 1
                                  ? 'Executive Office Door'
                                  : index === 2
                                    ? 'Conference Room Door'
                                    : `Access Door ${name}`;
                        case 'camera':
                            return index === 0
                                ? 'Lobby Security Camera'
                                : index === 1
                                  ? 'Executive Area Camera'
                                  : index === 2
                                    ? 'Conference Room Camera'
                                    : `Security Camera ${name}`;
                        case 'gate':
                            return index === 0
                                ? 'Parking Gate PG-001'
                                : index === 1
                                  ? 'Security Turnstile GT-001'
                                  : `Security Gate ${name}`;
                        case 'people':
                            return index === 0
                                ? 'Reception Area People'
                                : index === 1
                                  ? 'Executive Area People'
                                  : index === 2
                                    ? 'Conference Room People'
                                    : `People Marker ${name}`;
                        default:
                            return `${type.charAt(0).toUpperCase() + type.slice(1)} ${name}`;
                    }
                };

                // Generate smart subtitle based on status
                const getSmartSubtitle = (type: string, status: string) => {
                    switch (status) {
                        case 'normal':
                            return 'Status: Normal';
                        case 'open':
                            return 'Access Granted';
                        case 'closed':
                            return 'Secure Access';
                        case 'locked':
                            return 'Access Denied';
                        case 'recording':
                            return 'HD Recording Active';
                        case 'offline':
                            return 'Connection Lost';
                        case 'motion_detected':
                            return 'Motion Alert';
                        case 'smoke_detected':
                            return 'Smoke Level: Critical';
                        default:
                            return `Status: ${status.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase())}`;
                    }
                };

                // Generate smart description
                const getSmartDescription = (type: string, status: string) => {
                    switch (type) {
                        case 'fire':
                            return status === 'smoke_detected'
                                ? 'Photoelectric smoke detector in north wing corridor'
                                : status === 'normal'
                                  ? 'Heat detector in north wing conference room'
                                  : 'Combination smoke and heat detector in executive area';
                        case 'door':
                            return index === 0
                                ? 'Main entrance door with card reader access'
                                : index === 1
                                  ? 'Executive office door with keycard access'
                                  : index === 2
                                    ? 'Conference room door with biometric scanner'
                                    : 'Automatic sliding door with proximity sensor in main lobby';
                        case 'camera':
                            return index === 0
                                ? 'Fixed security camera monitoring main lobby area'
                                : index === 1
                                  ? 'PTZ camera covering executive office area'
                                  : 'Conference room surveillance camera with motion detection';
                        case 'gate':
                            return index === 0
                                ? 'Automated parking barrier gate'
                                : 'Electronic turnstile gate for lobby access control';
                        case 'people':
                            return index === 0
                                ? 'People marker tracking occupancy in reception area'
                                : index === 1
                                  ? 'People marker monitoring executive area occupancy'
                                  : index === 2
                                    ? 'People marker tracking conference room attendance'
                                    : 'People marker for occupancy tracking and space utilization';
                        default:
                            return `${type} marker placed at ${Math.round(marker.exact_x)}px, ${Math.round(marker.exact_y)}px`;
                    }
                };

                // Generate smart zone description
                const getSmartZone = () => {
                    if (building && zone && floor) {
                        return `Floor ${floor.level} - ${building.shortCode} - ${zone.name}`;
                    }
                    return `Floor ${floorId} - Building ${buildingId} - Zone ${zoneId}`;
                };

                // Generate smart public address
                const getSmartPublicAddress = () => {
                    if (building && zone) {
                        const locationDetail =
                            index === 0
                                ? 'Main Lobby'
                                : index === 1
                                  ? 'Executive Office 101'
                                  : index === 2
                                    ? 'Conference Room Alpha'
                                    : 'Parking Entrance';
                        return `${building.name}, Ground Floor, ${zone.name}, ${locationDetail}`;
                    }
                    return `Building ${buildingId}, Floor ${floorId}, Zone ${zoneId}`;
                };

                return {
                    id: parseInt(marker.id) || index + 1,
                    name: smartName,
                    type: marker.type,
                    positionX: Math.round(marker.exact_x),
                    positionY: Math.round(marker.exact_y),
                    positionXPercent: Math.round(marker.positionX * 100) / 100,
                    positionYPercent: Math.round(marker.positionY * 100) / 100,
                    status: marker.status,
                    isAlert: marker.isAlert,
                    floorId: parseInt(marker.floorId),
                    zoneId: parseInt(marker.zoneId.replace('zone-', '')),
                    buildingId: parseInt(marker.buildingId),
                    title: getSmartTitle(marker.type, marker.name),
                    subtitle: getSmartSubtitle(marker.type, marker.status),
                    description: getSmartDescription(marker.type, marker.status),
                    zone: getSmartZone(),
                    publicAddress: getSmartPublicAddress(),
                    alertTimestamp: new Date().toISOString(),
                };
            }),
        };

        const jsonString = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `b${buildingId}-f${floorId}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    };

    return (
        <div className="flex h-screen">
            {/* Left Panel - Compact Icon Toolbar */}
            <div className="w-20 bg-white border-r border-gray-200 p-2 flex flex-col items-center">
                <div className="mb-4">
                    <h3 className="text-xs font-semibold text-gray-600 mb-2 text-center">Tools</h3>
                    <MarkerSelector
                        selectedMarkerType={selectedMarkerType}
                        onMarkerTypeChange={setSelectedMarkerType}
                    />
                </div>

                {/* Compact Input Fields */}
                <div className="space-y-3">
                    <div className="flex flex-col items-center">
                        <label className="text-xs font-medium text-gray-700 mb-1">B</label>
                        <input
                            type="number"
                            min="1"
                            value={buildingId}
                            onChange={(e) => setBuildingId(parseInt(e.target.value) || 1)}
                            className="w-12 h-8 px-1 text-xs border border-gray-300 rounded text-center text-gray-900 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                    </div>
                    <div className="flex flex-col items-center">
                        <label className="text-xs font-medium text-gray-700 mb-1">F</label>
                        <input
                            type="number"
                            min="1"
                            value={floorId}
                            onChange={(e) => setFloorId(parseInt(e.target.value) || 1)}
                            className="w-12 h-8 px-1 text-xs border border-gray-300 rounded text-center text-gray-900 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                    </div>
                    <div className="flex flex-col items-center">
                        <label className="text-xs font-medium text-gray-700 mb-1">Z</label>
                        <input
                            type="number"
                            min="1"
                            value={zoneId}
                            onChange={(e) => setZoneId(parseInt(e.target.value) || 1)}
                            className="w-12 h-8 px-1 text-xs border border-gray-300 rounded text-center text-gray-900 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        />
                    </div>
                </div>
            </div>

            {/* Right Panel - Floor Plan Editor */}
            <div className="flex-1 flex flex-col">
                <div className="flex-1 p-4">
                    <div className="h-full flex flex-col">
                        <div className="mb-4">
                            <div className="flex justify-between items-center mb-2">
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-800">Floor Plan Editor</h3>
                                    <p className="text-sm text-gray-600">Click on the floor plan to place markers</p>
                                </div>
                                <div className="flex items-center gap-4">
                                    <div className="text-sm text-gray-600">
                                        Total Devices:{' '}
                                        <span className="font-semibold text-gray-800">{placedMarkers.length}</span>
                                    </div>
                                    <button
                                        onClick={() => setPlacedMarkers([])}
                                        className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600 transition-colors">
                                        Clear All
                                    </button>
                                    <button
                                        onClick={exportCoordinates}
                                        className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                                        Export JSON
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div className="flex-1 relative border-2 border-dashed border-gray-300 rounded-lg overflow-hidden bg-white">
                            <Image
                                ref={imageRef}
                                src="/plans/floorPlan-b1-f1.png"
                                alt="Floor Plan"
                                width={800}
                                height={600}
                                className="w-full h-full object-contain cursor-crosshair"
                                onClick={handleImageClick}
                                onMouseMove={handleImageMouseMove}
                                onMouseLeave={handleImageMouseLeave}
                                priority
                            />

                            {/* Render placed markers */}
                            {placedMarkers.map((marker) => (
                                <PlacedMarkerComponent
                                    key={marker.id}
                                    marker={marker}
                                    onRemove={() => removeMarker(marker.id)}
                                />
                            ))}
                        </div>

                        {/* Bottom Section - Coordinates and Export */}
                        <div className="mt-4 flex justify-between items-center">
                            <CoordinateDisplay
                                currentCoordinates={currentCoordinates}
                                placedMarkers={placedMarkers}
                                onRemoveMarker={removeMarker}
                                onExport={exportCoordinates}
                                onClear={clearAllMarkers}
                            />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
