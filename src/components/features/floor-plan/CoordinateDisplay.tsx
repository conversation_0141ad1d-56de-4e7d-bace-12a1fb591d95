'use client';

import React from 'react';
import { PlacedMarker } from './types';
import { SvgIcon } from '@/components/common/ui/SvgIcon';

interface CoordinateDisplayProps {
  currentCoordinates: { x: number; y: number } | null;
  placedMarkers: PlacedMarker[];
  onRemoveMarker: (markerId: string | number) => void;
  onExport: () => void;
  onClear: () => void;
}

export function CoordinateDisplay({
  currentCoordinates,
  placedMarkers,
  onRemoveMarker,
  onExport,
  onClear
}: CoordinateDisplayProps) {
  const getMarkerSvgIcon = (type: string) => {
    switch (type) {
      case 'door': return 'door';
      case 'camera': return 'camera';
      case 'fire': return 'fire';
      case 'gate': return 'gateBarrier';
      default: return 'door';
    }
  };

  const getStatusColor = (status: string, isAlert: boolean) => {
    if (isAlert) return 'text-red-600';
    
    switch (status) {
      case 'normal': return 'text-green-600';
      case 'open': return 'text-blue-600';
      case 'closed': return 'text-gray-600';
      case 'recording': return 'text-green-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="space-y-4">
      {/* Current Mouse Coordinates */}
      <div className="p-4 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-semibold text-gray-700 mb-2">Mouse Position</h4>
        {currentCoordinates ? (
          <div className="text-sm text-gray-600">
            <div>X: {Math.round(currentCoordinates.x)}px</div>
            <div>Y: {Math.round(currentCoordinates.y)}px</div>
          </div>
        ) : (
          <div className="text-sm text-gray-400">Move mouse over floor plan</div>
        )}
      </div>

      {/* Placed Markers List */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-sm font-semibold text-gray-700">
            Placed Markers ({placedMarkers.length})
          </h4>
          <div className="flex space-x-2">
            {placedMarkers.length > 0 && (
              <>
                <button
                  onClick={onClear}
                  className="px-3 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
                >
                  Clear All
                </button>
                <button
                  onClick={onExport}
                  className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                >
                  Export JSON
                </button>
              </>
            )}
          </div>
        </div>
        
        <div className="max-h-64 overflow-y-auto space-y-2">
          {placedMarkers.length === 0 ? (
            <div className="text-sm text-gray-400 text-center py-4">
              No markers placed yet
            </div>
          ) : (
            placedMarkers.map((marker, index) => (
              <div
                key={marker.id}
                className="p-3 bg-white border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <SvgIcon 
                      name={getMarkerSvgIcon(marker.type)} 
                      size="sm" 
                      strokeColor="#374151"
                    />
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {marker.name}
                      </div>
                      <div className={`text-xs ${getStatusColor(marker.status, marker.isAlert)}`}>
                        {marker.status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        {marker.isAlert && (
                          <span className="ml-1 bg-red-100 text-red-600 px-1 rounded text-xs">
                            Alert
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => onRemoveMarker(marker.id)}
                    className="text-red-500 hover:text-red-700 text-sm font-medium"
                    title="Remove marker"
                  >
                    ✕
                  </button>
                </div>
                
                <div className="mt-2 text-xs text-gray-500">
                  <div>Position: {Math.round(marker.exact_x)}px, {Math.round(marker.exact_y)}px</div>
                  <div>Building: {marker.buildingId} | Floor: {marker.floorId} | Zone: {marker.zoneId || 'N/A'}</div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}