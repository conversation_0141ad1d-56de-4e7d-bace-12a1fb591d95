'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';

interface SmartSpinnerProps {
  size?: number;
  className?: string;
  speed?: number;
}

/**
 * SmartSpinner Component
 * A loading spinner with smooth zoom in/out animation using the app logo
 */
export default function SmartSpinner({ 
  size = 48, 
  className = '', 
  speed = 1.5 
}: SmartSpinnerProps) {
  return (
    <div className={`flex items-center justify-center ${className}`}>
      <motion.div
        animate={{
          scale: [1, 1.2, 1, 0.8, 1],
        }}
        transition={{
          duration: speed,
          repeat: Infinity,
          ease: "easeInOut",
        }}
        style={{
          width: size,
          height: size,
        }}
      >
        <Image
          src="/svg/appLogo.svg"
          alt="Loading..."
          width={size}
          height={size}
          className="w-full h-full"
          priority
        />
      </motion.div>
    </div>
  );
}

/**
 * SmartSpinnerOverlay Component
 * Full-screen overlay with centered spinner
 */
export function SmartSpinnerOverlay({ 
  size = 64, 
  speed = 1.5,
  backdrop = true 
}: SmartSpinnerProps & { backdrop?: boolean }) {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={`fixed inset-0 z-50 flex items-center justify-center ${
        backdrop ? 'bg-black/20 backdrop-blur-sm' : ''
      }`}
    >
      <motion.div
        initial={{ scale: 0.5, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.5, opacity: 0 }}
        transition={{ duration: 0.3 }}
        className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-2xl"
      >
        <SmartSpinner size={size} speed={speed} />
        <motion.p
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="text-center text-gray-600 dark:text-gray-400 mt-4 text-sm font-medium"
        >
          Loading...
        </motion.p>
      </motion.div>
    </motion.div>
  );
}

/**
 * SmartSpinnerInline Component
 * Inline spinner for buttons and small spaces
 */
export function SmartSpinnerInline({ 
  size = 20, 
  speed = 1.2,
  className = '' 
}: SmartSpinnerProps) {
  return (
    <motion.div
      animate={{
        scale: [1, 1.1, 1, 0.9, 1],
        rotate: [0, 180, 360],
      }}
      transition={{
        duration: speed,
        repeat: Infinity,
        ease: "easeInOut",
      }}
      className={`inline-block ${className}`}
      style={{
        width: size,
        height: size,
      }}
    >
      <Image
        src="/svg/appLogo.svg"
        alt="Loading..."
        width={size}
        height={size}
        className="w-full h-full"
        priority
      />
    </motion.div>
  );
}