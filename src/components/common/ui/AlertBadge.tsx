import React from 'react';
import { cn } from '@/shared/utils/classNameUtils';

export interface AlertBadgeProps {
    value: number | string;
    isAlert?: boolean;
    size?: 'sm' | 'md' | 'lg';
    animate?: boolean;
    className?: string;
}

export default function AlertBadge({
    value,
    isAlert = false,
    size = 'md',
    animate = true,
    className,
}: AlertBadgeProps) {
    const sizeClasses = {
        sm: 'text-sm',
        md: 'text-base',
        lg: 'text-lg',
    };

    return (
        <span
            className={cn(
                'block w-full font-semibold tabular-nums',
                'transition-all duration-200',
                sizeClasses[size],
                isAlert ? `text-red-500 ${animate ? 'animate-pulse' : ''}` : 'text-white',
                className,
            )}
            aria-label={isAlert ? 'Alert value' : 'Normal value'}>
            {value}
        </span>
    );
}
