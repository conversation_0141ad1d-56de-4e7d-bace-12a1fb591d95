import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@/shared/utils';

const svgIconVariants = cva('inline-flex shrink-0', {
    variants: {
        size: {
            sm: 'w-4 h-4',
            default: 'w-6 h-6',
            lg: 'w-8 h-8',
            xl: 'w-10 h-10',
        },
    },
    defaultVariants: {
        size: 'default',
    },
});

export interface SvgIconProps extends React.HTMLAttributes<HTMLDivElement>, VariantProps<typeof svgIconVariants> {
    name: string;
    color?: string;
    strokeColor?: string;
    fillColor?: string;
    strokeWidth?: string | number;
}

const SvgIcon = React.forwardRef<HTMLDivElement, SvgIconProps>(
    ({ className, size, name, color, strokeColor, fillColor, strokeWidth = '1.5', ...props }, ref) => {
        const [svgContent, setSvgContent] = React.useState<string>('');
        const [loading, setLoading] = React.useState(true);
        const [error, setError] = React.useState<string | null>(null);

        React.useEffect(() => {
            const loadSvg = async () => {
                try {
                    setLoading(true);
                    setError(null);

                    const response = await fetch(`/svg/${name}.svg`);
                    if (!response.ok) {
                        throw new Error(`Failed to load SVG: ${name}`);
                    }

                    let svgText = await response.text();

                    // Apply color modifications
                    if (color || strokeColor || fillColor) {
                        // Replace stroke colors
                        const strokeColorToUse = strokeColor || color;
                        if (strokeColorToUse) {
                            svgText = svgText.replace(/stroke="[^"]*"/g, `stroke="${strokeColorToUse}"`);
                        }

                        // Replace fill colors (if any)
                        const fillColorToUse = fillColor || color;
                        if (fillColorToUse) {
                            svgText = svgText.replace(/fill="(?!none)[^"]*"/g, `fill="${fillColorToUse}"`);
                        }

                        // Update stroke-width if provided
                        if (strokeWidth) {
                            svgText = svgText.replace(/stroke-width="[^"]*"/g, `stroke-width="${strokeWidth}"`);
                        }
                    }

                    setSvgContent(svgText);
                } catch (err) {
                    setError(err instanceof Error ? err.message : 'Unknown error');
                } finally {
                    setLoading(false);
                }
            };

            loadSvg();
        }, [name, color, strokeColor, fillColor, strokeWidth]);

        if (loading) {
            return (
                <div
                    className={cn(svgIconVariants({ size, className }), 'animate-pulse bg-muted rounded')}
                    {...props}
                />
            );
        }

        if (error) {
            return (
                <div
                    className={cn(
                        svgIconVariants({ size, className }),
                        'bg-destructive/10 rounded flex items-center justify-center',
                    )}
                    title={error}
                    {...props}>
                    <span className="text-xs text-destructive">!</span>
                </div>
            );
        }

        return (
            <div
                className={cn(svgIconVariants({ size, className }))}
                dangerouslySetInnerHTML={{ __html: svgContent }}
                ref={ref}
                {...props}
            />
        );
    },
);

SvgIcon.displayName = 'SvgIcon';

export { SvgIcon, svgIconVariants };
