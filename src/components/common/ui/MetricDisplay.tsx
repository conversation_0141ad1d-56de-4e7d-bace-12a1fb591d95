import React from 'react';
import { cn } from '@/shared/utils/classNameUtils';
import AlertBadge from './AlertBadge';

export interface MetricDisplayProps {
    label: string;
    value: number | string;
    layout?: 'horizontal' | 'vertical';
    highlight?: boolean;
    isAlert?: boolean;
    animate?: boolean;
    size?: 'sm' | 'md' | 'lg';
    className?: string;
    labelClassName?: string;
    valueClassName?: string;
    textAlign?: 'start' | 'center' | 'end'; // new prop
}

export default function MetricDisplay({
    label,
    value,
    layout = 'horizontal',
    highlight = false,
    isAlert = false,
    animate = true,
    size = 'md',
    className,
    labelClassName,
    valueClassName,
    textAlign = 'center',
}: MetricDisplayProps) {
    const sizeClasses = {
        sm: 'text-sm',
        md: 'text-base',
        lg: 'text-lg',
    };

    // Determine alignment class
    let alignClass = '';
    if (textAlign === 'start') alignClass = 'text-left';
    else if (textAlign === 'end') alignClass = 'text-right';
    else alignClass = 'text-center';

    // For vertical layout, make container block and w-full so label and value align together
    const containerClasses = cn(
        layout === 'horizontal' ? 'flex w-full justify-between items-center' : 'block w-full',
        alignClass,
        className,
    );

    // Apply alignClass to both label and value
    const labelClasses = cn('text-gray-400 font-normal leading-tight', sizeClasses[size], labelClassName);
    const valueClasses = cn('font-semibold tabular-nums', highlight && !isAlert && 'text-yellow-400', valueClassName);

    return (
        <div className={containerClasses}>
            <span className={labelClasses}>{label}</span>
            <AlertBadge value={value} isAlert={isAlert} animate={animate} size={size} className={valueClasses} />
        </div>
    );
}
