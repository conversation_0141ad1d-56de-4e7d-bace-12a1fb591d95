import type { Meta, StoryObj } from '@storybook/react';
import { SvgIcon } from './SvgIcon';

const meta: Meta<typeof SvgIcon> = {
    title: 'UI/SvgIcon',
    component: SvgIcon,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
    argTypes: {
        name: {
            control: 'select',
            options: [
                'add',
                'bell',
                'camera',
                'chat',
                'door',
                'fire',
                'minus',
                'building',
                'calender',
                'gateBarrier',
                'maximize',
                'maximizeScreen',
                'openDoor',
                'appLogo',
                'bellActive',
            ],
            description: 'The name of the SVG file (without .svg extension)',
        },
        size: {
            control: 'select',
            options: ['sm', 'default', 'lg', 'xl'],
            description: 'Size variant of the icon',
        },
        strokeColor: {
            control: 'color',
            description: 'Custom stroke color for the SVG',
        },
        fillColor: {
            control: 'color',
            description: 'Custom fill color for the SVG',
        },
        strokeWidth: {
            control: { type: 'range', min: 0.5, max: 4, step: 0.5 },
            description: 'Custom stroke width',
        },
        className: {
            control: 'text',
            description: 'Additional CSS classes',
        },
    },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Default story
export const Default: Story = {
    args: {
        name: 'add',
        size: 'default',
    },
};

// Different sizes
export const Sizes: Story = {
    render: () => (
        <div className="flex items-center gap-4">
            <div className="text-center">
                <SvgIcon name="camera" size="sm" />
                <p className="text-xs mt-1">Small</p>
            </div>
            <div className="text-center">
                <SvgIcon name="camera" size="default" />
                <p className="text-xs mt-1">Medium</p>
            </div>
            <div className="text-center">
                <SvgIcon name="camera" size="lg" />
                <p className="text-xs mt-1">Large</p>
            </div>
            <div className="text-center">
                <SvgIcon name="camera" size="xl" />
                <p className="text-xs mt-1">Extra Large</p>
            </div>
        </div>
    ),
};

// Custom colors
export const CustomColors: Story = {
    render: () => (
        <div className="flex items-center gap-4">
            <div className="text-center">
                <SvgIcon name="bell" strokeColor="#ef4444" />
                <p className="text-xs mt-1">Red</p>
            </div>
            <div className="text-center">
                <SvgIcon name="bell" strokeColor="#3b82f6" />
                <p className="text-xs mt-1">Blue</p>
            </div>
            <div className="text-center">
                <SvgIcon name="bell" strokeColor="#10b981" />
                <p className="text-xs mt-1">Green</p>
            </div>
            <div className="text-center">
                <SvgIcon name="bell" strokeColor="#f59e0b" />
                <p className="text-xs mt-1">Yellow</p>
            </div>
        </div>
    ),
};

// Different stroke widths
export const StrokeWidths: Story = {
    render: () => (
        <div className="flex items-center gap-4">
            <div className="text-center">
                <SvgIcon name="add" strokeWidth={0.5} strokeColor="#6b7280" />
                <p className="text-xs mt-1">0.5px</p>
            </div>
            <div className="text-center">
                <SvgIcon name="add" strokeWidth={1} strokeColor="#6b7280" />
                <p className="text-xs mt-1">1px</p>
            </div>
            <div className="text-center">
                <SvgIcon name="add" strokeWidth={2} strokeColor="#6b7280" />
                <p className="text-xs mt-1">2px</p>
            </div>
            <div className="text-center">
                <SvgIcon name="add" strokeWidth={3} strokeColor="#6b7280" />
                <p className="text-xs mt-1">3px</p>
            </div>
        </div>
    ),
};

// Icon gallery
export const IconGallery: Story = {
    render: () => (
        <div className="grid grid-cols-5 gap-4">
            {[
                'add',
                'bell',
                'camera',
                'chat',
                'door',
                'fire',
                'minus',
                'building',
                'calender',
                'gateBarrier',
                'maximize',
                'maximizeScreen',
                'openDoor',
                'appLogo',
                'bellActive',
            ].map((iconName) => (
                <div key={iconName} className="text-center p-2 border rounded">
                    <SvgIcon name={iconName} size="lg" strokeColor="#374151" />
                    <p className="text-xs mt-2 text-gray-600">{iconName}</p>
                </div>
            ))}
        </div>
    ),
};

// Interactive example
export const Interactive: Story = {
    args: {
        name: 'camera',
        size: 'lg',
        strokeColor: '#3b82f6',
        strokeWidth: 1.5,
    },
};

// Error handling
export const ErrorHandling: Story = {
    args: {
        name: 'nonexistent-icon',
        size: 'default',
    },
};

// With custom styling
export const WithCustomStyling: Story = {
    render: () => (
        <div className="flex items-center gap-4">
            <SvgIcon
                name="bell"
                size="lg"
                strokeColor="#ef4444"
                className="hover:scale-110 transition-transform cursor-pointer"
            />
            <SvgIcon
                name="chat"
                size="lg"
                strokeColor="#3b82f6"
                className="opacity-50 hover:opacity-100 transition-opacity"
            />
            <SvgIcon name="fire" size="lg" strokeColor="#f59e0b" className="animate-pulse" />
        </div>
    ),
};
