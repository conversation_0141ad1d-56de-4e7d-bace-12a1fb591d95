'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useBuildingStore } from '@/stores/building.store';
import type { Building } from '@/infrastructure/api/geography/buildings/types';
import { cn } from '@/shared/utils';
import { SvgIcon } from './ui';
import { ChevronDown } from 'lucide-react';

interface BuildingDropdownProps {
    onSelect?: (building: Building) => void;
    placeholder?: string;
    className?: string;
    useStoreSelection?: boolean; // optional, default to true
}

export const BuildingDropdown: React.FC<BuildingDropdownProps> = ({
    onSelect,
    placeholder = 'Select Building',
    className,
    useStoreSelection = true,
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);

    const { buildings, selectedBuilding, isLoading, setSelectedBuilding } = useBuildingStore();

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleSelect = (building: Building) => {
        if (useStoreSelection) {
            setSelectedBuilding(building);
        }
        setIsOpen(false);
        onSelect?.(building);
    };

    const toggleDropdown = () => {
        setIsOpen(!isOpen);
    };

    return (
        <div className={cn('relative inline-block', className)} ref={dropdownRef}>
            {/* Main Dropdown Button */}
            <button
                onClick={toggleDropdown}
                className="flex items-center justify-between w-full min-w-[200px] h-[36px] px-2 py-1 
                          bg-[#212633CC] border border-[#1F2937] rounded-[4px] 
                          text-white text-sm font-medium
                          transition-all duration-200 ease-in-out
                          hover:scale-105 hover:shadow-md hover:bg-[#232b3a]
                          focus:outline-none focus:ring-2 focus:ring-[#5C9DD5] focus:ring-opacity-50"
                disabled={isLoading}>
                <div className="flex items-center gap-3">
                    <SvgIcon
                        name={'building'}
                        size="lg"
                        color={'white'}
                        className="flex items-center justify-center w-4 h-4"
                    />
                    <span className="truncate">
                        {isLoading
                            ? 'Loading...'
                            : selectedBuilding
                              ? `Building ${selectedBuilding.shortCode}`
                              : placeholder}
                    </span>
                </div>
                <div className={cn('transition-transform duration-200', isOpen && 'rotate-180')}>
                    <ChevronDown className="w-4 h-4 text-white" />
                </div>
            </button>

            {/* Dropdown Menu */}
            {isOpen && (
                <div
                    className="absolute top-full left-0 min-w-full mt-1 z-50
                               bg-[#212633CC] border border-[#1F2937] rounded-[4px]
                               shadow-lg max-h-60 overflow-y-auto">
                    {buildings.length === 0 ? (
                        <div className="px-2 py-4 text-gray-400 text-sm text-center">No buildings available</div>
                    ) : (
                        buildings.map((building) => (
                            <button
                                key={building.id}
                                onClick={() => handleSelect(building)}
                                className="flex items-center gap-3 w-11/12 h-[36px] mb-1 mx-auto px-2 py-4
                                          bg-[#21263380] border border-[#1F2937] rounded-[4px]
                                          text-white text-sm font-medium
                                          transition-all duration-200 ease-in-out
                                          hover:scale-105 hover:shadow-md hover:bg-[#232b3a] hover:bg-opacity-90 hover:border-[#5C9DD5]
                                          focus:outline-none focus:bg-[#5C9DD5] focus:bg-opacity-20">
                                <SvgIcon
                                    name={'building'}
                                    size="lg"
                                    color={'white'}
                                    className="flex items-center justify-center w-4 h-4"
                                />
                                <span className="truncate">Building {building.shortCode}</span>
                            </button>
                        ))
                    )}
                </div>
            )}
        </div>
    );
};

export default BuildingDropdown;
