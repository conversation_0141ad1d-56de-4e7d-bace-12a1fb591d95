'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useFloorStore } from '@/stores/floor.store';
import type { Floor } from '@/infrastructure/api/geography/floors/types';
import { cn } from '@/shared/utils';

interface FloorDropdownProps {
    onSelect?: (floor: Floor) => void;
    placeholder?: string;
    className?: string;
    useStoreSelection?: boolean; // optional, default to true
}

const FloorIcon = () => (
    <svg
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="text-white">
        <path
            d="M3 21H21M5 21V11H9V21M15 21V7H19V21"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            fill="currentColor"
            fillOpacity="0.8"
        />
    </svg>
);

const ChevronDownIcon = () => (
    <svg
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="text-white transition-transform duration-200">
        <path d="M6 9L12 15L18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
);

export const FloorDropdown: React.FC<FloorDropdownProps> = ({
    onSelect,
    placeholder = 'Select Floor',
    className,
    useStoreSelection = true,
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);

    const { floors, selectedFloorId, isLoading, setSelectedFloor, initialize, subscribeToBuildingStore } =
        useFloorStore();

    useEffect(() => {
        initialize(); // جلب جميع الطوابق الافتراضية
        subscribeToBuildingStore(); // ربط store بالـ building store
    }, [initialize, subscribeToBuildingStore]);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    const handleSelect = (floor: Floor) => {
        if (useStoreSelection) {
            setSelectedFloor(floor.id);
        }
        setIsOpen(false);
        onSelect?.(floor);
    };

    const toggleDropdown = () => {
        setIsOpen(!isOpen);
    };

    // Find the selected floor object
    const selectedFloor = floors.find((floor) => floor.id === selectedFloorId);

    return (
        <div className={cn('relative inline-block', className)} ref={dropdownRef}>
            {/* Main Dropdown Button */}
            <button
                onClick={toggleDropdown}
                className="flex items-center justify-between w-full min-w-[200px] h-[36px] px-2 py-1 
                          bg-[#212633CC] border border-[#1F2937] rounded-[4px] 
                          text-white text-sm font-medium
                          transition-all duration-200 ease-in-out
                          hover:scale-105 hover:shadow-md hover:bg-[#232b3a] 
                          focus:outline-none focus:ring-2 focus:ring-[#5C9DD5] focus:ring-opacity-50"
                disabled={isLoading}>
                <div className="flex items-center gap-3">
                    <FloorIcon />
                    <span className="truncate">
                        {isLoading ? 'Loading...' : selectedFloor ? selectedFloor.name : placeholder}
                    </span>
                </div>
                <div className={cn('transition-transform duration-200', isOpen && 'rotate-180')}>
                    <ChevronDownIcon />
                </div>
            </button>

            {/* Dropdown Menu */}
            {isOpen && (
                <div
                    className="absolute top-full left-0 min-w-full mt-1 z-50
                               bg-[#212633CC] border border-[#1F2937] rounded-[4px]
                               shadow-lg max-h-60 overflow-y-auto">
                    {floors.length === 0 ? (
                        <div className="px-2 py-4 text-gray-400 text-sm text-center">No floors available</div>
                    ) : (
                        floors.map((floor) => (
                            <button
                                key={floor.id}
                                onClick={() => handleSelect(floor)}
                                className="flex items-center gap-3 w-11/12 h-[36px] mb-1 mx-auto px-2 py-4
                                          bg-[#21263380] border border-[#1F2937] rounded-[4px]
                                          text-white text-sm font-medium
                                          transition-all duration-200 ease-in-out
                                          hover:scale-105 hover:shadow-md hover:bg-[#232b3a] hover:bg-opacity-90 hover:border-[#5C9DD5]
                                          focus:outline-none focus:bg-[#5C9DD5] focus:bg-opacity-20">
                                <FloorIcon />
                                <span className="truncate">{floor.name}</span>
                            </button>
                        ))
                    )}
                </div>
            )}
        </div>
    );
};

export default FloorDropdown;
