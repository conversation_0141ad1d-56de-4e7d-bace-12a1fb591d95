'use client';

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/shared/utils';
import { SvgIcon } from './ui';
import { ChevronDown } from 'lucide-react';

export interface TimeRange {
    id: string;
    label: string;
    value: string;
}

interface CalendarDropdownProps {
    onSelect?: (timeRange: TimeRange) => void;
    selectedTimeRange?: TimeRange | null;
    placeholder?: string;
    className?: string;
}

const timeRanges: TimeRange[] = [
    { id: '24h', label: 'Last 24 hours', value: 'last_24_hours' },
    { id: '1w', label: 'Last week', value: 'last_week' },
    { id: '1m', label: 'Last month', value: 'last_month' },
];

export const CalendarDropdown: React.FC<CalendarDropdownProps> = ({
    onSelect,
    selectedTimeRange,
    placeholder = 'Select Time Range',
    className,
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [selected, setSelected] = useState<TimeRange | null>(selectedTimeRange || null);
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    useEffect(() => {
        if (selectedTimeRange !== undefined) {
            setSelected(selectedTimeRange);
        }
    }, [selectedTimeRange]);

    const handleSelect = (timeRange: TimeRange) => {
        setSelected(timeRange);
        setIsOpen(false);
        onSelect?.(timeRange);
    };

    const toggleDropdown = () => {
        setIsOpen(!isOpen);
    };

    return (
        <div className={cn('relative inline-block', className)} ref={dropdownRef}>
            {/* Main Dropdown Button */}
            <button
                onClick={toggleDropdown}
                className="flex items-center justify-between w-full min-w-[200px] h-[36px] px-2 py-1 
                          bg-[#212633CC] border border-[#1F2937] rounded-[4px] 
                          text-white text-sm font-medium
                          transition-all duration-200 ease-in-out
                          hover:scale-105 hover:shadow-md hover:bg-[#232b3a]
                          focus:outline-none focus:ring-2 focus:ring-[#5C9DD5] focus:ring-opacity-50">
                <div className="flex items-center gap-3">
                    <SvgIcon
                        name={'calender'}
                        size="lg"
                        color={'white'}
                        className="flex items-center justify-center w-4 h-4"
                    />
                    <span className="truncate">{selected ? selected.label : placeholder}</span>
                </div>
                <div className={cn('transition-transform duration-200', isOpen && 'rotate-180')}>
                    <ChevronDown className="w-4 h-4 text-white" />
                </div>
            </button>

            {/* Dropdown Menu */}
            {isOpen && (
                <div
                    className="absolute top-full left-0 min-w-full mt-1 z-50
                               bg-[#212633CC] border border-[#1F2937] rounded-[4px]
                               shadow-lg max-h-60 overflow-y-auto">
                    {timeRanges.map((timeRange) => (
                        <button
                            key={timeRange.id}
                            onClick={() => handleSelect(timeRange)}
                            className="flex items-center gap-3 w-11/12 h-[36px] mb-1 mx-auto px-2 py-4
                                      bg-[#21263380] border border-[#1F2937] rounded-[4px]
                                      text-white text-sm font-medium
                                      transition-all duration-200 ease-in-out
                                      hover:scale-105 hover:shadow-md hover:bg-[#232b3a] hover:bg-opacity-90 hover:border-[#5C9DD5]
                                      focus:outline-none focus:bg-[#5C9DD5] focus:bg-opacity-20">
                            <SvgIcon
                                name={'calender'}
                                size="lg"
                                color={'white'}
                                className="flex items-center justify-center w-4 h-4"
                            />
                            <span className="truncate">{timeRange.label}</span>
                        </button>
                    ))}
                </div>
            )}
        </div>
    );
};

export default CalendarDropdown;
