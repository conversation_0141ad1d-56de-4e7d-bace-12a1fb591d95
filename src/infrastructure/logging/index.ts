// Export interfaces and types
export type { <PERSON>ogger, LogLevel, LogEntry } from './logger.interface';
export type { LoggerConfig, ExternalLogService, PerformanceMetrics, UserAction, SystemEvent } from './logger';

// Export main logger class
export { Logger } from './logger';

// Import for internal use
import type { LoggerConfig } from './logger';
import { Logger } from './logger';

// Default configuration
export const DEFAULT_LOGGER_CONFIG: LoggerConfig = {
    level: 'info',
    enableConsole: true,
    enableExternal: false,
    enablePerformance: true,
    enableUserActions: true,
    enableSystemEvents: true,
    environment: 'development',
    appName: 'NextJS App',
    appVersion: '1.0.0',
};

// Default logger instance
export const logger = Logger.getInstance(DEFAULT_LOGGER_CONFIG);
