// Logger types
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
    level: LogLevel;
    message: string;
    timestamp: number;
    data?: unknown;
    sessionId?: string;
    environment?: string;
    appName?: string;
    appVersion?: string;
}

// Main Logger interface
export interface ILogger {
    // Public logging methods
    debug(message: string, context?: Record<string, unknown>): void;
    info(message: string, context?: Record<string, unknown>): void;
    warn(message: string, context?: Record<string, unknown>): void;
    error(message: string, error?: Error, context?: Record<string, unknown>): void;

    // Utility methods
    setUserId(userId: string): void;
    clearLogs(): void;
    exportLogs(): LogEntry[];

    // Performance logging
    startTimer(label: string): () => void;

    // API request logging
    logApiRequest(method: string, url: string, data?: unknown): void;
    logApiResponse(method: string, url: string, status: number, duration: number): void;

    // User action logging
    logUserAction(action: {
        action: string;
        element?: string;
        page?: string;
        userId?: string;
        sessionId?: string;
        timestamp: number;
        metadata?: Record<string, unknown>;
    }): void;

    // System event logging
    logSystemEvent(event: {
        event: string;
        category: 'error' | 'warning' | 'info' | 'performance' | 'security';
        severity: 'low' | 'medium' | 'high' | 'critical';
        source: string;
        timestamp: number;
        metadata?: Record<string, unknown>;
    }): void;
}
