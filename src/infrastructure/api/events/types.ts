export type DeviceType = 'camera' | 'door' | 'gate' | 'fire';

export type EventStatus =
    // Camera statuses
    | 'Active Incidents'
    | 'recording'
    | 'offline'
    | 'motion_detected'
    // Door statuses
    | 'Opened'
    | 'Closed'
    | 'locked'
    | 'unlocked'
    // Gate statuses
    | 'Unauthorized attempts'
    | 'open'
    | 'closed'
    | 'maintenance_required'
    // Fire statuses
    | 'normal'
    | 'smoke_detected'
    | 'alarm_triggered'
    | 'heat_detected';

export interface Event {
    id: number;
    eventCode: string;
    deviceType: DeviceType;
    name: string;
    timestamp: string;
    status: EventStatus;
    isAlert: boolean;
    buildingId: number;
    zoneId: number;
    floorId: number;
    description: string;
    publicAddress: string;
}

export interface Events {
    events: Event[];
}

// Device-specific status configurations
export const deviceStatusConfig = {
    camera: {
        alert: 'Active Incidents',
        normal: 'recording',
    },
    door: {
        alert: 'Opened',
        normal: 'Closed',
    },
    gate: {
        alert: 'Unauthorized attempts',
        normal: 'closed',
    },
    fire: {
        alert: 'alarm_triggered',
        normal: 'normal',
    },
} as const;

// Event code generators
export const generateEventCode = (deviceType: DeviceType, id: number): string => {
    const prefix = deviceType.toUpperCase().substring(0, 3);
    return `${prefix}-${String(id).padStart(3, '0')}`;
};
