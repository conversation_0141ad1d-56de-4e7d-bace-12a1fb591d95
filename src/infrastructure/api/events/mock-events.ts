import { Events, generateEventCode } from './types';

export const mockEvents: Events = {
    events: [
        {
            id: 1,
            eventCode: generateEventCode('fire', 1),
            deviceType: 'fire',
            name: 'Fire Detector FD-001 Alert',
            timestamp: '2024-01-15T10:30:00Z',
            status: 'alarm_triggered',
            isAlert: true,
            buildingId: 1,
            zoneId: 1,
            floorId: 1,
            description: 'Photoelectric smoke detector in north wing corridor',
            publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Executive Office 101',
        },
        {
            id: 2,
            eventCode: generateEventCode('fire', 2),
            deviceType: 'fire',
            name: 'Fire Detector FD-002 Status',
            timestamp: '2024-01-15T08:15:00Z',
            status: 'normal',
            isAlert: false,
            buildingId: 1,
            zoneId: 1,
            floorId: 1,
            description: 'Heat detector in north wing conference room',
            publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Conference Room Alpha',
        },
        {
            id: 3,
            eventCode: generateEventCode('door', 1),
            deviceType: 'door',
            name: 'Main Entrance Access',
            timestamp: '2024-01-15T10:25:00Z',
            status: 'Opened',
            isAlert: true,
            buildingId: 1,
            zoneId: 1,
            floorId: 1,
            description: 'Main entrance door with card reader access',
            publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Main Lobby',
        },
        {
            id: 4,
            eventCode: generateEventCode('door', 3),
            deviceType: 'door',
            name: 'Executive Office Door',
            timestamp: '2024-01-15T09:15:00Z',
            status: 'Closed',
            isAlert: false,
            buildingId: 1,
            zoneId: 1,
            floorId: 1,
            description: 'Executive office door with keycard access',
            publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Executive Office 101',
        },
        {
            id: 5,
            eventCode: generateEventCode('door', 4),
            deviceType: 'door',
            name: 'Conference Room Access Denied',
            timestamp: '2024-01-15T11:05:00Z',
            status: 'locked',
            isAlert: true,
            buildingId: 1,
            zoneId: 1,
            floorId: 1,
            description: 'Conference room door with biometric scanner',
            publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Conference Room Alpha',
        },
        {
            id: 6,
            eventCode: generateEventCode('camera', 3),
            deviceType: 'camera',
            name: 'Lobby Security Camera',
            timestamp: '2024-01-15T10:10:00Z',
            status: 'recording',
            isAlert: false,
            buildingId: 1,
            zoneId: 1,
            floorId: 1,
            description: 'Fixed security camera monitoring main lobby area',
            publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Main Lobby',
        },
        {
            id: 7,
            eventCode: generateEventCode('camera', 4),
            deviceType: 'camera',
            name: 'Executive Area Camera Offline',
            timestamp: '2024-01-15T11:15:00Z',
            status: 'offline',
            isAlert: true,
            buildingId: 1,
            zoneId: 1,
            floorId: 1,
            description: 'PTZ camera covering executive office area',
            publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Executive Office 101',
        },
        {
            id: 8,
            eventCode: generateEventCode('camera', 5),
            deviceType: 'camera',
            name: 'Conference Room Motion Detection',
            timestamp: '2024-01-15T11:20:00Z',
            status: 'Active Incidents',
            isAlert: true,
            buildingId: 1,
            zoneId: 1,
            floorId: 1,
            description: 'Conference room surveillance camera with motion detection',
            publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Conference Room Alpha',
        },
        {
            id: 9,
            eventCode: generateEventCode('gate', 3),
            deviceType: 'gate',
            name: 'Parking Gate Access',
            timestamp: '2024-01-15T08:30:00Z',
            status: 'open',
            isAlert: false,
            buildingId: 1,
            zoneId: 1,
            floorId: 1,
            description: 'Automated parking barrier gate',
            publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Parking Entrance',
        },
        {
            id: 10,
            eventCode: generateEventCode('door', 5),
            deviceType: 'door',
            name: 'Lobby Access Door',
            timestamp: '2024-01-15T10:40:00Z',
            status: 'Opened',
            isAlert: true,
            buildingId: 1,
            zoneId: 1,
            floorId: 1,
            description: 'Automatic sliding door with proximity sensor in main lobby',
            publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Main Lobby',
        },
        {
            id: 11,
            eventCode: generateEventCode('fire', 3),
            deviceType: 'fire',
            name: 'Fire Detector FD-003 Status',
            timestamp: '2024-01-15T09:45:00Z',
            status: 'normal',
            isAlert: false,
            buildingId: 1,
            zoneId: 1,
            floorId: 1,
            description: 'Combination smoke and heat detector in executive area',
            publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Executive Office 101',
        },
        {
            id: 12,
            eventCode: generateEventCode('gate', 4),
            deviceType: 'gate',
            name: 'Security Turnstile',
            timestamp: '2024-01-15T08:45:00Z',
            status: 'closed',
            isAlert: false,
            buildingId: 1,
            zoneId: 1,
            floorId: 1,
            description: 'Electronic turnstile gate for lobby access control',
            publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Main Lobby',
        },
        {
            id: 13,
            eventCode: generateEventCode('door', 2),
            deviceType: 'door',
            name: 'Operations Center Breach Attempt',
            timestamp: '2024-01-15T10:45:00Z',
            status: 'locked',
            isAlert: true,
            buildingId: 1,
            zoneId: 2,
            floorId: 3,
            description: 'High-security door to operations center with biometric access',
            publicAddress: 'Main Corporate Building, Ground Floor, South Wing, Operations Center',
        },
        {
            id: 14,
            eventCode: generateEventCode('gate', 1),
            deviceType: 'gate',
            name: 'Security Gate Maintenance Alert',
            timestamp: '2024-01-15T09:45:00Z',
            status: 'Unauthorized attempts',
            isAlert: true,
            buildingId: 1,
            zoneId: 2,
            floorId: 3,
            description: 'Automated security barrier at south entrance',
            publicAddress: 'Main Corporate Building, Ground Floor, South Wing, Utility Room',
        },
        {
            id: 15,
            eventCode: generateEventCode('camera', 1),
            deviceType: 'camera',
            name: 'Security Camera CAM-001',
            timestamp: '2024-01-15T10:20:00Z',
            status: 'recording',
            isAlert: false,
            buildingId: 1,
            zoneId: 1,
            floorId: 2,
            description: 'PTZ security camera with night vision capability',
            publicAddress: 'Main Corporate Building, Second Floor, North Wing, Office 201',
        },
        {
            id: 16,
            eventCode: generateEventCode('camera', 2),
            deviceType: 'camera',
            name: 'Storage Area Motion Alert',
            timestamp: '2024-01-15T10:50:00Z',
            status: 'Active Incidents',
            isAlert: true,
            buildingId: 2,
            zoneId: 3,
            floorId: 5,
            description: 'Fixed security camera monitoring storage area',
            publicAddress: 'Warehouse Complex, Ground Floor, Storage Area Alpha, Storage Bay Alpha-1',
        },
        {
            id: 17,
            eventCode: generateEventCode('camera', 6),
            deviceType: 'camera',
            name: 'Operations Camera CAM-006',
            timestamp: '2024-01-15T10:35:00Z',
            status: 'recording',
            isAlert: false,
            buildingId: 2,
            zoneId: 4,
            floorId: 6,
            description: 'High-definition security camera monitoring warehouse operations center',
            publicAddress: 'Warehouse Complex, Second Floor, Office Wing, Warehouse Operations Center',
        },
        {
            id: 18,
            eventCode: generateEventCode('door', 6),
            deviceType: 'door',
            name: 'Laboratory Security Breach',
            timestamp: '2024-01-15T10:55:00Z',
            status: 'locked',
            isAlert: true,
            buildingId: 3,
            zoneId: 5,
            floorId: 7,
            description: 'High-security laboratory door with biometric access control',
            publicAddress: 'R&D Center, Ground Floor, Laboratory Section, Research Lab 1',
        },
        {
            id: 19,
            eventCode: generateEventCode('fire', 4),
            deviceType: 'fire',
            name: 'Fire Detector FD-004 Status',
            timestamp: '2024-01-15T10:00:00Z',
            status: 'normal',
            isAlert: false,
            buildingId: 3,
            zoneId: 5,
            floorId: 8,
            description: 'Advanced multi-sensor fire detector for laboratory environment',
            publicAddress: 'R&D Center, Second Floor, Laboratory Section, Testing Lab',
        },
        {
            id: 20,
            eventCode: generateEventCode('camera', 7),
            deviceType: 'camera',
            name: 'Development Area Camera Failure',
            timestamp: '2024-01-15T09:30:00Z',
            status: 'offline',
            isAlert: true,
            buildingId: 3,
            zoneId: 6,
            floorId: 9,
            description: 'Network security camera with remote monitoring capability',
            publicAddress: 'R&D Center, Third Floor, Development Area, Development Workshop',
        },
        {
            id: 21,
            eventCode: generateEventCode('gate', 5),
            deviceType: 'gate',
            name: 'Control Room Gate Access',
            timestamp: '2024-01-15T08:00:00Z',
            status: 'open',
            isAlert: false,
            buildingId: 4,
            zoneId: 7,
            floorId: 10,
            description: 'Automated security gate for control room access',
            publicAddress: 'Security Operations Center, Ground Floor, Control Room, Main Control Room',
        },
        {
            id: 22,
            eventCode: generateEventCode('fire', 5),
            deviceType: 'fire',
            name: 'Fire Alarm System Emergency',
            timestamp: '2024-01-15T10:58:00Z',
            status: 'alarm_triggered',
            isAlert: true,
            buildingId: 5,
            zoneId: 8,
            floorId: 12,
            description: 'Integrated fire detection and alarm system',
            publicAddress: 'Guest House, Ground Floor, Reception Area, Reception Desk',
        },
        {
            id: 23,
            eventCode: generateEventCode('gate', 6),
            deviceType: 'gate',
            name: 'Perimeter Gate Breach Attempt',
            timestamp: '2024-01-15T11:30:00Z',
            status: 'Unauthorized attempts',
            isAlert: true,
            buildingId: 1,
            zoneId: 1,
            floorId: 1,
            description: 'Perimeter security gate with access control system',
            publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Perimeter Gate',
        },
        {
            id: 24,
            eventCode: generateEventCode('camera', 8),
            deviceType: 'camera',
            name: 'Parking Lot Surveillance',
            timestamp: '2024-01-15T09:00:00Z',
            status: 'Active Incidents',
            isAlert: true,
            buildingId: 1,
            zoneId: 1,
            floorId: 1,
            description: 'Outdoor security camera monitoring parking area',
            publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Parking Lot',
        },
        {
            id: 25,
            eventCode: generateEventCode('door', 7),
            deviceType: 'door',
            name: 'Emergency Exit Door',
            timestamp: '2024-01-15T11:45:00Z',
            status: 'Closed',
            isAlert: false,
            buildingId: 1,
            zoneId: 1,
            floorId: 1,
            description: 'Emergency exit door with push bar mechanism',
            publicAddress: 'Main Corporate Building, Ground Floor, North Wing, Emergency Exit',
        },
    ],
};
