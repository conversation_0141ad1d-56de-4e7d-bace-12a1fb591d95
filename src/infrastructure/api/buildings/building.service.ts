import { fetchy } from '@/shared/lib/Fetchy';
import { logger } from '@/infrastructure/logging';
import { appConfig } from '@/shared/config/app-settings.config';
import { Building, BuildingApiResponse, GetBuildingsQueryParams } from './types';

export class BuildingService {
    private static instance: BuildingService;

    public static getInstance(): BuildingService {
        if (!BuildingService.instance) {
            BuildingService.instance = new BuildingService();
        }
        return BuildingService.instance;
    }

    public async getBuildings(params: GetBuildingsQueryParams = {}): Promise<BuildingApiResponse> {
        logger.info('[BuildingService] fetching buildings with query params:', params);

        try {
            if (appConfig.get('mockApiData')) {
                return this.fakeFetchBuildings(params);
            }
            return this.realFetchBuildings(params);
        } catch (error: unknown) {
            logger.error('[BuildingService] Error fetching buildings:', error as Error);
            throw error;
        }
    }

    public async getBuildingById(id: number): Promise<Building> {
        logger.info('[BuildingService] fetching building by id:', id);

        try {
            const response = await fetchy.get<BuildingApiResponse>(`/api/v1/buildings/${id}`);
            
            if (response.data.response_code !== '000') {
                throw new Error(response.data.response_message);
            }

            return response.data.data.buildings[0];
        } catch (error: unknown) {
            logger.error('[BuildingService] Error fetching building by id:', error as Error);
            throw error;
        }
    }

    private async realFetchBuildings(params: Partial<GetBuildingsQueryParams> = {}): Promise<BuildingApiResponse> {
        const response = await fetchy.get<BuildingApiResponse>('/api/v1/buildings', { params });
        
        if (response.data.response_code !== '000') {
            logger.error('[BuildingService] API error:', response.data.response_message);
            throw new Error(response.data.response_message);
        }

        return response.data;
    }

    private fakeFetchBuildings(params: Partial<GetBuildingsQueryParams> = {}): Promise<BuildingApiResponse> {
        logger.info('[BuildingService] Using mock data for buildings');
        
        const mockResponse: BuildingApiResponse = {
            response_code: "000",
            response_message: "Success",
            response_message_ar: "نجح",
            data: {
                buildings: [
                    {
                        id: 1,
                        name: "Building A",
                        code: "A",
                        description: "Main headquarters building",
                        address: "123 Technology Drive",
                        is_active: true,
                        zone_count: 2,
                        floor_count: 3,
                        room_count: 4,
                        door_count: 2,
                        device_count: 10,
                        event_count: 22,
                        has_alerts: false,
                        create_date: "2025-09-23T10:47:38.352278",
                        write_date: "2025-09-23T10:47:38.352278",
                        zones_url: "/api/v1/buildings/1/zones",
                        floors_url: "/api/v1/buildings/1/floors",
                        devices_url: "/api/v1/buildings/1/devices"
                    }
                ],
                total_count: 1,
                returned_count: 1,
                pagination: {
                    limit: 100,
                    offset: 0,
                    has_more: false
                }
            }
        };
        
        return Promise.resolve(mockResponse);
    }
}

export const buildingService = BuildingService.getInstance();