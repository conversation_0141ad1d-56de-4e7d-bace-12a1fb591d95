import { useFetchyQuery, useFetchyMutation } from '@/shared/lib/Fetchy';
import { buildingService } from './building.service';
import { Building, GetBuildingsQueryParams } from './types';

export const useBuildings = (params: GetBuildingsQueryParams = {}) => {
    return useFetchyQuery(
        ['buildings', params],
        () => buildingService.getBuildings(params),
        {
            enabled: true,
            staleTime: 5 * 60 * 1000, // 5 minutes
            retry: 3
        }
    );
};

export const useBuilding = (id: number) => {
    return useFetchyQuery(
        ['building', id],
        () => buildingService.getBuildingById(id),
        {
            enabled: !!id,
            staleTime: 5 * 60 * 1000,
            retry: 3
        }
    );
};