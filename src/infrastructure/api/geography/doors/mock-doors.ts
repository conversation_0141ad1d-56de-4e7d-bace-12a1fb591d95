import { Door } from './types';

export const mockDoors: Door[] = [
    // Building A - Executive Office 101 (Room ID: 1)
    {
        id: 1,
        name: 'Executive Office Main Door',
        doorCode: 'D101A',
        type: 'main_entry',
        roomId: 1,
        status: 'closed',
        accessLevel: 'restricted',
        isActive: true,
        lastStatusChange: '2024-01-20T14:30:00Z',
        createdAt: '2024-01-15T09:30:00Z',
        updatedAt: '2024-01-20T16:00:00Z'
    },
    {
        id: 2,
        name: 'Executive Office Emergency Exit',
        doorCode: 'D101B',
        type: 'emergency_exit',
        roomId: 1,
        status: 'locked',
        accessLevel: 'emergency_only',
        isActive: true,
        lastStatusChange: '2024-01-15T09:30:00Z',
        createdAt: '2024-01-15T09:30:00Z',
        updatedAt: '2024-01-20T16:00:00Z'
    },
    // Conference Room Alpha (Room ID: 2)
    {
        id: 3,
        name: 'Conference Alpha Main Door',
        doorCode: 'DCONF1A',
        type: 'standard',
        roomId: 2,
        status: 'open',
        accessLevel: 'public',
        isActive: true,
        lastStatusChange: '2024-01-20T09:15:00Z',
        createdAt: '2024-01-15T09:45:00Z',
        updatedAt: '2024-01-20T16:15:00Z'
    },
    // Main Lobby (Room ID: 3)
    {
        id: 4,
        name: 'Lobby Main Entrance',
        doorCode: 'DLOB1A',
        type: 'main_entry',
        roomId: 3,
        status: 'open',
        accessLevel: 'public',
        isActive: true,
        lastStatusChange: '2024-01-20T08:00:00Z',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-20T16:30:00Z'
    },
    {
        id: 5,
        name: 'Lobby Emergency Exit',
        doorCode: 'DLOB1B',
        type: 'emergency_exit',
        roomId: 3,
        status: 'locked',
        accessLevel: 'emergency_only',
        isActive: true,
        lastStatusChange: '2024-01-15T10:00:00Z',
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-20T16:30:00Z'
    },
    // Office 201 (Room ID: 4)
    {
        id: 6,
        name: 'Office 201 Door',
        doorCode: 'D201A',
        type: 'standard',
        roomId: 4,
        status: 'closed',
        accessLevel: 'restricted',
        isActive: true,
        lastStatusChange: '2024-01-20T17:30:00Z',
        createdAt: '2024-01-15T10:15:00Z',
        updatedAt: '2024-01-20T16:45:00Z'
    },
    // Office 202 (Room ID: 5)
    {
        id: 7,
        name: 'Office 202 Door',
        doorCode: 'D202A',
        type: 'standard',
        roomId: 5,
        status: 'closed',
        accessLevel: 'restricted',
        isActive: true,
        lastStatusChange: '2024-01-20T16:45:00Z',
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-20T17:00:00Z'
    },
    // Operations Center (Room ID: 6)
    {
        id: 8,
        name: 'Operations Center Main Door',
        doorCode: 'DOPS1A',
        type: 'security',
        roomId: 6,
        status: 'locked',
        accessLevel: 'admin_only',
        isActive: true,
        lastStatusChange: '2024-01-20T18:00:00Z',
        createdAt: '2024-01-15T11:00:00Z',
        updatedAt: '2024-01-20T17:15:00Z'
    },
    // Utility Room (Room ID: 7)
    {
        id: 9,
        name: 'Utility Room Door',
        doorCode: 'DUTIL1A',
        type: 'standard',
        roomId: 7,
        status: 'locked',
        accessLevel: 'restricted',
        isActive: true,
        lastStatusChange: '2024-01-20T12:00:00Z',
        createdAt: '2024-01-15T11:15:00Z',
        updatedAt: '2024-01-20T17:30:00Z'
    },
    // Technical Office 210 (Room ID: 8)
    {
        id: 10,
        name: 'Technical Office 210 Door',
        doorCode: 'D210A',
        type: 'standard',
        roomId: 8,
        status: 'closed',
        accessLevel: 'restricted',
        isActive: true,
        lastStatusChange: '2024-01-20T15:30:00Z',
        createdAt: '2024-01-15T11:30:00Z',
        updatedAt: '2024-01-20T17:45:00Z'
    },
    // Storage Room 211 (Room ID: 9)
    {
        id: 11,
        name: 'Storage Room 211 Door',
        doorCode: 'DSTOR1A',
        type: 'standard',
        roomId: 9,
        status: 'locked',
        accessLevel: 'restricted',
        isActive: true,
        lastStatusChange: '2024-01-20T10:00:00Z',
        createdAt: '2024-01-15T11:45:00Z',
        updatedAt: '2024-01-20T18:00:00Z'
    },
    // Storage Bay Alpha-1 (Room ID: 10)
    {
        id: 12,
        name: 'Bay Alpha-1 Main Door',
        doorCode: 'DBAY1A',
        type: 'main_entry',
        roomId: 10,
        status: 'closed',
        accessLevel: 'restricted',
        isActive: true,
        lastStatusChange: '2024-01-18T14:00:00Z',
        createdAt: '2024-01-10T10:30:00Z',
        updatedAt: '2024-01-18T13:00:00Z'
    },
    {
        id: 13,
        name: 'Bay Alpha-1 Loading Door',
        doorCode: 'DBAY1B',
        type: 'standard',
        roomId: 10,
        status: 'locked',
        accessLevel: 'restricted',
        isActive: true,
        lastStatusChange: '2024-01-18T16:30:00Z',
        createdAt: '2024-01-10T10:30:00Z',
        updatedAt: '2024-01-18T13:00:00Z'
    },
    // Storage Bay Alpha-2 (Room ID: 11)
    {
        id: 14,
        name: 'Bay Alpha-2 Main Door',
        doorCode: 'DBAY2A',
        type: 'main_entry',
        roomId: 11,
        status: 'open',
        accessLevel: 'restricted',
        isActive: true,
        lastStatusChange: '2024-01-18T08:30:00Z',
        createdAt: '2024-01-10T10:45:00Z',
        updatedAt: '2024-01-18T13:15:00Z'
    },
    {
        id: 15,
        name: 'Bay Alpha-2 Emergency Exit',
        doorCode: 'DBAY2B',
        type: 'emergency_exit',
        roomId: 11,
        status: 'locked',
        accessLevel: 'emergency_only',
        isActive: true,
        lastStatusChange: '2024-01-10T10:45:00Z',
        createdAt: '2024-01-10T10:45:00Z',
        updatedAt: '2024-01-18T13:15:00Z'
    },
    // Warehouse Operations Center (Room ID: 12)
    {
        id: 16,
        name: 'Warehouse Ops Center Door',
        doorCode: 'DWOPS1A',
        type: 'security',
        roomId: 12,
        status: 'closed',
        accessLevel: 'restricted',
        isActive: true,
        lastStatusChange: '2024-01-18T11:00:00Z',
        createdAt: '2024-01-10T11:00:00Z',
        updatedAt: '2024-01-18T13:30:00Z'
    },
    // Conference Room Beta (Room ID: 13)
    {
        id: 17,
        name: 'Conference Beta Door',
        doorCode: 'DCONF2A',
        type: 'standard',
        roomId: 13,
        status: 'closed',
        accessLevel: 'public',
        isActive: true,
        lastStatusChange: '2024-01-18T15:30:00Z',
        createdAt: '2024-01-10T11:15:00Z',
        updatedAt: '2024-01-18T13:45:00Z'
    },
    // Research Lab 1 (Room ID: 14)
    {
        id: 18,
        name: 'Research Lab 1 Door',
        doorCode: 'DLAB1A',
        type: 'security',
        roomId: 14,
        status: 'locked',
        accessLevel: 'admin_only',
        isActive: true,
        lastStatusChange: '2024-01-22T19:00:00Z',
        createdAt: '2024-01-05T12:00:00Z',
        updatedAt: '2024-01-22T18:00:00Z'
    },
    // Research Lab 2 (Room ID: 15)
    {
        id: 19,
        name: 'Research Lab 2 Door',
        doorCode: 'DLAB2A',
        type: 'security',
        roomId: 15,
        status: 'closed',
        accessLevel: 'restricted',
        isActive: true,
        lastStatusChange: '2024-01-22T16:45:00Z',
        createdAt: '2024-01-05T12:15:00Z',
        updatedAt: '2024-01-22T18:15:00Z'
    },
    // Testing Lab (Room ID: 16)
    {
        id: 20,
        name: 'Testing Lab Door',
        doorCode: 'DTEST1A',
        type: 'security',
        roomId: 16,
        status: 'locked',
        accessLevel: 'restricted',
        isActive: true,
        lastStatusChange: '2024-01-22T17:30:00Z',
        createdAt: '2024-01-05T12:30:00Z',
        updatedAt: '2024-01-22T18:30:00Z'
    },
    // Clean Room (Room ID: 17)
    {
        id: 21,
        name: 'Clean Room Door',
        doorCode: 'DCLEAN1A',
        type: 'security',
        roomId: 17,
        status: 'locked',
        accessLevel: 'admin_only',
        isActive: true,
        lastStatusChange: '2024-01-22T18:00:00Z',
        createdAt: '2024-01-05T12:45:00Z',
        updatedAt: '2024-01-22T18:45:00Z'
    },
    // Development Workshop (Room ID: 18)
    {
        id: 22,
        name: 'Development Workshop Door',
        doorCode: 'DDEV1A',
        type: 'standard',
        roomId: 18,
        status: 'closed',
        accessLevel: 'restricted',
        isActive: true,
        lastStatusChange: '2024-01-22T14:30:00Z',
        createdAt: '2024-01-05T13:00:00Z',
        updatedAt: '2024-01-22T19:00:00Z'
    },
    // Prototype Lab (Room ID: 19)
    {
        id: 23,
        name: 'Prototype Lab Door',
        doorCode: 'DPROTO1A',
        type: 'security',
        roomId: 19,
        status: 'locked',
        accessLevel: 'restricted',
        isActive: true,
        lastStatusChange: '2024-01-22T15:45:00Z',
        createdAt: '2024-01-05T13:15:00Z',
        updatedAt: '2024-01-22T19:15:00Z'
    },
    // Main Control Room (Room ID: 20)
    {
        id: 24,
        name: 'Main Control Room Door',
        doorCode: 'DCTRL1A',
        type: 'security',
        roomId: 20,
        status: 'locked',
        accessLevel: 'admin_only',
        isActive: true,
        lastStatusChange: '2024-01-19T15:00:00Z',
        createdAt: '2024-01-12T09:00:00Z',
        updatedAt: '2024-01-19T14:30:00Z'
    },
    {
        id: 25,
        name: 'Control Room Emergency Exit',
        doorCode: 'DCTRL1B',
        type: 'fire_exit',
        roomId: 20,
        status: 'locked',
        accessLevel: 'emergency_only',
        isActive: true,
        lastStatusChange: '2024-01-12T09:00:00Z',
        createdAt: '2024-01-12T09:00:00Z',
        updatedAt: '2024-01-19T14:30:00Z'
    },
    // Security Office (Room ID: 21)
    {
        id: 26,
        name: 'Security Office Door',
        doorCode: 'DSEC1A',
        type: 'security',
        roomId: 21,
        status: 'closed',
        accessLevel: 'admin_only',
        isActive: true,
        lastStatusChange: '2024-01-19T13:30:00Z',
        createdAt: '2024-01-12T09:15:00Z',
        updatedAt: '2024-01-19T14:45:00Z'
    },
    // Reception Desk (Room ID: 22)
    {
        id: 27,
        name: 'Reception Main Entrance',
        doorCode: 'DREC1A',
        type: 'main_entry',
        roomId: 22,
        status: 'open',
        accessLevel: 'public',
        isActive: true,
        lastStatusChange: '2024-01-21T08:00:00Z',
        createdAt: '2024-01-08T13:30:00Z',
        updatedAt: '2024-01-21T11:00:00Z'
    },
    // Waiting Area (Room ID: 23)
    {
        id: 28,
        name: 'Waiting Area Door',
        doorCode: 'DWAIT1A',
        type: 'standard',
        roomId: 23,
        status: 'open',
        accessLevel: 'public',
        isActive: true,
        lastStatusChange: '2024-01-21T08:30:00Z',
        createdAt: '2024-01-08T13:45:00Z',
        updatedAt: '2024-01-21T11:15:00Z'
    },
    // Guest Room 1 (Room ID: 24)
    {
        id: 29,
        name: 'Guest Room 1 Door',
        doorCode: 'DGUEST1A',
        type: 'standard',
        roomId: 24,
        status: 'closed',
        accessLevel: 'restricted',
        isActive: true,
        lastStatusChange: '2024-01-21T20:00:00Z',
        createdAt: '2024-01-08T14:00:00Z',
        updatedAt: '2024-01-21T11:30:00Z'
    },
    // Guest Room 2 (Room ID: 25)
    {
        id: 30,
        name: 'Guest Room 2 Door',
        doorCode: 'DGUEST2A',
        type: 'standard',
        roomId: 25,
        status: 'locked',
        accessLevel: 'restricted',
        isActive: true,
        lastStatusChange: '2024-01-21T22:00:00Z',
        createdAt: '2024-01-08T14:15:00Z',
        updatedAt: '2024-01-21T11:45:00Z'
    }
];

// Helper functions
export const getDoorsByRoomId = (roomId: number): Door[] => {
    return mockDoors.filter(door => door.roomId === roomId && door.isActive);
};

export const getDoorByCode = (doorCode: string): Door | undefined => {
    return mockDoors.find(door => door.doorCode === doorCode);
};

export const getDoorById = (id: number): Door | undefined => {
    return mockDoors.find(door => door.id === id);
};

export const getDoorsByStatus = (status: string): Door[] => {
    return mockDoors.filter(door => door.status === status && door.isActive);
};

export const getDoorsByType = (type: string): Door[] => {
    return mockDoors.filter(door => door.type === type && door.isActive);
};

export const getDoorsByAccessLevel = (accessLevel: string): Door[] => {
    return mockDoors.filter(door => door.accessLevel === accessLevel && door.isActive);
};