export interface Door {
    id: number;
    name: string;
    doorCode: string; // e.g., 'D101A', 'D202B', 'EMRG01'
    type: 'main_entry' | 'emergency_exit' | 'standard' | 'security' | 'fire_exit';
    roomId: number;
    status: 'open' | 'closed' | 'locked' | 'malfunction';
    accessLevel: 'public' | 'restricted' | 'emergency_only' | 'admin_only';
    isActive: boolean;
    lastStatusChange: string;
    createdAt: string;
    updatedAt: string;
}

export interface DoorReference {
    id: number;
    name: string;
    doorCode: string;
    type: string;
    status: string;
    accessLevel: string;
    roomId: number;
}

export interface DoorStats {
    totalDoors: number;
    byStatus: {
        open: number;
        closed: number;
        locked: number;
        malfunction: number;
    };
    byType: {
        main_entry: number;
        emergency_exit: number;
        standard: number;
        security: number;
        fire_exit: number;
    };
    byAccessLevel: {
        public: number;
        restricted: number;
        emergency_only: number;
        admin_only: number;
    };
}