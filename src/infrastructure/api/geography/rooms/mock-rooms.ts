import { Room } from './types';

export const mockRooms: Room[] = [
    // Building A - North Wing - Ground Floor (Floor ID: 1)
    {
        id: 1,
        name: 'Executive Office 101',
        roomCode: 'R101',
        floorId: 1,
        roomType: 'office',
        capacity: 4,
        area: 25.5,
        isActive: true,
        createdAt: '2024-01-15T09:30:00Z',
        updatedAt: '2024-01-20T16:00:00Z'
    },
    {
        id: 2,
        name: 'Conference Room Alpha',
        roomCode: 'CONF1',
        floorId: 1,
        roomType: 'conference',
        capacity: 12,
        area: 40.0,
        isActive: true,
        createdAt: '2024-01-15T09:45:00Z',
        updatedAt: '2024-01-20T16:15:00Z'
    },
    {
        id: 3,
        name: 'Main Lobby',
        roomCode: 'LOBBY1',
        floorId: 1,
        roomType: 'lobby',
        capacity: 50,
        area: 80.0,
        isActive: true,
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-20T16:30:00Z'
    },
    // Building A - North Wing - Second Floor (Floor ID: 2)
    {
        id: 4,
        name: 'Office 201',
        roomCode: 'R201',
        floorId: 2,
        roomType: 'office',
        capacity: 6,
        area: 30.0,
        isActive: true,
        createdAt: '2024-01-15T10:15:00Z',
        updatedAt: '2024-01-20T16:45:00Z'
    },
    {
        id: 5,
        name: 'Office 202',
        roomCode: 'R202',
        floorId: 2,
        roomType: 'office',
        capacity: 4,
        area: 22.5,
        isActive: true,
        createdAt: '2024-01-15T10:30:00Z',
        updatedAt: '2024-01-20T17:00:00Z'
    },
    // Building A - South Wing - Ground Floor (Floor ID: 3)
    {
        id: 6,
        name: 'Operations Center',
        roomCode: 'OPS1',
        floorId: 3,
        roomType: 'office',
        capacity: 8,
        area: 45.0,
        isActive: true,
        createdAt: '2024-01-15T11:00:00Z',
        updatedAt: '2024-01-20T17:15:00Z'
    },
    {
        id: 7,
        name: 'Utility Room',
        roomCode: 'UTIL1',
        floorId: 3,
        roomType: 'utility',
        area: 15.0,
        isActive: true,
        createdAt: '2024-01-15T11:15:00Z',
        updatedAt: '2024-01-20T17:30:00Z'
    },
    // Building A - South Wing - Second Floor (Floor ID: 4)
    {
        id: 8,
        name: 'Technical Office 210',
        roomCode: 'R210',
        floorId: 4,
        roomType: 'office',
        capacity: 6,
        area: 28.0,
        isActive: true,
        createdAt: '2024-01-15T11:30:00Z',
        updatedAt: '2024-01-20T17:45:00Z'
    },
    {
        id: 9,
        name: 'Storage Room 211',
        roomCode: 'STOR1',
        floorId: 4,
        roomType: 'storage',
        area: 20.0,
        isActive: true,
        createdAt: '2024-01-15T11:45:00Z',
        updatedAt: '2024-01-20T18:00:00Z'
    },
    // Building B - Storage Area Alpha - Ground Floor (Floor ID: 5)
    {
        id: 10,
        name: 'Storage Bay Alpha-1',
        roomCode: 'BAY1',
        floorId: 5,
        roomType: 'storage',
        area: 200.0,
        isActive: true,
        createdAt: '2024-01-10T10:30:00Z',
        updatedAt: '2024-01-18T13:00:00Z'
    },
    {
        id: 11,
        name: 'Storage Bay Alpha-2',
        roomCode: 'BAY2',
        floorId: 5,
        roomType: 'storage',
        area: 180.0,
        isActive: true,
        createdAt: '2024-01-10T10:45:00Z',
        updatedAt: '2024-01-18T13:15:00Z'
    },
    // Building B - Office Wing - Second Floor (Floor ID: 6)
    {
        id: 12,
        name: 'Warehouse Operations Center',
        roomCode: 'WOPS1',
        floorId: 6,
        roomType: 'office',
        capacity: 10,
        area: 50.0,
        isActive: true,
        createdAt: '2024-01-10T11:00:00Z',
        updatedAt: '2024-01-18T13:30:00Z'
    },
    {
        id: 13,
        name: 'Conference Room Beta',
        roomCode: 'CONF2',
        floorId: 6,
        roomType: 'conference',
        capacity: 8,
        area: 35.0,
        isActive: true,
        createdAt: '2024-01-10T11:15:00Z',
        updatedAt: '2024-01-18T13:45:00Z'
    },
    // Building C - Laboratory Section - Ground Floor (Floor ID: 7)
    {
        id: 14,
        name: 'Research Lab 1',
        roomCode: 'LAB1',
        floorId: 7,
        roomType: 'other',
        capacity: 6,
        area: 60.0,
        isActive: true,
        createdAt: '2024-01-05T12:00:00Z',
        updatedAt: '2024-01-22T18:00:00Z'
    },
    {
        id: 15,
        name: 'Research Lab 2',
        roomCode: 'LAB2',
        floorId: 7,
        roomType: 'other',
        capacity: 8,
        area: 70.0,
        isActive: true,
        createdAt: '2024-01-05T12:15:00Z',
        updatedAt: '2024-01-22T18:15:00Z'
    },
    // Building C - Laboratory Section - Second Floor (Floor ID: 8)
    {
        id: 16,
        name: 'Testing Lab',
        roomCode: 'TEST1',
        floorId: 8,
        roomType: 'other',
        capacity: 4,
        area: 45.0,
        isActive: true,
        createdAt: '2024-01-05T12:30:00Z',
        updatedAt: '2024-01-22T18:30:00Z'
    },
    {
        id: 17,
        name: 'Clean Room',
        roomCode: 'CLEAN1',
        floorId: 8,
        roomType: 'other',
        capacity: 2,
        area: 25.0,
        isActive: true,
        createdAt: '2024-01-05T12:45:00Z',
        updatedAt: '2024-01-22T18:45:00Z'
    },
    // Building C - Development Area - Third Floor (Floor ID: 9)
    {
        id: 18,
        name: 'Development Workshop',
        roomCode: 'DEV1',
        floorId: 9,
        roomType: 'other',
        capacity: 12,
        area: 80.0,
        isActive: true,
        createdAt: '2024-01-05T13:00:00Z',
        updatedAt: '2024-01-22T19:00:00Z'
    },
    {
        id: 19,
        name: 'Prototype Lab',
        roomCode: 'PROTO1',
        floorId: 9,
        roomType: 'other',
        capacity: 6,
        area: 55.0,
        isActive: true,
        createdAt: '2024-01-05T13:15:00Z',
        updatedAt: '2024-01-22T19:15:00Z'
    },
    // Building D - Control Room - Ground Floor (Floor ID: 10)
    {
        id: 20,
        name: 'Main Control Room',
        roomCode: 'CTRL1',
        floorId: 10,
        roomType: 'office',
        capacity: 15,
        area: 100.0,
        isActive: true,
        createdAt: '2024-01-12T09:00:00Z',
        updatedAt: '2024-01-19T14:30:00Z'
    },
    // Building D - Control Room - Second Floor (Floor ID: 11)
    {
        id: 21,
        name: 'Security Office',
        roomCode: 'SEC1',
        floorId: 11,
        roomType: 'office',
        capacity: 8,
        area: 40.0,
        isActive: true,
        createdAt: '2024-01-12T09:15:00Z',
        updatedAt: '2024-01-19T14:45:00Z'
    },
    // Building E - Reception Area - Ground Floor (Floor ID: 12)
    {
        id: 22,
        name: 'Reception Desk',
        roomCode: 'REC1',
        floorId: 12,
        roomType: 'lobby',
        capacity: 20,
        area: 60.0,
        isActive: true,
        createdAt: '2024-01-08T13:30:00Z',
        updatedAt: '2024-01-21T11:00:00Z'
    },
    {
        id: 23,
        name: 'Waiting Area',
        roomCode: 'WAIT1',
        floorId: 12,
        roomType: 'lobby',
        capacity: 30,
        area: 75.0,
        isActive: true,
        createdAt: '2024-01-08T13:45:00Z',
        updatedAt: '2024-01-21T11:15:00Z'
    },
    // Building E - Guest Quarters - Second Floor (Floor ID: 13)
    {
        id: 24,
        name: 'Guest Room 1',
        roomCode: 'GUEST1',
        floorId: 13,
        roomType: 'other',
        capacity: 2,
        area: 20.0,
        isActive: true,
        createdAt: '2024-01-08T14:00:00Z',
        updatedAt: '2024-01-21T11:30:00Z'
    },
    {
        id: 25,
        name: 'Guest Room 2',
        roomCode: 'GUEST2',
        floorId: 13,
        roomType: 'other',
        capacity: 2,
        area: 20.0,
        isActive: true,
        createdAt: '2024-01-08T14:15:00Z',
        updatedAt: '2024-01-21T11:45:00Z'
    }
];

// Helper functions
export const getRoomsByFloorId = (floorId: number): Room[] => {
    return mockRooms.filter(room => room.floorId === floorId && room.isActive);
};

export const getRoomByCode = (roomCode: string, floorId?: number): Room | undefined => {
    return mockRooms.find(room => 
        room.roomCode === roomCode && 
        (floorId ? room.floorId === floorId : true)
    );
};

export const getRoomById = (id: number): Room | undefined => {
    return mockRooms.find(room => room.id === id);
};

export const getRoomsByType = (roomType: string): Room[] => {
    return mockRooms.filter(room => room.roomType === roomType && room.isActive);
};