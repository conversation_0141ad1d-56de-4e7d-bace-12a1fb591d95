export interface Room {
    id: number;
    name: string;
    roomCode: string; // e.g., 'R101', 'R202', 'CONF1'
    floorId: number;
    roomType: 'office' | 'conference' | 'storage' | 'lobby' | 'restroom' | 'utility' | 'other';
    capacity?: number;
    area?: number; // in square meters
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
}

export interface RoomReference {
    id: number;
    name: string;
    roomCode: string;
    roomType: string;
    floorId: number;
}

export interface RoomStats {
    roomId: number;
    roomName: string;
    roomCode: string;
    roomType: string;
    totalDoors: number;
    doorStats: {
        open: number;
        closed: number;
        locked: number;
    };
}