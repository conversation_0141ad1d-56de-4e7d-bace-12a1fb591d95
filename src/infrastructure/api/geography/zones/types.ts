export interface Zone {
    id: number;
    name: string;
    description: string;
    buildingId: number;
    zoneCode: string; // e.g., 'NW', 'SW', 'E1'
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
}

export interface ZoneReference {
    id: number;
    name: string;
    zoneCode: string;
    buildingId: number;
}

export interface ZoneStats {
    zoneId: number;
    zoneName: string;
    zoneCode: string;
    totalFloors: number;
    totalRooms: number;
    totalDoors: number;
}