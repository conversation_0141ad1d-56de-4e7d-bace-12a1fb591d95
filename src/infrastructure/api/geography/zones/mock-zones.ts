import { Zone } from './types';

export const mockZones: Zone[] = [
    // Building A (Main Corporate Building) zones
    {
        id: 1,
        name: 'North Wing',
        description: 'Administrative and executive offices',
        buildingId: 1,
        zoneCode: 'NW',
        isActive: true,
        createdAt: '2024-01-15T08:30:00Z',
        updatedAt: '2024-01-20T15:00:00Z'
    },
    {
        id: 2,
        name: 'South Wing',
        description: 'Operations and technical facilities',
        buildingId: 1,
        zoneCode: 'SW',
        isActive: true,
        createdAt: '2024-01-15T08:45:00Z',
        updatedAt: '2024-01-20T15:15:00Z'
    },
    // Building B (Warehouse Complex) zones
    {
        id: 3,
        name: 'Storage Area Alpha',
        description: 'High-security storage zone',
        buildingId: 2,
        zoneCode: 'SA',
        isActive: true,
        createdAt: '2024-01-10T09:30:00Z',
        updatedAt: '2024-01-18T12:00:00Z'
    },
    {
        id: 4,
        name: 'Office Wing',
        description: 'Administrative offices for warehouse operations',
        buildingId: 2,
        zoneCode: 'OW',
        isActive: true,
        createdAt: '2024-01-10T09:45:00Z',
        updatedAt: '2024-01-18T12:15:00Z'
    },
    // Building C (R&D Center) zones
    {
        id: 5,
        name: 'Laboratory Section',
        description: 'Research laboratories and testing facilities',
        buildingId: 3,
        zoneCode: 'LAB',
        isActive: true,
        createdAt: '2024-01-05T11:00:00Z',
        updatedAt: '2024-01-22T16:45:00Z'
    },
    {
        id: 6,
        name: 'Development Area',
        description: 'Product development and prototyping',
        buildingId: 3,
        zoneCode: 'DEV',
        isActive: true,
        createdAt: '2024-01-05T11:15:00Z',
        updatedAt: '2024-01-22T17:00:00Z'
    },
    // Building D (Security Operations Center) zones
    {
        id: 7,
        name: 'Control Room',
        description: 'Central monitoring and control systems',
        buildingId: 4,
        zoneCode: 'CTL',
        isActive: true,
        createdAt: '2024-01-12T08:00:00Z',
        updatedAt: '2024-01-19T13:30:00Z'
    },
    // Building E (Guest Services) zones
    {
        id: 8,
        name: 'Reception Area',
        description: 'Visitor reception and waiting areas',
        buildingId: 5,
        zoneCode: 'REC',
        isActive: true,
        createdAt: '2024-01-08T12:30:00Z',
        updatedAt: '2024-01-21T10:00:00Z'
    },
    {
        id: 9,
        name: 'Guest Quarters',
        description: 'Temporary accommodation for visitors',
        buildingId: 5,
        zoneCode: 'GQ',
        isActive: true,
        createdAt: '2024-01-08T12:45:00Z',
        updatedAt: '2024-01-21T10:15:00Z'
    }
];

// Helper functions
export const getZonesByBuildingId = (buildingId: number): Zone[] => {
    return mockZones.filter(zone => zone.buildingId === buildingId && zone.isActive);
};

export const getZoneByCode = (zoneCode: string, buildingId?: number): Zone | undefined => {
    return mockZones.find(zone => 
        zone.zoneCode === zoneCode && 
        (buildingId ? zone.buildingId === buildingId : true)
    );
};

export const getZoneById = (id: number): Zone | undefined => {
    return mockZones.find(zone => zone.id === id);
};