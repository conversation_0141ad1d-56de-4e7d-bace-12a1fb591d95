export interface Building {
    id: number;
    name: string;
    shortCode: string; // e.g., 'A', 'B', 'C'
    address: string;
    description: string;
    totalFloors: number;
    totalRooms: number;
    totalDoors: number;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
}

export interface BuildingStats {
    buildingId: number;
    buildingName: string;
    shortCode: string;
    totalZones: number;
    totalFloors: number;
    totalRooms: number;
    totalDoors: number;
    doorStats: {
        open: number;
        closed: number;
        locked: number;
    };
}

export interface BuildingReference {
    id: number;
    name: string;
    shortCode: string;
}