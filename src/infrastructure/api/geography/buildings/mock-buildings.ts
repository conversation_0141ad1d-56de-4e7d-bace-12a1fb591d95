import { Building } from './types';

export const mockBuildings: Building[] = [
    {
        id: 1,
        name: 'Main Corporate Building',
        shortCode: 'A',
        address: '123 Corporate Plaza, Business District',
        description: 'Primary corporate facility with comprehensive security systems',
        totalFloors: 4,
        totalRooms: 12,
        totalDoors: 24,
        isActive: true,
        createdAt: '2024-01-15T08:00:00Z',
        updatedAt: '2024-01-20T14:30:00Z'
    },
    {
        id: 2,
        name: 'Warehouse Complex',
        shortCode: 'B',
        address: '456 Industrial Avenue, Logistics Park',
        description: 'Storage and distribution facility with automated systems',
        totalFloors: 2,
        totalRooms: 8,
        totalDoors: 16,
        isActive: true,
        createdAt: '2024-01-10T09:15:00Z',
        updatedAt: '2024-01-18T11:45:00Z'
    },
    {
        id: 3,
        name: 'Research & Development Center',
        shortCode: 'C',
        address: '789 Innovation Drive, Tech Campus',
        description: 'Advanced R&D facility with specialized laboratories',
        totalFloors: 3,
        totalRooms: 15,
        totalDoors: 30,
        isActive: true,
        createdAt: '2024-01-05T10:30:00Z',
        updatedAt: '2024-01-22T16:20:00Z'
    },
    {
        id: 4,
        name: 'Security Operations Center',
        shortCode: 'D',
        address: '321 Security Boulevard, Control District',
        description: 'Central security monitoring and control facility',
        totalFloors: 2,
        totalRooms: 6,
        totalDoors: 12,
        isActive: true,
        createdAt: '2024-01-12T07:45:00Z',
        updatedAt: '2024-01-19T13:10:00Z'
    },
    {
        id: 5,
        name: 'Guest Services Building',
        shortCode: 'E',
        address: '654 Welcome Street, Reception Area',
        description: 'Visitor reception and guest accommodation facility',
        totalFloors: 2,
        totalRooms: 10,
        totalDoors: 20,
        isActive: true,
        createdAt: '2024-01-08T12:00:00Z',
        updatedAt: '2024-01-21T09:30:00Z'
    }
];

// Helper functions
export const getBuildingByShortCode = (shortCode: string): Building | undefined => {
    return mockBuildings.find(building => building.shortCode === shortCode);
};

export const getBuildingById = (id: number): Building | undefined => {
    return mockBuildings.find(building => building.id === id);
};

export const getActiveBuildingsCount = (): number => {
    return mockBuildings.filter(building => building.isActive).length;
};

export const getAllBuildingShortCodes = (): string[] => {
    return mockBuildings.map(building => building.shortCode);
};