import { useState, useEffect, useMemo } from 'react';
import { geographyService, LocationReference, NavigationContext } from './geography-service';
import type { Building } from './buildings';
import type { Zone } from './zones';
import type { Floor } from './floors';
import type { Room } from './rooms';
import type { Door } from './doors';

// Hook for managing geography data
export const useGeography = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const service = useMemo(() => geographyService, []);

    // Building operations
    const getAllBuildings = () => {
        try {
            setIsLoading(true);
            const buildings = service.getAllBuildings();
            setError(null);
            return buildings;
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load buildings');
            return [];
        } finally {
            setIsLoading(false);
        }
    };

    const getBuildingByShortCode = (shortCode: string) => {
        try {
            return service.getBuildingByShortCode(shortCode);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load building');
            return undefined;
        }
    };

    const getBuildingStats = (buildingId: number) => {
        try {
            return service.getBuildingStats(buildingId);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load building stats');
            return null;
        }
    };

    // Zone operations
    const getZonesByBuildingId = (buildingId: number) => {
        try {
            return service.getZonesByBuildingId(buildingId);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load zones');
            return [];
        }
    };

    // Floor operations
    const getFloorsByZoneId = (zoneId: number) => {
        try {
            return service.getFloorsByZoneId(zoneId);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load floors');
            return [];
        }
    };

    // Room operations
    const getRoomsByFloorId = (floorId: number) => {
        try {
            return service.getRoomsByFloorId(floorId);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load rooms');
            return [];
        }
    };

    // Door operations
    const getDoorsByRoomId = (roomId: number) => {
        try {
            return service.getDoorsByRoomId(roomId);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load doors');
            return [];
        }
    };

    // Navigation operations
    const getLocationReference = (buildingId: number, zoneId: number, floorId: number, roomId?: number) => {
        try {
            return service.getLocationReference(buildingId, zoneId, floorId, roomId);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to get location reference');
            return null;
        }
    };

    const getNavigationContext = (buildingId?: number, zoneId?: number, floorId?: number, roomId?: number) => {
        try {
            return service.getNavigationContext(buildingId, zoneId, floorId, roomId);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to get navigation context');
            return { breadcrumb: [] };
        }
    };

    // Search operations
    const searchByCode = (code: string) => {
        try {
            return service.searchByCode(code);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Search failed');
            return [];
        }
    };

    // Statistics
    const getOverallStats = () => {
        try {
            return service.getOverallStats();
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to load statistics');
            return {
                totalBuildings: 0,
                totalZones: 0,
                totalFloors: 0,
                totalRooms: 0,
                totalDoors: 0,
                doorStatusBreakdown: { open: 0, closed: 0, locked: 0, malfunction: 0 }
            };
        }
    };

    return {
        // State
        isLoading,
        error,
        
        // Building methods
        getAllBuildings,
        getBuildingByShortCode,
        getBuildingStats,
        
        // Zone methods
        getZonesByBuildingId,
        
        // Floor methods
        getFloorsByZoneId,
        
        // Room methods
        getRoomsByFloorId,
        
        // Door methods
        getDoorsByRoomId,
        
        // Navigation methods
        getLocationReference,
        getNavigationContext,
        
        // Search methods
        searchByCode,
        
        // Statistics
        getOverallStats,
        
        // Direct service access for advanced usage
        service
    };
};

// Hook for specific building data
export const useBuildingData = (buildingShortCode?: string) => {
    const [building, setBuilding] = useState<Building | undefined>();
    const [zones, setZones] = useState<Zone[]>([]);
    const [stats, setStats] = useState<ReturnType<typeof geographyService.getBuildingStats> | null>(null);
    const { getBuildingByShortCode, getZonesByBuildingId, getBuildingStats } = useGeography();

    useEffect(() => {
        if (buildingShortCode) {
            const buildingData = getBuildingByShortCode(buildingShortCode);
            setBuilding(buildingData);
            
            if (buildingData) {
                const zonesData = getZonesByBuildingId(buildingData.id);
                setZones(zonesData);
                
                const statsData = getBuildingStats(buildingData.id);
                setStats(statsData);
            }
        }
    }, [buildingShortCode, getBuildingByShortCode, getZonesByBuildingId, getBuildingStats]);

    return {
        building,
        zones,
        stats
    };
};

// Hook for navigation breadcrumb
export const useNavigationBreadcrumb = (buildingId?: number, zoneId?: number, floorId?: number, roomId?: number) => {
    const { getNavigationContext } = useGeography();
    
    const navigationContext = useMemo(() => {
        return getNavigationContext(buildingId, zoneId, floorId, roomId);
    }, [buildingId, zoneId, floorId, roomId, getNavigationContext]);

    return navigationContext;
};