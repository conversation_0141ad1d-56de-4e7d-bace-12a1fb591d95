import { mockBuildings } from '../geography/buildings/mock-buildings';
import { mockZones } from '../geography/zones/mock-zones';
import { mockFloors } from '../geography/floors/mock-floors';
import { mockRooms } from '../geography/rooms/mock-rooms';

/**
 * Generates a formatted zone string combining floor, building, zone, and room information
 * Format: "Floor {floorName}, Building {buildingShortCode}, {zoneName}, {roomName}"
 * Example: "Floor 1, Building A, North Wing, Executive Office 101"
 */
export function generateZoneFormat(floorId: number, buildingId: number, roomName?: string): string {
    // Get floor information
    const floor = mockFloors.find(f => f.id === floorId);
    if (!floor) {
        return 'Unknown Location';
    }

    // Get building information
    const building = mockBuildings.find(b => b.id === buildingId);
    if (!building) {
        return 'Unknown Location';
    }

    // Get zone information using the floor's zoneId
    const zone = mockZones.find(z => z.id === floor.zoneId);
    if (!zone) {
        return 'Unknown Location';
    }

    // Get room information if roomName is provided
    let roomInfo = '';
    if (roomName) {
        // Try to find the room by name in the floor
        const room = mockRooms.find(r => r.floorId === floorId && r.name === roomName);
        roomInfo = room ? room.name : roomName;
    }

    // Build the formatted string
    const parts = [
        `Floor ${floor.level}`, // Use level number for consistency
        `Building ${building.shortCode}`,
        zone.name
    ];

    if (roomInfo) {
        parts.push(roomInfo);
    }

    return parts.join(', ');
}

/**
 * Generates zone format for markers based on their geographic IDs
 */
export function getMarkerZoneFormat(floorId: number, buildingId: number): string {
    return generateZoneFormat(floorId, buildingId);
}

/**
 * Generates zone format with room information
 */
export function getMarkerZoneFormatWithRoom(floorId: number, buildingId: number, roomName: string): string {
    return generateZoneFormat(floorId, buildingId, roomName);
}