import { Markers } from './types';
import { mockMarkers } from './mock-markers';

/**
 * Marker service for loading marker data from JSON files or fallback to mock data
 */
export class MarkerService {
    private static instance: MarkerService;
    private cache: Map<string, Markers> = new Map();

    private constructor() {}

    public static getInstance(): MarkerService {
        if (!MarkerService.instance) {
            MarkerService.instance = new MarkerService();
        }
        return MarkerService.instance;
    }

    /**
     * Get markers for a specific building and floor
     */
    public async getMarkersByBuildingAndFloor(buildingId: number, floorId: number): Promise<Markers> {
        const cacheKey = `b${buildingId}-f${floorId}`;

        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey)!;
        }

        try {
            const jsonData = await this.loadMarkersFromJson(buildingId, floorId);
            this.cache.set(cacheKey, jsonData);
            return jsonData;
        } catch (error) {
            console.warn(`❌ Failed to load markers for ${cacheKey}, using mock data.`, error);
            const fallback = this.filterMockMarkers(buildingId, floorId);
            this.cache.set(cacheKey, fallback);
            return fallback;
        }
    }

    /**
     * Get all mock markers
     */
    public getAllMarkers(): Markers {
        return mockMarkers;
    }

    /**
     * Load markers from public/markers JSON
     */
    private async loadMarkersFromJson(buildingId: number, floorId: number): Promise<Markers> {
        const fileName = `b${buildingId}-f${floorId}.json`;
        const filePath = `/markers/${fileName}`; // must exist in /public/markers/

        const response = await fetch(filePath);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return (await response.json()) as Markers;
    }

    /**
     * Filter mock markers by building + floor
     */
    private filterMockMarkers(buildingId: number, floorId: number): Markers {
        const filtered = mockMarkers.markers.filter((m) => m.buildingId === buildingId && m.floorId === floorId);

        return { markers: filtered };
    }

    /**
     * Clear cache for one or all building/floor combos
     */
    public clearCache(buildingId?: number, floorId?: number): void {
        if (buildingId !== undefined && floorId !== undefined) {
            this.cache.delete(`b${buildingId}-f${floorId}`);
        } else {
            this.cache.clear();
        }
    }

    /**
     * Preload markers for multiple building/floor combos
     */
    public async preloadMarkers(combos: Array<{ buildingId: number; floorId: number }>): Promise<void> {
        const tasks = combos.map(({ buildingId, floorId }) => this.getMarkersByBuildingAndFloor(buildingId, floorId));
        await Promise.allSettled(tasks);
    }
}

// Singleton export
export const markerService = MarkerService.getInstance();
export const getMarkersByBuildingAndFloor = (b: number, f: number) => markerService.getMarkersByBuildingAndFloor(b, f);
export const getAllMarkers = () => markerService.getAllMarkers();
export const clearMarkerCache = (b?: number, f?: number) => markerService.clearCache(b, f);
