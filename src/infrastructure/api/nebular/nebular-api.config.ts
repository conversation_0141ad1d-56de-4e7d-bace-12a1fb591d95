/**
 * Nebular Event Management System API Configuration
 *
 * Centralized API configuration for the Nebular Event Management System
 * using the Fetchy library with proper interceptors and error handling.
 */

import { Fetchy } from '@/shared/lib/Fetchy';
import { appConfig } from '@/shared/config/app.config';
import { isDevelopment } from '@/shared/config/app-settings.config';

const config = appConfig.getConfig();

// Create Nebular API instance with configuration
export const nebularApi = Fetchy.getInstance({
    baseURL: config.nebularApi.baseUrl,
    timeout: config.nebularApi.timeout,
    retries: config.nebularApi.retryAttempts,
    retryDelay: config.nebularApi.retryDelay,
    headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
    },
    Logger: null, // Will be set up with proper logger if needed
});

// ✅ Add request interceptor
nebularApi.addRequestInterceptor((config) => {
    if (isDevelopment()) {
        console.log('[Nebular API] Request:', {
            method: config.method?.toUpperCase(),
            url: config.url,
            params: config.params,
            data: config.data,
        });
    }

    // // Safely set headers using AxiosHeaders API
    // if (config.headers) {
    //     // Axios v1 uses `AxiosHeaders`
    //     (config.headers as any)['X-Client'] = 'Nebular-Dashboard';
    //     // (config.headers as any)['X-Timestamp'] = new Date().toISOString();
    // }

    return config;
});

// ✅ Add response interceptor
nebularApi.addResponseInterceptor(
    (response) => {
        if (isDevelopment()) {
            console.log('[Nebular API] Response:', {
                status: response.status,
                statusText: response.statusText,
                data: response.data,
            });
        }
        return response;
    },
    (error) => {
        console.error('[Nebular API] Error:', {
            status: error.response?.status,
            statusText: error.response?.statusText,
            message: error.message,
            data: error.response?.data,
        });

        if (error.response?.status === 404) {
            console.warn('[Nebular API] Resource not found');
        } else if (error.response?.status && error.response.status >= 500) {
            console.error('[Nebular API] Server error');
        }

        return Promise.reject(error);
    },
);

export default nebularApi;
