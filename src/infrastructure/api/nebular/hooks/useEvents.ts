/**
 * Nebular Event Management System - React Query Hooks
 *
 * Custom hooks for event data fetching using React Query
 * with proper caching, loading states, and error handling.
 */

import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { eventService } from '../event.service';
import type {
    CreateEventRequest,
    GetEventsResponse,
    GetEventResponse,
    CreateEventResponse,
    GetEventsQueryParams,
    EventType,
    SystemCode,
    BaseApiResponse,
} from '@/shared/types/nebular-api.types';

// Query Keys
export const eventQueryKeys = {
    all: ['events'] as const,
    lists: () => [...eventQueryKeys.all, 'list'] as const,
    list: (params?: GetEventsQueryParams) => [...eventQueryKeys.lists(), params] as const,
    details: () => [...eventQueryKeys.all, 'detail'] as const,
    detail: (id: number) => [...eventQueryKeys.details(), id] as const,
    stats: () => [...eventQueryKeys.all, 'stats'] as const,
    bySystem: (systemCode: SystemCode) => [...eventQueryKeys.all, 'system', systemCode] as const,
    byType: (eventType: EventType) => [...eventQueryKeys.all, 'type', eventType] as const,
    byBuilding: (buildingId: number) => [...eventQueryKeys.all, 'building', buildingId] as const,
    byFloor: (floorId: number) => [...eventQueryKeys.all, 'floor', floorId] as const,
    upcoming: () => [...eventQueryKeys.all, 'upcoming'] as const,
    past: () => [...eventQueryKeys.all, 'past'] as const,
    search: (searchTerm: string) => [...eventQueryKeys.all, 'search', searchTerm] as const,
    dateRange: (dateFrom: string, dateTo: string) => [...eventQueryKeys.all, 'dateRange', dateFrom, dateTo] as const,
};

// Hook Options Types
type UseEventsOptions = Omit<UseQueryOptions<GetEventsResponse>, 'queryKey' | 'queryFn'>;
type UseEventOptions = Omit<UseQueryOptions<GetEventResponse>, 'queryKey' | 'queryFn'>;
type UseCreateEventOptions = Omit<UseMutationOptions<CreateEventResponse, Error, CreateEventRequest>, 'mutationFn'>;
type UseUpdateEventOptions = Omit<
    UseMutationOptions<CreateEventResponse, Error, { id: number; data: Partial<CreateEventRequest> }>,
    'mutationFn'
>;
type UseDeleteEventOptions = Omit<UseMutationOptions<BaseApiResponse, Error, number>, 'mutationFn'>;

/**
 * Hook to fetch all events with optional filtering
 */
export const useEvents = (params?: GetEventsQueryParams, options?: UseEventsOptions) => {
    return useQuery({
        queryKey: eventQueryKeys.list(params),
        queryFn: () => eventService.getEvents(params),
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
        ...options,
    });
};

/**
 * Hook to fetch a specific event by ID
 */
export const useEvent = (id: number, options?: UseEventOptions) => {
    return useQuery({
        queryKey: eventQueryKeys.detail(id),
        queryFn: () => eventService.getEvent(id),
        enabled: !!id,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes
        ...options,
    });
};

/**
 * Hook to fetch events by system code
 */
export const useEventsBySystem = (
    systemCode: SystemCode,
    params?: Omit<GetEventsQueryParams, 'system_code'>,
    options?: UseEventsOptions,
) => {
    return useQuery({
        queryKey: eventQueryKeys.bySystem(systemCode),
        queryFn: () => eventService.getEventsBySystem(systemCode, params),
        enabled: !!systemCode,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        ...options,
    });
};

/**
 * Hook to fetch events by type
 */
export const useEventsByType = (
    eventType: EventType,
    params?: Omit<GetEventsQueryParams, 'event_type'>,
    options?: UseEventsOptions,
) => {
    return useQuery({
        queryKey: eventQueryKeys.byType(eventType),
        queryFn: () => eventService.getEventsByType(eventType, params),
        enabled: !!eventType,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        ...options,
    });
};

/**
 * Hook to fetch events by building
 */
export const useEventsByBuilding = (
    buildingId: number,
    params?: Omit<GetEventsQueryParams, 'building_id'>,
    options?: UseEventsOptions,
) => {
    return useQuery({
        queryKey: eventQueryKeys.byBuilding(buildingId),
        queryFn: () => eventService.getEventsByBuilding(buildingId, params),
        enabled: !!buildingId,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        ...options,
    });
};

/**
 * Hook to fetch events by floor
 */
export const useEventsByFloor = (
    floorId: number,
    params?: Omit<GetEventsQueryParams, 'floor_id'>,
    options?: UseEventsOptions,
) => {
    return useQuery({
        queryKey: eventQueryKeys.byFloor(floorId),
        queryFn: () => eventService.getEventsByFloor(floorId, params),
        enabled: !!floorId,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        ...options,
    });
};

/**
 * Hook to search events
 */
export const useSearchEvents = (
    searchTerm: string,
    params?: Omit<GetEventsQueryParams, 'search'>,
    options?: UseEventsOptions,
) => {
    return useQuery({
        queryKey: eventQueryKeys.search(searchTerm),
        queryFn: () => eventService.searchEvents(searchTerm, params),
        enabled: !!searchTerm && searchTerm.length > 2, // Only search if term is longer than 2 characters
        staleTime: 2 * 60 * 1000, // 2 minutes for search results
        gcTime: 5 * 60 * 1000, // 5 minutes
        ...options,
    });
};

/**
 * Hook to fetch events by date range
 */
export const useEventsByDateRange = (
    dateFrom: string,
    dateTo: string,
    params?: Omit<GetEventsQueryParams, 'date_from' | 'date_to'>,
    options?: UseEventsOptions,
) => {
    return useQuery({
        queryKey: eventQueryKeys.dateRange(dateFrom, dateTo),
        queryFn: () => eventService.getEventsByDateRange(dateFrom, dateTo, params),
        enabled: !!dateFrom && !!dateTo,
        staleTime: 5 * 60 * 1000,
        gcTime: 10 * 60 * 1000,
        ...options,
    });
};

/**
 * Hook to fetch upcoming events
 */
export const useUpcomingEvents = (params?: Omit<GetEventsQueryParams, 'date_from'>, options?: UseEventsOptions) => {
    return useQuery({
        queryKey: eventQueryKeys.upcoming(),
        queryFn: () => eventService.getUpcomingEvents(params),
        staleTime: 2 * 60 * 1000, // 2 minutes for upcoming events
        gcTime: 5 * 60 * 1000,
        refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
        ...options,
    });
};

/**
 * Hook to fetch past events
 */
export const usePastEvents = (params?: Omit<GetEventsQueryParams, 'date_to'>, options?: UseEventsOptions) => {
    return useQuery({
        queryKey: eventQueryKeys.past(),
        queryFn: () => eventService.getPastEvents(params),
        staleTime: 10 * 60 * 1000, // 10 minutes for past events (they don't change often)
        gcTime: 30 * 60 * 1000, // 30 minutes
        ...options,
    });
};

/**
 * Hook to fetch event statistics
 */
export const useEventStats = (
    options?: Omit<UseQueryOptions<Awaited<ReturnType<typeof eventService.getEventStats>>>, 'queryKey' | 'queryFn'>,
) => {
    return useQuery({
        queryKey: eventQueryKeys.stats(),
        queryFn: () => eventService.getEventStats(),
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 15 * 60 * 1000, // 15 minutes
        ...options,
    });
};

/**
 * Hook to create a new event
 */
export const useCreateEvent = (options?: UseCreateEventOptions) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (eventData: CreateEventRequest) => eventService.createEvent(eventData),
        onSuccess: (data, variables, context) => {
            // Invalidate and refetch event lists
            queryClient.invalidateQueries({ queryKey: eventQueryKeys.lists() });
            queryClient.invalidateQueries({ queryKey: eventQueryKeys.stats() });

            // Invalidate specific system and type queries
            queryClient.invalidateQueries({ queryKey: eventQueryKeys.bySystem(variables.systemCode) });
            queryClient.invalidateQueries({ queryKey: eventQueryKeys.byType(variables.eventType) });
            queryClient.invalidateQueries({ queryKey: eventQueryKeys.byBuilding(variables.buildingId) });
            queryClient.invalidateQueries({ queryKey: eventQueryKeys.byFloor(variables.floorId) });

            // Call custom onSuccess if provided
            options?.onSuccess?.(data, variables, context);
        },
        ...options,
    });
};

/**
 * Hook to update an existing event
 */
export const useUpdateEvent = (options?: UseUpdateEventOptions) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ id, data }: { id: number; data: Partial<CreateEventRequest> }) =>
            eventService.updateEvent(id, data),
        onSuccess: (data, variables, context) => {
            // Invalidate and refetch event lists
            queryClient.invalidateQueries({ queryKey: eventQueryKeys.lists() });
            queryClient.invalidateQueries({ queryKey: eventQueryKeys.stats() });

            // Invalidate the specific event detail
            queryClient.invalidateQueries({ queryKey: eventQueryKeys.detail(variables.id) });

            // Invalidate related queries if system or type changed
            if (variables.data.systemCode) {
                queryClient.invalidateQueries({ queryKey: eventQueryKeys.bySystem(variables.data.systemCode) });
            }
            if (variables.data.eventType) {
                queryClient.invalidateQueries({ queryKey: eventQueryKeys.byType(variables.data.eventType) });
            }
            if (variables.data.buildingId) {
                queryClient.invalidateQueries({ queryKey: eventQueryKeys.byBuilding(variables.data.buildingId) });
            }
            if (variables.data.floorId) {
                queryClient.invalidateQueries({ queryKey: eventQueryKeys.byFloor(variables.data.floorId) });
            }

            // Call custom onSuccess if provided
            options?.onSuccess?.(data, variables, context);
        },
        ...options,
    });
};

/**
 * Hook to delete an event
 */
export const useDeleteEvent = (options?: UseDeleteEventOptions) => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (id: number) => eventService.deleteEvent(id),
        onSuccess: (data, variables, context) => {
            // Invalidate and refetch event lists
            queryClient.invalidateQueries({ queryKey: eventQueryKeys.lists() });
            queryClient.invalidateQueries({ queryKey: eventQueryKeys.stats() });

            // Remove the specific event from cache
            queryClient.removeQueries({ queryKey: eventQueryKeys.detail(variables) });

            // Call custom onSuccess if provided
            options?.onSuccess?.(data, variables, context);
        },
        ...options,
    });
};

/**
 * Hook to test API connection
 */
export const useTestConnection = (options?: Omit<UseQueryOptions<boolean>, 'queryKey' | 'queryFn'>) => {
    return useQuery({
        queryKey: ['events', 'connection-test'],
        queryFn: () => eventService.testConnection(),
        staleTime: 30 * 1000, // 30 seconds
        gcTime: 60 * 1000, // 1 minute
        retry: 1, // Only retry once for connection tests
        ...options,
    });
};
