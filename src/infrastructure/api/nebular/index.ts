/**
 * Nebular Event Management System API - Main Export
 *
 * Centralized exports for all Nebular API services, hooks, and types
 */

// API Configuration
export { default as nebularApi } from './nebular-api.config';

// Services
export { EventService, eventService } from './event.service';

// React Query Hooks
export {
    // Query Hooks
    useEvents,
    useEvent,
    useEventsBySystem,
    useEventsByType,
    useEventsByBuilding,
    useEventsByFloor,
    useSearchEvents,
    useEventsByDateRange,
    useUpcomingEvents,
    usePastEvents,
    useEventStats,
    useTestConnection,

    // Mutation Hooks
    useCreateEvent,
    useUpdateEvent,
    useDeleteEvent,

    // Query Keys
    eventQueryKeys,
} from './hooks/useEvents';

// Types (re-export from shared types)
export type {
    Event,
    CreateEventRequest,
    GetEventsResponse,
    GetEventResponse,
    CreateEventResponse,
    GetEventsQueryParams,
    EventType,
    SystemCode,
    BaseApiResponse,
    SystemData,
    FireAlarmData,
    AccessControlData,
    CctvData,
    GateBarrierData,
    PublicAddressData,
    PresenceDetectionData,
    SystemInfo,
    ResponseCode,
} from '@/shared/types/nebular-api.types';

// Constants
export { AVAILABLE_SYSTEMS, RESPONSE_CODES } from '@/shared/types/nebular-api.types';
