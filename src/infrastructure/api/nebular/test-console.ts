/**
 * Nebular Event Management System - Console Test Script
 *
 * Test script to validate the API implementation without UI updates.
 * This script can be run in the browser console or Node.js environment.
 * node test-nebular-api.js
 */

import { eventService } from './event.service';
import type { CreateEventRequest, GetEventsQueryParams } from '@/shared/types/nebular-api.types';

/**
 * Console Test Suite for Nebular Event Management System
 */
export class NebularApiTestSuite {
    private testResults: Array<{ test: string; status: 'PASS' | 'FAIL'; message: string; duration: number }> = [];

    /**
     * Run all tests
     */
    async runAllTests(): Promise<void> {
        console.log('🚀 Starting Nebular API Test Suite...\n');

        await this.testConnection();
        await this.testGetEvents();
        await this.testGetEventsBySystem();
        await this.testGetEventsByType();
        await this.testSearchEvents();
        await this.testGetEventStats();
        await this.testCreateEvent();
        await this.testUpdateEvent();
        await this.testDeleteEvent();

        this.printResults();
    }

    /**
     * Test API connection
     */
    async testConnection(): Promise<void> {
        const startTime = Date.now();
        try {
            const isConnected = await eventService.testConnection();
            const duration = Date.now() - startTime;

            if (isConnected) {
                this.addResult('Connection Test', 'PASS', 'API connection successful', duration);
                console.log('✅ Connection Test: PASSED');
            } else {
                this.addResult('Connection Test', 'FAIL', 'API connection failed', duration);
                console.log('❌ Connection Test: FAILED');
            }
        } catch (error) {
            const duration = Date.now() - startTime;
            this.addResult('Connection Test', 'FAIL', `Error: ${error}`, duration);
            console.log('❌ Connection Test: FAILED -', error);
        }
    }

    /**
     * Test getting all events
     */
    async testGetEvents(): Promise<void> {
        const startTime = Date.now();
        try {
            const params: GetEventsQueryParams = { limit: 5 };
            const response = await eventService.getEvents(params);
            const duration = Date.now() - startTime;

            if (response && typeof response === 'object') {
                this.addResult('Get Events', 'PASS', `Retrieved ${response.Events?.length || 0} events`, duration);
                console.log('✅ Get Events: PASSED -', `Found ${response.Events?.length || 0} events`);
                console.log('📊 Sample Response:', JSON.stringify(response, null, 2));
            } else {
                this.addResult('Get Events', 'FAIL', 'Invalid response format', duration);
                console.log('❌ Get Events: FAILED - Invalid response format');
            }
        } catch (error) {
            const duration = Date.now() - startTime;
            this.addResult('Get Events', 'FAIL', `Error: ${error}`, duration);
            console.log('❌ Get Events: FAILED -', error);
        }
    }

    /**
     * Test getting events by system
     */
    async testGetEventsBySystem(): Promise<void> {
        const startTime = Date.now();
        try {
            const response = await eventService.getEventsBySystem('fire', { limit: 3 });
            const duration = Date.now() - startTime;

            if (response && typeof response === 'object') {
                this.addResult(
                    'Get Events by System',
                    'PASS',
                    `Retrieved ${response.Events?.length || 0} fire events`,
                    duration,
                );
                console.log('✅ Get Events by System: PASSED -', `Found ${response.Events?.length || 0} fire events`);
            } else {
                this.addResult('Get Events by System', 'FAIL', 'Invalid response format', duration);
                console.log('❌ Get Events by System: FAILED - Invalid response format');
            }
        } catch (error) {
            const duration = Date.now() - startTime;
            this.addResult('Get Events by System', 'FAIL', `Error: ${error}`, duration);
            console.log('❌ Get Events by System: FAILED -', error);
        }
    }

    /**
     * Test getting events by type
     */
    async testGetEventsByType(): Promise<void> {
        const startTime = Date.now();
        try {
            const response = await eventService.getEventsByType('alarm', { limit: 3 });
            const duration = Date.now() - startTime;

            if (response && typeof response === 'object') {
                this.addResult(
                    'Get Events by Type',
                    'PASS',
                    `Retrieved ${response.Events?.length || 0} alarm events`,
                    duration,
                );
                console.log('✅ Get Events by Type: PASSED -', `Found ${response.Events?.length || 0} alarm events`);
            } else {
                this.addResult('Get Events by Type', 'FAIL', 'Invalid response format', duration);
                console.log('❌ Get Events by Type: FAILED - Invalid response format');
            }
        } catch (error) {
            const duration = Date.now() - startTime;
            this.addResult('Get Events by Type', 'FAIL', `Error: ${error}`, duration);
            console.log('❌ Get Events by Type: FAILED -', error);
        }
    }

    /**
     * Test search events
     */
    async testSearchEvents(): Promise<void> {
        const startTime = Date.now();
        try {
            const response = await eventService.searchEvents('fire', { limit: 3 });
            const duration = Date.now() - startTime;

            if (response && typeof response === 'object') {
                this.addResult(
                    'Search Events',
                    'PASS',
                    `Found ${response.Events?.length || 0} events matching 'fire'`,
                    duration,
                );
                console.log(
                    '✅ Search Events: PASSED -',
                    `Found ${response.Events?.length || 0} events matching 'fire'`,
                );
            } else {
                this.addResult('Search Events', 'FAIL', 'Invalid response format', duration);
                console.log('❌ Search Events: FAILED - Invalid response format');
            }
        } catch (error) {
            const duration = Date.now() - startTime;
            this.addResult('Search Events', 'FAIL', `Error: ${error}`, duration);
            console.log('❌ Search Events: FAILED -', error);
        }
    }

    /**
     * Test getting event statistics
     */
    async testGetEventStats(): Promise<void> {
        const startTime = Date.now();
        try {
            const stats = await eventService.getEventStats();
            const duration = Date.now() - startTime;

            if (stats && typeof stats.total === 'number') {
                this.addResult('Get Event Stats', 'PASS', `Total events: ${stats.total}`, duration);
                console.log('✅ Get Event Stats: PASSED');
                console.log('📈 Statistics:', JSON.stringify(stats, null, 2));
            } else {
                this.addResult('Get Event Stats', 'FAIL', 'Invalid stats format', duration);
                console.log('❌ Get Event Stats: FAILED - Invalid stats format');
            }
        } catch (error) {
            const duration = Date.now() - startTime;
            this.addResult('Get Event Stats', 'FAIL', `Error: ${error}`, duration);
            console.log('❌ Get Event Stats: FAILED -', error);
        }
    }

    /**
     * Test creating an event
     */
    async testCreateEvent(): Promise<void> {
        const startTime = Date.now();
        try {
            const testEvent: CreateEventRequest = {
                buildingId: 1,
                buildingCode: 'TEST-BLDG',
                floorId: 1,
                floorCode: 'TEST-FLOOR',
                systemCode: 'fire',
                systemName: 'Fire Alarm System',
                eventType: 'test',
                datetime: new Date().toISOString(),
                message: 'Test event created by console test suite',
                sourceEventCode: 'TEST-001',
                sourceState: 'active',
                state: 'new',
                Data: {
                    panelId: 1,
                    panelCode: 'TEST-PANEL',
                    panelName: 'Test Fire Panel',
                    zone: 'Zone A',
                    loop: 'Loop 1',
                    nodeId: 1,
                    nodeCode: 'TEST-NODE',
                    address: '*************',
                },
            };

            const response = await eventService.createEvent(testEvent);
            const duration = Date.now() - startTime;

            if (response && response.ResponseCode === '000') {
                this.addResult('Create Event', 'PASS', 'Event created successfully', duration);
                console.log('✅ Create Event: PASSED');
                console.log('📝 Created Event:', JSON.stringify(response, null, 2));

                // Store the created event ID for update/delete tests
                (this as { createdEventId?: number }).createdEventId = response.eventId;
            } else {
                this.addResult('Create Event', 'FAIL', `Response code: ${response?.ResponseCode}`, duration);
                console.log('❌ Create Event: FAILED - Response code:', response?.ResponseCode);
            }
        } catch (error) {
            const duration = Date.now() - startTime;
            this.addResult('Create Event', 'FAIL', `Error: ${error}`, duration);
            console.log('❌ Create Event: FAILED -', error);
        }
    }

    /**
     * Test updating an event
     */
    async testUpdateEvent(): Promise<void> {
        const startTime = Date.now();
        try {
            const eventId = (this as { createdEventId?: number }).createdEventId || 1; // Use created event ID or fallback to 1
            const updateData = {
                message: 'Updated test event message',
                state: 'updated',
            };

            const response = await eventService.updateEvent(eventId, updateData);
            const duration = Date.now() - startTime;

            if (response && response.ResponseCode === '000') {
                this.addResult('Update Event', 'PASS', 'Event updated successfully', duration);
                console.log('✅ Update Event: PASSED');
                console.log('📝 Updated Event:', JSON.stringify(response, null, 2));
            } else {
                this.addResult('Update Event', 'FAIL', `Response code: ${response?.ResponseCode}`, duration);
                console.log('❌ Update Event: FAILED - Response code:', response?.ResponseCode);
            }
        } catch (error) {
            const duration = Date.now() - startTime;
            this.addResult('Update Event', 'FAIL', `Error: ${error}`, duration);
            console.log('❌ Update Event: FAILED -', error);
        }
    }

    /**
     * Test deleting an event
     */
    async testDeleteEvent(): Promise<void> {
        const startTime = Date.now();
        try {
            const eventId = (this as { createdEventId?: number }).createdEventId || 1; // Use created event ID or fallback to 1

            const response = await eventService.deleteEvent(eventId);
            const duration = Date.now() - startTime;

            if (response && response.ResponseCode === '000') {
                this.addResult('Delete Event', 'PASS', 'Event deleted successfully', duration);
                console.log('✅ Delete Event: PASSED');
            } else {
                this.addResult('Delete Event', 'FAIL', `Response code: ${response?.ResponseCode}`, duration);
                console.log('❌ Delete Event: FAILED - Response code:', response?.ResponseCode);
            }
        } catch (error) {
            const duration = Date.now() - startTime;
            this.addResult('Delete Event', 'FAIL', `Error: ${error}`, duration);
            console.log('❌ Delete Event: FAILED -', error);
        }
    }

    /**
     * Add test result
     */
    private addResult(test: string, status: 'PASS' | 'FAIL', message: string, duration: number): void {
        this.testResults.push({ test, status, message, duration });
    }

    /**
     * Print test results summary
     */
    private printResults(): void {
        console.log('\n📋 Test Results Summary:');
        console.log('========================');

        const passed = this.testResults.filter((r) => r.status === 'PASS').length;
        const failed = this.testResults.filter((r) => r.status === 'FAIL').length;
        const totalDuration = this.testResults.reduce((sum, r) => sum + r.duration, 0);

        this.testResults.forEach((result) => {
            const icon = result.status === 'PASS' ? '✅' : '❌';
            console.log(`${icon} ${result.test}: ${result.status} (${result.duration}ms) - ${result.message}`);
        });

        console.log('\n📊 Summary:');
        console.log(`Total Tests: ${this.testResults.length}`);
        console.log(`Passed: ${passed}`);
        console.log(`Failed: ${failed}`);
        console.log(`Success Rate: ${((passed / this.testResults.length) * 100).toFixed(1)}%`);
        console.log(`Total Duration: ${totalDuration}ms`);

        if (failed === 0) {
            console.log('\n🎉 All tests passed! The Nebular API implementation is working correctly.');
        } else {
            console.log('\n⚠️  Some tests failed. Please check the API configuration and server status.');
        }
    }

    /**
     * Quick test method for individual testing
     */
    async quickTest(): Promise<void> {
        console.log('🔍 Running Quick Test...\n');

        try {
            // Test connection
            const isConnected = await eventService.testConnection();
            console.log('Connection:', isConnected ? '✅ Connected' : '❌ Failed');

            // Test basic get events
            const events = await eventService.getEvents({ limit: 1 });
            console.log('Get Events:', events ? '✅ Success' : '❌ Failed');
            console.log('Sample Response:', JSON.stringify(events, null, 2));
        } catch (error) {
            console.log('❌ Quick Test Failed:', error);
        }
    }
}

// Create and export test instance
export const nebularApiTest = new NebularApiTestSuite();

// Global console methods for easy testing
if (typeof window !== 'undefined') {
    (
        window as unknown as {
            nebularApiTest: NebularApiTestSuite;
            testNebularApi: () => Promise<void>;
            quickTestNebularApi: () => Promise<void>;
        }
    ).nebularApiTest = nebularApiTest;
    (
        window as unknown as {
            nebularApiTest: NebularApiTestSuite;
            testNebularApi: () => Promise<void>;
            quickTestNebularApi: () => Promise<void>;
        }
    ).testNebularApi = () => nebularApiTest.runAllTests();
    (
        window as unknown as {
            nebularApiTest: NebularApiTestSuite;
            testNebularApi: () => Promise<void>;
            quickTestNebularApi: () => Promise<void>;
        }
    ).quickTestNebularApi = () => nebularApiTest.quickTest();
}

// Export for Node.js testing
export default nebularApiTest;
