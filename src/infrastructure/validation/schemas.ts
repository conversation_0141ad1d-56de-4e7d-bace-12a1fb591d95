import { z } from 'zod';

// Common regex patterns
const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
const STRONG_PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
const PHONE_REGEX = /^[+]?[1-9]\d{1,14}$/;
const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

// Basic schemas
export const EmailSchema = z.string().regex(EMAIL_REGEX, 'Invalid email format');
export const StrongPasswordSchema = z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(STRONG_PASSWORD_REGEX, 'Password must contain uppercase, lowercase, number, and special character');
export const PhoneSchema = z.string().regex(PHONE_REGEX, 'Invalid phone number format');
export const UuidSchema = z.string().regex(UUID_REGEX, 'Invalid UUID format');

// Pagination schema
export const PaginationSchema = z.object({
    pageSize: z.number().int().min(1).max(100).default(10),
    currentPage: z.number().int().min(1).default(1),
    totalPages: z.number().int().min(0).default(0),
    totalCount: z.number().int().min(0).default(0),
    hasPrevious: z.boolean().default(false),
    hasNext: z.boolean().default(false),
});

export const DEFAULT_PAGINATION = {
    pageSize: 10,
    currentPage: 1,
    totalPages: 0,
    totalCount: 0,
    hasPrevious: false,
    hasNext: false,
};

// Translatable text schema (for i18n)
export const TranslatableSchema = z
    .object({
        en: z.string().optional(),
        ar: z.string().optional(),
    })
    .refine((data) => data.en || data.ar, {
        message: 'At least one translation (en or ar) must be provided',
    });

// Location schema
export const LocationSchema = z.object({
    latitude: z.number().min(-90).max(90),
    longitude: z.number().min(-180).max(180),
    accuracy: z.number().optional(),
    altitude: z.number().optional(),
    heading: z.number().optional(),
    speed: z.number().optional(),
});

// Date range schema
export const DateRangeSchema = z
    .object({
        startDate: z.string().datetime().or(z.date()),
        endDate: z.string().datetime().or(z.date()),
    })
    .refine(
        (data) => {
            const start = new Date(data.startDate);
            const end = new Date(data.endDate);
            return start <= end;
        },
        {
            message: 'Start date must be before or equal to end date',
        },
    );

// API Response schemas
export const ApiResponseSchema = z.object({
    success: z.boolean(),
    message: z.string().optional(),
    data: z.unknown().optional(),
    errors: z.array(z.string()).optional(),
});

export const ApiErrorSchema = z.object({
    success: z.literal(false),
    message: z.string(),
    errors: z.array(z.string()).optional(),
    code: z.string().optional(),
    statusCode: z.number().optional(),
});

// File upload schema
export const FileUploadSchema = z
    .object({
        file: z.instanceof(File),
        maxSize: z.number().default(5 * 1024 * 1024), // 5MB default
        allowedTypes: z.array(z.string()).default(['image/jpeg', 'image/png', 'image/gif']),
    })
    .refine((data) => data.file.size <= data.maxSize, {
        message: 'File size exceeds maximum allowed size',
    })
    .refine((data) => data.allowedTypes.includes(data.file.type), {
        message: 'File type not allowed',
    });

// Search/Filter schemas
export const SearchSchema = z.object({
    query: z.string().min(1).max(255),
    filters: z.record(z.string(), z.unknown()).optional(),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).default('asc'),
    ...PaginationSchema.shape,
});

// Authentication schemas
export const LoginSchema = z.object({
    email: EmailSchema,
    password: z.string().min(1, 'Password is required'),
    rememberMe: z.boolean().default(false),
});

export const RegisterSchema = z
    .object({
        email: EmailSchema,
        password: StrongPasswordSchema,
        confirmPassword: z.string(),
        firstName: z.string().min(1, 'First name is required').max(50),
        lastName: z.string().min(1, 'Last name is required').max(50),
        phone: PhoneSchema.optional(),
        acceptTerms: z.boolean().refine((val) => val === true, {
            message: 'You must accept the terms and conditions',
        }),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: 'Passwords do not match',
        path: ['confirmPassword'],
    });

export const ForgotPasswordSchema = z.object({
    email: EmailSchema,
});

export const ResetPasswordSchema = z
    .object({
        token: z.string().min(1, 'Reset token is required'),
        password: StrongPasswordSchema,
        confirmPassword: z.string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: 'Passwords do not match',
        path: ['confirmPassword'],
    });

// User profile schema
export const UserProfileSchema = z.object({
    id: UuidSchema,
    email: EmailSchema,
    firstName: z.string().min(1).max(50),
    lastName: z.string().min(1).max(50),
    phone: PhoneSchema.optional(),
    avatar: z.string().url().optional(),
    isActive: z.boolean().default(true),
    createdAt: z.string().datetime(),
    updatedAt: z.string().datetime(),
});

// Contact form schema
export const ContactFormSchema = z.object({
    name: z.string().min(1, 'Name is required').max(100),
    email: EmailSchema,
    subject: z.string().min(1, 'Subject is required').max(200),
    message: z.string().min(10, 'Message must be at least 10 characters').max(1000),
});

// Newsletter subscription schema
export const NewsletterSchema = z.object({
    email: EmailSchema,
    preferences: z.array(z.string()).optional(),
});

// Export types
export type Pagination = z.infer<typeof PaginationSchema>;
export type TranslatableText = z.infer<typeof TranslatableSchema>;
export type Location = z.infer<typeof LocationSchema>;
export type DateRange = z.infer<typeof DateRangeSchema>;
export type ApiResponse = z.infer<typeof ApiResponseSchema>;
export type ApiError = z.infer<typeof ApiErrorSchema>;
export type FileUpload = z.infer<typeof FileUploadSchema>;
export type SearchParams = z.infer<typeof SearchSchema>;
export type LoginCredentials = z.infer<typeof LoginSchema>;
export type RegisterData = z.infer<typeof RegisterSchema>;
export type ForgotPasswordData = z.infer<typeof ForgotPasswordSchema>;
export type ResetPasswordData = z.infer<typeof ResetPasswordSchema>;
export type UserProfile = z.infer<typeof UserProfileSchema>;
export type ContactFormData = z.infer<typeof ContactFormSchema>;
export type NewsletterData = z.infer<typeof NewsletterSchema>;
