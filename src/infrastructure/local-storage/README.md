# Local Storage Infrastructure

A simple and type-safe local storage implementation for applications using Zustand state management.

## Features

- **Type Safety**: Generic TypeScript support for stored values
- **Error Handling**: Graceful error handling with console logging
- **Zustand Compatible**: Designed to work seamlessly with Zustand stores
- **Simple API**: Clean and intuitive interface

## Quick Start

### Basic Usage

```typescript
import { LocalStorage, LocalStorageKeys } from '@/infrastructure/local-storage';

// Store data
LocalStorage.set(LocalStorageKeys.userPreferences, { theme: 'dark', language: 'en' });

// Retrieve data
const preferences = LocalStorage.get(LocalStorageKeys.userPreferences);

// Remove data
LocalStorage.remove(LocalStorageKeys.userPreferences);
```

### Zustand Store Integration

```typescript
import { create } from 'zustand';
import { LocalStorage, LocalStorageKeys } from '@/infrastructure/local-storage';

interface ThemeStore {
    theme: string;
    setTheme: (theme: string) => void;
}

export const useThemeStore = create<ThemeStore>((set) => ({
    theme: LocalStorage.get(LocalStorageKeys.theme) || 'light',
    setTheme: (theme) => {
        LocalStorage.set(LocalStorageKeys.theme, theme);
        set({ theme });
    },
}));
```

## API Reference

### LocalStorage

- `set<T>(key: string, data: T): void` - Store data
- `get<T>(key: string): T | null` - Retrieve data
- `remove(key: string): void` - Remove data
- `has(key: string): boolean` - Check if key exists
- `clear(): void` - Clear all data
- `keys(): string[]` - Get all keys

### Integration with Zustand

The local storage works perfectly with Zustand's persistence middleware or manual integration:

```typescript
// Manual integration (recommended for fine control)
const initializeFromStorage = () => {
    const savedTheme = LocalStorage.get(LocalStorageKeys.theme);
    if (savedTheme) {
        useThemeStore.getState().setTheme(savedTheme);
    }
};
```

### LocalStorageKeys

Predefined keys for consistent usage:

- `authToken`: Authentication token
- `userPreferences`: User preferences
- `theme`: Application theme
- `language`: User language preference
- `settings`: Application settings
- `cache`: Temporary cache data

## Best Practices

1. **Use Predefined Keys**: Always use `LocalStorageKeys` constants
2. **Type Your Data**: Specify generic types when using `get<T>()`
3. **Handle Null Values**: Always check for null when retrieving data
4. **Store Integration**: Initialize stores with localStorage data on app startup
5. **Sync on Changes**: Update localStorage when store state changes

## Browser Compatibility

This implementation works in all modern browsers that support:
- `localStorage` API
- `JSON.stringify/parse`
- ES6+ features