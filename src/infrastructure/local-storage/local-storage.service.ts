class LocalStorageFactory {
    set<T>(key: string, data: T): void {
        try {
            localStorage.setItem(key, JSON.stringify(data));
        } catch (error) {
            console.error('LocalStorageFactory.set: Error saving to localStorage', error);
        }
    }

    get<T>(key: string): T | null {
        const data = localStorage.getItem(key);
        if (!data) return null;

        try {
            return JSON.parse(data) as T;
        } catch {
            return data as T;
        }
    }

    remove(key: string): void {
        localStorage.removeItem(key);
    }

    has(key: string): boolean {
        return localStorage.getItem(key) !== null;
    }

    clear(): void {
        localStorage.clear();
    }

    keys(): string[] {
        return Object.keys(localStorage);
    }
}

export const LocalStorage = new LocalStorageFactory();
