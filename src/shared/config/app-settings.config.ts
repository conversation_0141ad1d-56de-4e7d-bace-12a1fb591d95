/**
 * Application Settings Configuration
 * Environment-based configuration for the application
 */

export interface AppSettings {
    apiBaseUrl: string;
    environment: 'development' | 'production' | 'test';
    enableLogging: boolean;
    enableDevTools: boolean;
    version: string;
}

/**
 * Get application settings based on environment variables
 */
export const getAppSettings = (): AppSettings => {
    const isDevelopment = process.env.NODE_ENV === 'development';
    const isProduction = process.env.NODE_ENV === 'production';
    const isTest = process.env.NODE_ENV === 'test';

    return {
        apiBaseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api',
        environment: isProduction ? 'production' : isTest ? 'test' : 'development',
        enableLogging: isDevelopment || Boolean(process.env.NEXT_PUBLIC_ENABLE_LOGGING),
        enableDevTools: isDevelopment,
        version: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
    };
};

/**
 * Application settings instance
 */
export const appSettings = getAppSettings();

/**
 * Type guard to check if we're in development mode
 */
export const isDevelopment = () => appSettings.environment === 'development';

/**
 * Type guard to check if we're in production mode
 */
export const isProduction = () => appSettings.environment === 'production';

/**
 * Type guard to check if we're in test mode
 */
export const isTest = () => appSettings.environment === 'test';
