import { z, type ZodError, type ZodSchema } from 'zod';

import type { ILogger } from '@/infrastructure/logging';

// Validation result types
export interface ValidationResult<T> {
    success: boolean;
    data?: T;
    errors?: ValidationError[];
}

export interface ValidationError {
    field: string;
    message: string;
    code: string;
}

// Valy configuration
export interface ValyConfig {
    logger?: ILogger | null;
    throwOnError?: boolean;
    customErrorMessages?: Record<string, string>;
}

// Main Valy class
export class Valy {
    private static instance: Valy;
    private config: ValyConfig;
    private logger: ILogger | null;

    private constructor(config: ValyConfig = {}) {
        this.config = {
            throwOnError: false,
            customErrorMessages: {},
            ...config,
        };
        this.logger = this.config.logger || null;

        this.logger?.info('Valy validation library initialized', { config: this.config });
    }

    public static getInstance(config?: ValyConfig): Valy {
        if (!Valy.instance) {
            Valy.instance = new Valy(config);
        } else if (config) {
            Valy.instance.updateConfig(config);
        }
        return Valy.instance;
    }

    public updateConfig(newConfig: Partial<ValyConfig>): void {
        this.config = { ...this.config, ...newConfig };
        this.logger = this.config.logger || null;
        this.logger?.info('Valy config updated', { config: this.config });
    }

    // Main validation method
    public validate<T>(schema: ZodSchema<T>, data: unknown): ValidationResult<T> {
        try {
            const result = schema.parse(data);
            this.logger?.debug('Validation successful', { data: result });
            return {
                success: true,
                data: result,
            };
        } catch (error) {
            const validationErrors = this.formatZodErrors(error as ZodError);
            this.logger?.warn('Validation failed', { errors: validationErrors, data });

            if (this.config.throwOnError) {
                throw new ValidationException(validationErrors);
            }

            return {
                success: false,
                errors: validationErrors,
            };
        }
    }

    // Safe parse that never throws
    public safeParse<T>(schema: ZodSchema<T>, data: unknown): ValidationResult<T> {
        const result = schema.safeParse(data);

        if (result.success) {
            this.logger?.debug('Safe validation successful', { data: result.data });
            return {
                success: true,
                data: result.data,
            };
        }

        const validationErrors = this.formatZodErrors(result.error);
        this.logger?.warn('Safe validation failed', { errors: validationErrors, data });

        return {
            success: false,
            errors: validationErrors,
        };
    }

    // Validate and return data or throw
    public validateOrThrow<T>(schema: ZodSchema<T>, data: unknown): T {
        const result = this.validate(schema, data);
        if (!result.success) {
            throw new ValidationException(result.errors || []);
        }
        return result.data!;
    }

    // Validate multiple schemas
    public validateMultiple(
        validations: Array<{ schema: ZodSchema; data: unknown; name: string }>,
    ): Record<string, ValidationResult<unknown>> {
        const results: Record<string, ValidationResult<unknown>> = {};

        for (const { schema, data, name } of validations) {
            results[name] = this.validate(schema, data);
        }

        this.logger?.debug('Multiple validation completed', { results });
        return results;
    }

    // Check if all validations passed
    public allValid(results: Record<string, ValidationResult<unknown>>): boolean {
        return Object.values(results).every((result) => result.success);
    }

    // Get all errors from multiple validations
    public getAllErrors(results: Record<string, ValidationResult<unknown>>): ValidationError[] {
        const allErrors: ValidationError[] = [];
        Object.entries(results).forEach(([name, result]) => {
            if (!result.success && result.errors) {
                allErrors.push(
                    ...result.errors.map((error) => ({
                        ...error,
                        field: `${name}.${error.field}`,
                    })),
                );
            }
        });
        return allErrors;
    }

    // Format Zod errors to our ValidationError format
    private formatZodErrors(zodError: ZodError): ValidationError[] {
        return zodError.issues.map((error) => {
            const field = error.path.join('.');
            const code = error.code;
            let message = error.message;

            // Apply custom error messages if available
            const customKey = `${field}.${code}`;
            if (this.config.customErrorMessages?.[customKey]) {
                message = this.config.customErrorMessages[customKey];
            } else if (this.config.customErrorMessages?.[code]) {
                message = this.config.customErrorMessages[code];
            }

            return {
                field,
                message,
                code,
            };
        });
    }

    // Utility methods for common validations
    public isEmail(email: string): boolean {
        return z.string().email().safeParse(email).success;
    }

    public isUrl(url: string): boolean {
        return z.string().url().safeParse(url).success;
    }

    public isUuid(uuid: string): boolean {
        return z.string().uuid().safeParse(uuid).success;
    }

    public isPhoneNumber(phone: string): boolean {
        // Basic phone number validation - can be customized
        const phoneRegex = /^[+]?[1-9]\d{1,14}$/;
        return z.string().regex(phoneRegex).safeParse(phone).success;
    }
}

// Custom validation exception
export class ValidationException extends Error {
    public errors: ValidationError[];

    constructor(errors: ValidationError[]) {
        const message = `Validation failed: ${errors.map((e) => `${e.field}: ${e.message}`).join(', ')}`;
        super(message);
        this.name = 'ValidationException';
        this.errors = errors;
    }
}

// Export commonly used Zod schemas and utilities
export { z };
export type { ZodSchema, ZodError };

// Default instance
export const valy = Valy.getInstance();
