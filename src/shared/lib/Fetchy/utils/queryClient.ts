import { QueryClient } from '@tanstack/react-query';

import type { ILogger } from '@/infrastructure/logging';
import type { FetchyConfig } from '../fetchy.types';

export class QueryClientWrapper {
    private queryClient: QueryClient;
    private logger: ILogger | null;

    constructor(config: FetchyConfig, logger: ILogger | null = null) {
        this.logger = logger;
        this.queryClient = new QueryClient({
            defaultOptions: {
                queries: {
                    staleTime: 5 * 60 * 1000, // 5 minutes
                    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
                    retry: config.retries,
                    retryDelay: config.retryDelay,
                    refetchOnWindowFocus: false,
                },
                mutations: {
                    retry: config.retries,
                    retryDelay: config.retryDelay,
                },
            },
        });

        this.logger?.info('QueryClient initialized', {
            staleTime: 5 * 60 * 1000,
            gcTime: 10 * 60 * 1000,
            retries: config.retries,
            retryDelay: config.retryDelay,
        });
    }

    public getClient(): QueryClient {
        return this.queryClient;
    }

    public invalidateQueries(queryKey?: string[]): Promise<void> {
        this.logger?.info('Invalidating queries', { queryKey });
        return this.queryClient.invalidateQueries({ queryKey });
    }

    public removeQueries(queryKey?: string[]): void {
        this.logger?.info('Removing queries', { queryKey });
        this.queryClient.removeQueries({ queryKey });
    }

    public clear(): void {
        this.logger?.info('Clearing all queries');
        this.queryClient.clear();
    }

    public prefetchQuery<T>(queryKey: string[], queryFn: () => Promise<T>, staleTime?: number): Promise<void> {
        this.logger?.info('Prefetching query', { queryKey });
        return this.queryClient.prefetchQuery({
            queryKey,
            queryFn,
            staleTime,
        });
    }
}
