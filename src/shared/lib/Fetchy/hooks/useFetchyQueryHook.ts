import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import type { UseMutationOptions, UseQueryOptions } from '@tanstack/react-query';

import type { FetchyOptions, FetchyResponse } from '../fetchy.types';
import { Fetchy } from '../fetchy';

type FetchyQueryOptions<T> = Omit<UseQueryOptions<FetchyResponse<T>>, 'queryKey' | 'queryFn'>;
type FetchyMutationOptions<T, V> = Omit<UseMutationOptions<FetchyResponse<T>, Error, V>, 'mutationFn'>;

// GET query hook
export function useFetchyQuery<T>(
    queryKey: string[],
    endpoint: string,
    options?: Omit<FetchyOptions, 'method' | 'body'> & { params?: Record<string, unknown> },
    queryOptions?: FetchyQueryOptions<T>,
) {
    const fetchy = Fetchy.getInstance();

    return useQuery({
        queryKey,
        queryFn: () => fetchy.get<T>(endpoint, options),
        ...queryOptions,
    });
}

// POST mutation hook
export function useFetchyPostMutation<T, V = unknown>(endpoint: string, mutationOptions?: FetchyMutationOptions<T, V>) {
    const fetchy = Fetchy.getInstance();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (variables: V) => {
            const options: FetchyOptions = {
                body: variables,
            };
            return fetchy.post<T>(endpoint, options);
        },
        onSuccess: (data, variables, context) => {
            // Invalidate and refetch queries after successful mutation
            queryClient.invalidateQueries();
            mutationOptions?.onSuccess?.(data, variables, context);
        },
        ...mutationOptions,
    });
}

// PUT mutation hook
export function useFetchyPutMutation<T, V = unknown>(endpoint: string, mutationOptions?: FetchyMutationOptions<T, V>) {
    const fetchy = Fetchy.getInstance();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (variables: V) => {
            const options: FetchyOptions = {
                body: variables,
            };
            return fetchy.put<T>(endpoint, options);
        },
        onSuccess: (data, variables, context) => {
            queryClient.invalidateQueries();
            mutationOptions?.onSuccess?.(data, variables, context);
        },
        ...mutationOptions,
    });
}

// PATCH mutation hook
export function useFetchyPatchMutation<T, V = unknown>(
    endpoint: string,
    mutationOptions?: FetchyMutationOptions<T, V>,
) {
    const fetchy = Fetchy.getInstance();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: (variables: V) => {
            const options: FetchyOptions = {
                body: variables,
            };
            return fetchy.patch<T>(endpoint, options);
        },
        onSuccess: (data, variables, context) => {
            queryClient.invalidateQueries();
            mutationOptions?.onSuccess?.(data, variables, context);
        },
        ...mutationOptions,
    });
}

// DELETE mutation hook
export function useFetchyDeleteMutation<T>(endpoint: string, mutationOptions?: FetchyMutationOptions<T, void>) {
    const fetchy = Fetchy.getInstance();
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: () => fetchy.delete<T>(endpoint),
        onSuccess: (data, variables, context) => {
            queryClient.invalidateQueries();
            mutationOptions?.onSuccess?.(data, variables, context);
        },
        ...mutationOptions,
    });
}

// Generic mutation hook for custom operations
export function useFetchyMutation<T, V = unknown>(
    mutationFn: (variables: V) => Promise<FetchyResponse<T>>,
    mutationOptions?: FetchyMutationOptions<T, V>,
) {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn,
        onSuccess: (data, variables, context) => {
            queryClient.invalidateQueries();
            mutationOptions?.onSuccess?.(data, variables, context);
        },
        ...mutationOptions,
    });
}
