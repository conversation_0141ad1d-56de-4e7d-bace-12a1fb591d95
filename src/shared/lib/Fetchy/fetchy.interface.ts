import type { FetchyConfig, FetchyOptions, FetchyResponse } from './fetchy.types';
import type { QueryClientWrapper } from './utils/queryClient';

// Main Fetchy interface
export interface IFetchy {
    queryClient: QueryClientWrapper;
    // HTTP methods
    get<T>(
        endpoint: string,
        options?: Omit<FetchyOptions, 'method' | 'body'> & { params?: Record<string, unknown> },
    ): Promise<FetchyResponse<T>>;
    post<T>(endpoint: string, options?: Omit<FetchyOptions, 'method'>): Promise<FetchyResponse<T>>;
    put<T>(endpoint: string, options?: Omit<FetchyOptions, 'method'>): Promise<FetchyResponse<T>>;
    patch<T>(endpoint: string, options?: Omit<FetchyOptions, 'method'>): Promise<FetchyResponse<T>>;
    delete<T>(endpoint: string, options?: Omit<FetchyOptions, 'method' | 'body'>): Promise<FetchyResponse<T>>;

    // Configuration methods
    updateConfig(newConfig: Partial<FetchyConfig>): void;
    getConfig(): FetchyConfig;
}
