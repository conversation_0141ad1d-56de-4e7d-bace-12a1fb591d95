// Main Fetchy class and interface
export { Fetchy } from './fetchy';
export type { IFetchy } from './fetchy.interface';

// Types
export type {
    FetchyConfig,
    FetchyOptions,
    FetchyResponse,
    ApiError,
    UploadOptions,
    HttpMethod,
    onInterceptorFulfilled,
    onInterceptorRejected,
} from './fetchy.types';

// Utilities
export { DEFAULT_CONFIG } from './utils/defaultOptions';
export { QueryClientWrapper } from './utils/queryClient';

// React Query hooks
export {
    useFetchyQuery,
    useFetchyPostMutation,
    useFetchyPutMutation,
    useFetchyPatchMutation,
    useFetchyDeleteMutation,
    useFetchyMutation,
} from './hooks/useFetchyQueryHook';
