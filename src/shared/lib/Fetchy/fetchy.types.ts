import type { AxiosError, InternalAxiosRequestConfig } from 'axios';

import type { ILogger } from '@/infrastructure/logging';

export interface FetchyConfig {
    baseURL: string;
    timeout: number;
    retries: number;
    retryDelay: number;
    headers?: Record<string, string>;
    Logger?: ILogger | null;
}

export interface ApiError {
    message: string;
    code: string;
    details?: unknown;
}

export interface FetchyResponse<T> {
    data: T;
    status: number;
    statusText: string;
    headers: Record<string, string>;
}

export interface FetchyOptions {
    method?: HttpMethod;
    headers?: Record<string, string>;
    body?: unknown;
    params?: Record<string, unknown>;
    timeout?: number;
    onUploadProgress?: (progress: number) => void;
}

export interface UploadOptions extends Omit<FetchyOptions, 'body'> {
    files: File | File[];
    fields?: Record<string, string>;
    onUploadProgress?: (progress: number) => void;
}

export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

export type RequestInterceptor = (config: RequestInit & { url: string }) => Promise<typeof config> | typeof config;
export type ResponseInterceptor = (response: Response) => Promise<Response> | Response;
export type ErrorInterceptor = (error: unknown) => void;

export type onInterceptorFulfilled = (
    config: InternalAxiosRequestConfig,
) => InternalAxiosRequestConfig | Promise<InternalAxiosRequestConfig>;
export type onInterceptorRejected = (error: AxiosError) => AxiosError | Promise<AxiosError>;
