import type { AxiosError, AxiosResponse } from 'axios';
import axios, { type AxiosInstance } from 'axios';

import type { ILogger } from '@/infrastructure/logging';

import type {
    FetchyConfig,
    FetchyOptions,
    FetchyResponse,
    onInterceptorFulfilled,
    onInterceptorRejected,
} from './fetchy.types';
import type { IFetchy } from './fetchy.interface';
import { DEFAULT_CONFIG } from './utils/defaultOptions';
import { QueryClientWrapper } from './utils/queryClient';

export class Fetchy implements IFetchy {
    private static instance: Fetchy;
    private config: FetchyConfig;
    public queryClient: QueryClientWrapper;
    private logger: ILogger | null;
    private httpClient: AxiosInstance;

    private constructor(config: Partial<FetchyConfig> = {}) {
        this.config = { ...DEFAULT_CONFIG, ...config };
        this.logger = this.config.Logger || null;
        this.queryClient = new QueryClientWrapper(this.config, this.logger);

        this.httpClient = axios.create({
            baseURL: this.config.baseURL,
            timeout: this.config.timeout,
            headers: this.config.headers,
        });

        this.logger?.info('Fetchy initialized', { config: this.config });
    }

    public static getInstance(config?: Partial<FetchyConfig>): Fetchy {
        if (!Fetchy.instance) {
            Fetchy.instance = new Fetchy(config);
        } else if (config) {
            // Allow reconfiguration of existing instance
            Fetchy.instance.updateConfig(config);
        }
        return Fetchy.instance;
    }

    public updateConfig(newConfig: Partial<FetchyConfig>): void {
        this.config = { ...this.config, ...newConfig };
        this.logger = this.config.Logger || null;

        // Update axios instance
        this.httpClient.defaults.baseURL = this.config.baseURL;
        this.httpClient.defaults.timeout = this.config.timeout;
        if (this.config.headers) {
            Object.assign(this.httpClient.defaults.headers.common, this.config.headers);
        }

        this.logger?.info('Fetchy config updated', { config: this.config });
    }

    private async makeRequest<T>(endpoint: string, options: FetchyOptions = {}): Promise<FetchyResponse<T>> {
        const { method = 'GET', headers, body, params, timeout } = options;

        try {
            this.logger?.logApiRequest(method, endpoint, body);
            const startTime = Date.now();

            const response = await this.httpClient.request({
                url: endpoint,
                method,
                params,
                headers,
                data: body,
                timeout: timeout || this.config.timeout,
            });

            const duration = Date.now() - startTime;
            this.logger?.logApiResponse(method, endpoint, response.status, duration);

            return {
                data: response.data,
                status: response.status,
                statusText: response.statusText,
                headers: response.headers as Record<string, string>,
            };
        } catch (error) {
            this.logger?.error(`API request failed: ${method} ${endpoint}`, error as Error);
            throw error;
        }
    }

    // Public HTTP methods
    public async get<T>(
        endpoint: string,
        options?: Omit<FetchyOptions, 'method' | 'body'> & { params?: Record<string, unknown> },
    ): Promise<FetchyResponse<T>> {
        return this.makeRequest<T>(endpoint, { ...options, method: 'GET', params: options?.params });
    }

    public async post<T>(endpoint: string, options?: Omit<FetchyOptions, 'method'>): Promise<FetchyResponse<T>> {
        return this.makeRequest<T>(endpoint, { ...options, method: 'POST', body: options?.body });
    }

    public async put<T>(endpoint: string, options?: Omit<FetchyOptions, 'method'>): Promise<FetchyResponse<T>> {
        return this.makeRequest<T>(endpoint, { ...options, method: 'PUT', body: options?.body });
    }

    public async patch<T>(endpoint: string, options?: Omit<FetchyOptions, 'method'>): Promise<FetchyResponse<T>> {
        return this.makeRequest<T>(endpoint, { ...options, method: 'PATCH', body: options?.body });
    }

    public async delete<T>(
        endpoint: string,
        options?: Omit<FetchyOptions, 'method' | 'body'>,
    ): Promise<FetchyResponse<T>> {
        return this.makeRequest<T>(endpoint, { ...options, method: 'DELETE' });
    }

    // Utility methods
    public setLocaleHeader(locale: string): void {
        this.httpClient.defaults.headers.common['Accept-Language'] = locale;
    }

    public setAuthHeader(token: string): void {
        this.httpClient.defaults.headers.common['Authorization'] = token;
    }

    public addRequestInterceptor(onFulfilled?: onInterceptorFulfilled, onRejected?: onInterceptorRejected): number {
        return this.httpClient.interceptors.request.use(onFulfilled, onRejected);
    }

    public addResponseInterceptor(
        onFulfilled?: (response: AxiosResponse) => AxiosResponse | Promise<AxiosResponse>,
        onRejected?: (error: AxiosError) => Promise<AxiosError>,
    ): number {
        return this.httpClient.interceptors.response.use(onFulfilled, onRejected);
    }

    public getConfig(): FetchyConfig {
        return { ...this.config };
    }
}
