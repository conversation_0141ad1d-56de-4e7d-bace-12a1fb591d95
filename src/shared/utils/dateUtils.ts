// Date formatting and manipulation utilities

/**
 * Format a date to a readable string
 */
export function formatDate(
    date: Date | string | number,
    options: Intl.DateTimeFormatOptions = {},
    locale = 'en-US',
): string {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
        return 'Invalid Date';
    }

    const defaultOptions: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        ...options,
    };

    return new Intl.DateTimeFormat(locale, defaultOptions).format(dateObj);
}

/**
 * Format a date to ISO string
 */
export function formatDateISO(date: Date | string | number): string {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
        throw new Error('Invalid date');
    }
    return dateObj.toISOString();
}

/**
 * Format time to readable string
 */
export function formatTime(
    date: Date | string | number,
    options: Intl.DateTimeFormatOptions = {},
    locale = 'en-US',
): string {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
        return 'Invalid Time';
    }

    const defaultOptions: Intl.DateTimeFormatOptions = {
        hour: '2-digit',
        minute: '2-digit',
        ...options,
    };

    return new Intl.DateTimeFormat(locale, defaultOptions).format(dateObj);
}

/**
 * Format date and time together
 */
export function formatDateTime(
    date: Date | string | number,
    options: Intl.DateTimeFormatOptions = {},
    locale = 'en-US',
): string {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
        return 'Invalid DateTime';
    }

    const defaultOptions: Intl.DateTimeFormatOptions = {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        ...options,
    };

    return new Intl.DateTimeFormat(locale, defaultOptions).format(dateObj);
}

/**
 * Get relative time string (e.g., "2 hours ago", "in 3 days")
 */
export function getRelativeTime(date: Date | string | number, locale = 'en-US'): string {
    const dateObj = new Date(date);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });

    const intervals = [
        { label: 'year', seconds: 31536000 },
        { label: 'month', seconds: 2592000 },
        { label: 'week', seconds: 604800 },
        { label: 'day', seconds: 86400 },
        { label: 'hour', seconds: 3600 },
        { label: 'minute', seconds: 60 },
        { label: 'second', seconds: 1 },
    ] as const;

    for (const interval of intervals) {
        const count = Math.floor(Math.abs(diffInSeconds) / interval.seconds);
        if (count >= 1) {
            return rtf.format(diffInSeconds > 0 ? -count : count, interval.label);
        }
    }

    return rtf.format(0, 'second');
}

/**
 * Check if a date is today
 */
export function isToday(date: Date | string | number): boolean {
    const dateObj = new Date(date);
    const today = new Date();
    return (
        dateObj.getDate() === today.getDate() &&
        dateObj.getMonth() === today.getMonth() &&
        dateObj.getFullYear() === today.getFullYear()
    );
}

/**
 * Check if a date is yesterday
 */
export function isYesterday(date: Date | string | number): boolean {
    const dateObj = new Date(date);
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    return (
        dateObj.getDate() === yesterday.getDate() &&
        dateObj.getMonth() === yesterday.getMonth() &&
        dateObj.getFullYear() === yesterday.getFullYear()
    );
}

/**
 * Check if a date is tomorrow
 */
export function isTomorrow(date: Date | string | number): boolean {
    const dateObj = new Date(date);
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return (
        dateObj.getDate() === tomorrow.getDate() &&
        dateObj.getMonth() === tomorrow.getMonth() &&
        dateObj.getFullYear() === tomorrow.getFullYear()
    );
}

/**
 * Get start of day
 */
export function startOfDay(date: Date | string | number): Date {
    const dateObj = new Date(date);
    dateObj.setHours(0, 0, 0, 0);
    return dateObj;
}

/**
 * Get end of day
 */
export function endOfDay(date: Date | string | number): Date {
    const dateObj = new Date(date);
    dateObj.setHours(23, 59, 59, 999);
    return dateObj;
}

/**
 * Add days to a date
 */
export function addDays(date: Date | string | number, days: number): Date {
    const dateObj = new Date(date);
    dateObj.setDate(dateObj.getDate() + days);
    return dateObj;
}

/**
 * Add hours to a date
 */
export function addHours(date: Date | string | number, hours: number): Date {
    const dateObj = new Date(date);
    dateObj.setHours(dateObj.getHours() + hours);
    return dateObj;
}

/**
 * Add minutes to a date
 */
export function addMinutes(date: Date | string | number, minutes: number): Date {
    const dateObj = new Date(date);
    dateObj.setMinutes(dateObj.getMinutes() + minutes);
    return dateObj;
}

/**
 * Get difference between two dates in days
 */
export function getDaysDifference(date1: Date | string | number, date2: Date | string | number): number {
    const dateObj1 = new Date(date1);
    const dateObj2 = new Date(date2);
    const diffTime = Math.abs(dateObj2.getTime() - dateObj1.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

/**
 * Get age from birth date
 */
export function getAge(birthDate: Date | string | number): number {
    const birth = new Date(birthDate);
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
    }

    return age;
}

/**
 * Check if a year is a leap year
 */
export function isLeapYear(year: number): boolean {
    return (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;
}

/**
 * Get days in a month
 */
export function getDaysInMonth(year: number, month: number): number {
    return new Date(year, month + 1, 0).getDate();
}

/**
 * Parse date string with fallback
 */
export function parseDate(dateString: string, fallback?: Date): Date | null {
    try {
        const parsed = new Date(dateString);
        if (isNaN(parsed.getTime())) {
            return fallback || null;
        }
        return parsed;
    } catch {
        return fallback || null;
    }
}

/**
 * Format duration in milliseconds to human readable string
 */
export function formatDuration(milliseconds: number): string {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
        return `${days}d ${hours % 24}h ${minutes % 60}m`;
    }
    if (hours > 0) {
        return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    }
    if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
    }
    return `${seconds}s`;
}
