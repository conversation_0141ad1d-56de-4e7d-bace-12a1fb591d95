// File manipulation and utility functions

/**
 * File size units
 */
const FILE_SIZE_UNITS = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'] as const;

/**
 * Common file types and their extensions
 */
export const FILE_TYPES = {
    IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'ico'],
    VIDEO: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', '3gp'],
    AUDIO: ['mp3', 'wav', 'ogg', 'aac', 'flac', 'wma', 'm4a'],
    DOCUMENT: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'rtf'],
    ARCHIVE: ['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz'],
    CODE: ['js', 'ts', 'jsx', 'tsx', 'html', 'css', 'scss', 'json', 'xml', 'py', 'java', 'cpp', 'c', 'php'],
} as const;

/**
 * MIME type mappings
 */
export const MIME_TYPES: Record<string, string> = {
    // Images
    jpg: 'image/jpeg',
    jpeg: 'image/jpeg',
    png: 'image/png',
    gif: 'image/gif',
    bmp: 'image/bmp',
    webp: 'image/webp',
    svg: 'image/svg+xml',
    ico: 'image/x-icon',

    // Videos
    mp4: 'video/mp4',
    avi: 'video/x-msvideo',
    mov: 'video/quicktime',
    wmv: 'video/x-ms-wmv',
    flv: 'video/x-flv',
    webm: 'video/webm',
    mkv: 'video/x-matroska',

    // Audio
    mp3: 'audio/mpeg',
    wav: 'audio/wav',
    ogg: 'audio/ogg',
    aac: 'audio/aac',
    flac: 'audio/flac',
    wma: 'audio/x-ms-wma',
    m4a: 'audio/mp4',

    // Documents
    pdf: 'application/pdf',
    doc: 'application/msword',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    xls: 'application/vnd.ms-excel',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ppt: 'application/vnd.ms-powerpoint',
    pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    txt: 'text/plain',
    rtf: 'application/rtf',

    // Archives
    zip: 'application/zip',
    rar: 'application/vnd.rar',
    '7z': 'application/x-7z-compressed',
    tar: 'application/x-tar',
    gz: 'application/gzip',

    // Code
    js: 'text/javascript',
    ts: 'text/typescript',
    jsx: 'text/jsx',
    tsx: 'text/tsx',
    html: 'text/html',
    css: 'text/css',
    scss: 'text/scss',
    json: 'application/json',
    xml: 'application/xml',
    py: 'text/x-python',
    java: 'text/x-java-source',
    cpp: 'text/x-c++src',
    c: 'text/x-csrc',
    php: 'text/x-php',
};

/**
 * Get file extension from filename
 */
export function getFileExtension(filename: string): string | null {
    const lastDot = filename.lastIndexOf('.');
    if (lastDot === -1 || lastDot === filename.length - 1) {
        return null;
    }
    return filename.slice(lastDot + 1).toLowerCase();
}

/**
 * Get filename without extension
 */
export function getFileNameWithoutExtension(filename: string): string {
    const lastDot = filename.lastIndexOf('.');
    if (lastDot === -1) {
        return filename;
    }
    return filename.slice(0, lastDot);
}

/**
 * Get MIME type from file extension
 */
export function getMimeType(filename: string): string | null {
    const extension = getFileExtension(filename);
    if (!extension) return null;
    return MIME_TYPES[extension] || null;
}

/**
 * Check if file is of specific type
 */
export function isFileType(filename: string, type: keyof typeof FILE_TYPES): boolean {
    const extension = getFileExtension(filename);
    if (!extension) return false;
    return FILE_TYPES[type].includes(extension as never);
}

/**
 * Check if file is an image
 */
export function isImage(filename: string): boolean {
    return isFileType(filename, 'IMAGE');
}

/**
 * Check if file is a video
 */
export function isVideo(filename: string): boolean {
    return isFileType(filename, 'VIDEO');
}

/**
 * Check if file is an audio file
 */
export function isAudio(filename: string): boolean {
    return isFileType(filename, 'AUDIO');
}

/**
 * Check if file is a document
 */
export function isDocument(filename: string): boolean {
    return isFileType(filename, 'DOCUMENT');
}

/**
 * Check if file is an archive
 */
export function isArchive(filename: string): boolean {
    return isFileType(filename, 'ARCHIVE');
}

/**
 * Check if file is a code file
 */
export function isCode(filename: string): boolean {
    return isFileType(filename, 'CODE');
}

/**
 * Format file size in bytes to human readable format
 */
export function formatFileSize(bytes: number, decimals = 2): string {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;

    const i = Math.floor(Math.log(bytes) / Math.log(k));
    const size = parseFloat((bytes / Math.pow(k, i)).toFixed(dm));

    return `${size} ${FILE_SIZE_UNITS[i]}`;
}

/**
 * Parse file size string to bytes
 */
export function parseFileSize(sizeStr: string): number | null {
    const match = sizeStr.match(/^([0-9.]+)\s*(B|KB|MB|GB|TB|PB)$/i);
    if (!match) return null;

    const [, value, unit] = match;
    const bytes = parseFloat(value);
    const unitIndex = FILE_SIZE_UNITS.findIndex((u) => u.toLowerCase() === unit.toLowerCase());

    if (unitIndex === -1) return null;

    return bytes * Math.pow(1024, unitIndex);
}

/**
 * Validate file size against maximum allowed
 */
export function validateFileSize(fileSize: number, maxSize: number): { valid: boolean; message?: string } {
    if (fileSize > maxSize) {
        return {
            valid: false,
            message: `File size (${formatFileSize(fileSize)}) exceeds maximum allowed size (${formatFileSize(maxSize)})`,
        };
    }

    return { valid: true };
}

/**
 * Validate file type against allowed extensions
 */
export function validateFileType(filename: string, allowedExtensions: string[]): { valid: boolean; message?: string } {
    const extension = getFileExtension(filename);

    if (!extension) {
        return {
            valid: false,
            message: 'File has no extension',
        };
    }

    const normalizedAllowed = allowedExtensions.map((ext) => ext.toLowerCase());

    if (!normalizedAllowed.includes(extension)) {
        return {
            valid: false,
            message: `File type .${extension} is not allowed. Allowed types: ${allowedExtensions.join(', ')}`,
        };
    }

    return { valid: true };
}

/**
 * Generate unique filename by adding timestamp or counter
 */
export function generateUniqueFilename(filename: string, existingFiles: string[] = [], useTimestamp = false): string {
    const extension = getFileExtension(filename);
    const nameWithoutExt = getFileNameWithoutExtension(filename);

    if (!existingFiles.includes(filename)) {
        return filename;
    }

    if (useTimestamp) {
        const timestamp = Date.now();
        return extension ? `${nameWithoutExt}_${timestamp}.${extension}` : `${nameWithoutExt}_${timestamp}`;
    }

    let counter = 1;
    let newFilename: string;

    do {
        newFilename = extension ? `${nameWithoutExt}_${counter}.${extension}` : `${nameWithoutExt}_${counter}`;
        counter++;
    } while (existingFiles.includes(newFilename));

    return newFilename;
}

/**
 * Sanitize filename by removing invalid characters
 */
export function sanitizeFilename(filename: string): string {
    // Remove or replace invalid characters
    return filename
        .replace(/[<>:"/\\|?*]/g, '_') // Replace invalid chars with underscore
        .replace(/\s+/g, '_') // Replace spaces with underscore
        .replace(/_{2,}/g, '_') // Replace multiple underscores with single
        .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
        .slice(0, 255); // Limit length to 255 characters
}

/**
 * Convert File object to base64 string
 */
export function fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = () => {
            const result = reader.result as string;
            resolve(result);
        };

        reader.onerror = () => {
            reject(new Error('Failed to read file'));
        };

        reader.readAsDataURL(file);
    });
}

/**
 * Convert base64 string to File object
 */
export function base64ToFile(base64: string, filename: string, mimeType?: string): File {
    // Extract MIME type from base64 if not provided
    if (!mimeType) {
        const match = base64.match(/^data:([^;]+);base64,/);
        mimeType = match ? match[1] : 'application/octet-stream';
    }

    // Remove data URL prefix if present
    const base64Data = base64.replace(/^data:[^;]+;base64,/, '');

    // Convert base64 to binary
    const binaryString = atob(base64Data);
    const bytes = new Uint8Array(binaryString.length);

    for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
    }

    return new File([bytes], filename, { type: mimeType });
}

/**
 * Read file as text
 */
export function readFileAsText(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = () => {
            resolve(reader.result as string);
        };

        reader.onerror = () => {
            reject(new Error('Failed to read file as text'));
        };

        reader.readAsText(file);
    });
}

/**
 * Read file as array buffer
 */
export function readFileAsArrayBuffer(file: File): Promise<ArrayBuffer> {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();

        reader.onload = () => {
            resolve(reader.result as ArrayBuffer);
        };

        reader.onerror = () => {
            reject(new Error('Failed to read file as array buffer'));
        };

        reader.readAsArrayBuffer(file);
    });
}

/**
 * Create download link for file
 */
export function downloadFile(data: Blob | string, filename: string, mimeType = 'application/octet-stream'): void {
    const blob = typeof data === 'string' ? new Blob([data], { type: mimeType }) : data;

    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');

    link.href = url;
    link.download = filename;
    link.style.display = 'none';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Clean up the URL object
    URL.revokeObjectURL(url);
}

/**
 * Get file info from File object
 */
export function getFileInfo(file: File): {
    name: string;
    size: number;
    formattedSize: string;
    type: string;
    extension: string | null;
    lastModified: Date;
    isImage: boolean;
    isVideo: boolean;
    isAudio: boolean;
    isDocument: boolean;
    isArchive: boolean;
    isCode: boolean;
} {
    const extension = getFileExtension(file.name);

    return {
        name: file.name,
        size: file.size,
        formattedSize: formatFileSize(file.size),
        type: file.type,
        extension,
        lastModified: new Date(file.lastModified),
        isImage: isImage(file.name),
        isVideo: isVideo(file.name),
        isAudio: isAudio(file.name),
        isDocument: isDocument(file.name),
        isArchive: isArchive(file.name),
        isCode: isCode(file.name),
    };
}

/**
 * Compare two files for equality
 */
export async function compareFiles(file1: File, file2: File): Promise<boolean> {
    if (file1.size !== file2.size) {
        return false;
    }

    if (file1.name !== file2.name) {
        return false;
    }

    if (file1.lastModified !== file2.lastModified) {
        return false;
    }

    // For small files, compare content
    if (file1.size < 1024 * 1024) {
        // 1MB
        try {
            const [buffer1, buffer2] = await Promise.all([readFileAsArrayBuffer(file1), readFileAsArrayBuffer(file2)]);

            const array1 = new Uint8Array(buffer1);
            const array2 = new Uint8Array(buffer2);

            return array1.every((byte, index) => byte === array2[index]);
        } catch {
            return false;
        }
    }

    return true;
}

/**
 * Create file from URL
 */
export async function createFileFromUrl(url: string, filename?: string): Promise<File> {
    const response = await fetch(url);
    const blob = await response.blob();

    const finalFilename = filename || url.split('/').pop() || 'download';

    return new File([blob], finalFilename, {
        type: blob.type || 'application/octet-stream',
    });
}
