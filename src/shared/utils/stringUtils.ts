// String manipulation and formatting utilities

/**
 * Capitalize the first letter of a string
 */
export function capitalize(str: string): string {
    if (!str) return str;
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

/**
 * Capitalize the first letter of each word
 */
export function capitalizeWords(str: string): string {
    if (!str) return str;
    return str
        .split(' ')
        .map((word) => capitalize(word))
        .join(' ');
}

/**
 * Convert string to camelCase
 */
export function toCamelCase(str: string): string {
    return str
        .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
            return index === 0 ? word.toLowerCase() : word.toUpperCase();
        })
        .replace(/\s+/g, '');
}

/**
 * Convert string to PascalCase
 */
export function toPascalCase(str: string): string {
    return str.replace(/(?:^\w|[A-Z]|\b\w)/g, (word) => word.toUpperCase()).replace(/\s+/g, '');
}

/**
 * Convert string to kebab-case
 */
export function toKebabCase(str: string): string {
    return str
        .replace(/([a-z])([A-Z])/g, '$1-$2')
        .replace(/[\s_]+/g, '-')
        .toLowerCase();
}

/**
 * Convert string to snake_case
 */
export function toSnakeCase(str: string): string {
    return str
        .replace(/([a-z])([A-Z])/g, '$1_$2')
        .replace(/[\s-]+/g, '_')
        .toLowerCase();
}

/**
 * Truncate string with ellipsis
 */
export function truncate(str: string, length: number, suffix = '...'): string {
    if (!str || str.length <= length) return str;
    return str.slice(0, length - suffix.length) + suffix;
}

/**
 * Truncate string by words
 */
export function truncateWords(str: string, wordCount: number, suffix = '...'): string {
    if (!str) return str;
    const words = str.split(' ');
    if (words.length <= wordCount) return str;
    return words.slice(0, wordCount).join(' ') + suffix;
}

/**
 * Remove HTML tags from string
 */
export function stripHtml(str: string): string {
    return str.replace(/<[^>]*>/g, '');
}

/**
 * Escape HTML characters
 */
export function escapeHtml(str: string): string {
    const htmlEscapes: Record<string, string> = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#39;',
    };

    return str.replace(/[&<>"']/g, (match) => htmlEscapes[match]);
}

/**
 * Unescape HTML characters
 */
export function unescapeHtml(str: string): string {
    const htmlUnescapes: Record<string, string> = {
        '&amp;': '&',
        '&lt;': '<',
        '&gt;': '>',
        '&quot;': '"',
        '&#39;': "'",
    };

    return str.replace(/&(?:amp|lt|gt|quot|#39);/g, (match) => htmlUnescapes[match]);
}

/**
 * Generate a random string
 */
export function generateRandomString(
    length: number,
    charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789',
): string {
    let result = '';
    for (let i = 0; i < length; i++) {
        result += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return result;
}

/**
 * Generate a UUID v4
 */
export function generateUUID(): string {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        const r = (Math.random() * 16) | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });
}

/**
 * Slugify a string for URLs
 */
export function slugify(str: string): string {
    return str
        .toLowerCase()
        .trim()
        .replace(/[^\w\s-]/g, '') // Remove special characters
        .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
        .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
}

/**
 * Check if string is empty or whitespace
 */
export function isEmpty(str: string | null | undefined): boolean {
    return !str || str.trim().length === 0;
}

/**
 * Check if string is not empty
 */
export function isNotEmpty(str: string | null | undefined): str is string {
    return !isEmpty(str);
}

/**
 * Pad string with zeros
 */
export function padZero(num: number | string, length: number): string {
    return String(num).padStart(length, '0');
}

/**
 * Format file size in bytes to human readable
 */
export function formatFileSize(bytes: number, decimals = 2): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Extract initials from a name
 */
export function getInitials(name: string, maxLength = 2): string {
    if (!name) return '';

    return name
        .split(' ')
        .filter((word) => word.length > 0)
        .map((word) => word.charAt(0).toUpperCase())
        .slice(0, maxLength)
        .join('');
}

/**
 * Mask sensitive information (e.g., email, phone)
 */
export function maskString(str: string, visibleStart = 2, visibleEnd = 2, maskChar = '*'): string {
    if (!str || str.length <= visibleStart + visibleEnd) {
        return str;
    }

    const start = str.slice(0, visibleStart);
    const end = str.slice(-visibleEnd);
    const middle = maskChar.repeat(str.length - visibleStart - visibleEnd);

    return start + middle + end;
}

/**
 * Mask email address
 */
export function maskEmail(email: string): string {
    if (!email || !email.includes('@')) return email;

    const [username, domain] = email.split('@');
    const maskedUsername = maskString(username, 1, 1);

    return `${maskedUsername}@${domain}`;
}

/**
 * Mask phone number
 */
export function maskPhone(phone: string): string {
    if (!phone) return phone;

    // Remove all non-digit characters for processing
    const digits = phone.replace(/\D/g, '');

    if (digits.length < 4) return phone;

    // Keep original formatting but mask middle digits
    return phone.replace(/\d/g, (digit, index) => {
        const digitIndex = phone.slice(0, index + 1).replace(/\D/g, '').length - 1;
        const totalDigits = digits.length;

        // Show first 2 and last 2 digits
        if (digitIndex < 2 || digitIndex >= totalDigits - 2) {
            return digit;
        }
        return '*';
    });
}

/**
 * Count words in a string
 */
export function countWords(str: string): number {
    if (!str) return 0;
    return str
        .trim()
        .split(/\s+/)
        .filter((word) => word.length > 0).length;
}

/**
 * Count characters excluding whitespace
 */
export function countCharacters(str: string, includeSpaces = true): number {
    if (!str) return 0;
    return includeSpaces ? str.length : str.replace(/\s/g, '').length;
}

/**
 * Remove extra whitespace and normalize
 */
export function normalizeWhitespace(str: string): string {
    return str.replace(/\s+/g, ' ').trim();
}

/**
 * Check if string contains only digits
 */
export function isNumeric(str: string): boolean {
    return /^\d+$/.test(str);
}

/**
 * Check if string is a valid email format
 */
export function isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Extract domain from email
 */
export function extractEmailDomain(email: string): string | null {
    if (!isValidEmail(email)) return null;
    return email.split('@')[1];
}

/**
 * Highlight search terms in text
 */
export function highlightText(text: string, searchTerm: string, highlightClass = 'highlight'): string {
    if (!searchTerm) return text;

    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, `<span class="${highlightClass}">$1</span>`);
}

/**
 * Convert string to boolean
 */
export function stringToBoolean(str: string): boolean {
    const truthyValues = ['true', '1', 'yes', 'on', 'enabled'];
    return truthyValues.includes(str.toLowerCase());
}

/**
 * Parse query string to object
 */
export function parseQueryString(queryString: string): Record<string, string> {
    const params: Record<string, string> = {};
    const searchParams = new URLSearchParams(queryString);

    Array.from(searchParams.entries()).forEach(([key, value]) => {
        params[key] = value;
    });

    return params;
}

/**
 * Convert object to query string
 */
export function objectToQueryString(obj: Record<string, unknown>): string {
    const params = new URLSearchParams();

    Object.entries(obj).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
            params.append(key, String(value));
        }
    });

    return params.toString();
}
