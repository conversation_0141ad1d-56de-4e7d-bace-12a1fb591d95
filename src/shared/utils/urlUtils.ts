// URL manipulation and utility functions

/**
 * Build URL with query parameters
 */
export function buildUrl(
    baseUrl: string,
    params?: Record<string, string | number | boolean | null | undefined>,
): string {
    if (!params) return baseUrl;

    const url = new URL(baseUrl);

    Object.entries(params).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
            url.searchParams.set(key, String(value));
        }
    });

    return url.toString();
}

/**
 * Parse URL and extract components
 */
export function parseUrl(url: string): {
    protocol: string;
    hostname: string;
    port: string;
    pathname: string;
    search: string;
    hash: string;
    params: Record<string, string>;
} {
    const urlObj = new URL(url);
    const params: Record<string, string> = {};

    urlObj.searchParams.forEach((value, key) => {
        params[key] = value;
    });

    return {
        protocol: urlObj.protocol,
        hostname: urlObj.hostname,
        port: urlObj.port,
        pathname: urlObj.pathname,
        search: urlObj.search,
        hash: urlObj.hash,
        params,
    };
}

/**
 * Get query parameters from URL string
 */
export function getQueryParams(url: string): Record<string, string> {
    const urlObj = new URL(url);
    const params: Record<string, string> = {};

    urlObj.searchParams.forEach((value, key) => {
        params[key] = value;
    });

    return params;
}

/**
 * Add query parameters to URL
 */
export function addQueryParams(url: string, params: Record<string, string | number | boolean>): string {
    const urlObj = new URL(url);

    Object.entries(params).forEach(([key, value]) => {
        urlObj.searchParams.set(key, String(value));
    });

    return urlObj.toString();
}

/**
 * Remove query parameters from URL
 */
export function removeQueryParams(url: string, keys: string[]): string {
    const urlObj = new URL(url);

    keys.forEach((key) => {
        urlObj.searchParams.delete(key);
    });

    return urlObj.toString();
}

/**
 * Update query parameters in URL
 */
export function updateQueryParams(url: string, params: Record<string, string | number | boolean | null>): string {
    const urlObj = new URL(url);

    Object.entries(params).forEach(([key, value]) => {
        if (value === null) {
            urlObj.searchParams.delete(key);
        } else {
            urlObj.searchParams.set(key, String(value));
        }
    });

    return urlObj.toString();
}

/**
 * Check if URL is valid
 */
export function isValidUrl(url: string): boolean {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
}

/**
 * Check if URL is absolute
 */
export function isAbsoluteUrl(url: string): boolean {
    try {
        const urlObj = new URL(url);
        return urlObj.protocol !== '';
    } catch {
        return false;
    }
}

/**
 * Check if URL is relative
 */
export function isRelativeUrl(url: string): boolean {
    return !isAbsoluteUrl(url);
}

/**
 * Join URL paths
 */
export function joinPaths(...paths: string[]): string {
    return paths
        .map((path, index) => {
            // Remove leading slash from all paths except the first
            if (index > 0 && path.startsWith('/')) {
                path = path.slice(1);
            }
            // Remove trailing slash from all paths except the last
            if (index < paths.length - 1 && path.endsWith('/')) {
                path = path.slice(0, -1);
            }
            return path;
        })
        .filter((path) => path.length > 0)
        .join('/');
}

/**
 * Get domain from URL
 */
export function getDomain(url: string): string | null {
    try {
        const urlObj = new URL(url);
        return urlObj.hostname;
    } catch {
        return null;
    }
}

/**
 * Get subdomain from URL
 */
export function getSubdomain(url: string): string | null {
    const domain = getDomain(url);
    if (!domain) return null;

    const parts = domain.split('.');
    if (parts.length <= 2) return null;

    return parts[0];
}

/**
 * Get root domain from URL (without subdomain)
 */
export function getRootDomain(url: string): string | null {
    const domain = getDomain(url);
    if (!domain) return null;

    const parts = domain.split('.');
    if (parts.length <= 2) return domain;

    return parts.slice(-2).join('.');
}

/**
 * Normalize URL (remove trailing slash, convert to lowercase, etc.)
 */
export function normalizeUrl(url: string): string {
    try {
        const urlObj = new URL(url.toLowerCase());

        // Remove trailing slash from pathname (except for root)
        if (urlObj.pathname !== '/' && urlObj.pathname.endsWith('/')) {
            urlObj.pathname = urlObj.pathname.slice(0, -1);
        }

        // Sort query parameters
        const params = Array.from(urlObj.searchParams.entries()).sort(([a], [b]) => a.localeCompare(b));

        urlObj.search = '';
        params.forEach(([key, value]) => {
            urlObj.searchParams.append(key, value);
        });

        return urlObj.toString();
    } catch {
        return url;
    }
}

/**
 * Extract file extension from URL
 */
export function getFileExtension(url: string): string | null {
    try {
        const urlObj = new URL(url);
        const pathname = urlObj.pathname;
        const lastDot = pathname.lastIndexOf('.');

        if (lastDot === -1 || lastDot === pathname.length - 1) {
            return null;
        }

        return pathname.slice(lastDot + 1).toLowerCase();
    } catch {
        return null;
    }
}

/**
 * Extract filename from URL
 */
export function getFilename(url: string): string | null {
    try {
        const urlObj = new URL(url);
        const pathname = urlObj.pathname;
        const lastSlash = pathname.lastIndexOf('/');

        if (lastSlash === -1) {
            return pathname || null;
        }

        const filename = pathname.slice(lastSlash + 1);
        return filename || null;
    } catch {
        return null;
    }
}

/**
 * Check if URLs are from the same origin
 */
export function isSameOrigin(url1: string, url2: string): boolean {
    try {
        const urlObj1 = new URL(url1);
        const urlObj2 = new URL(url2);

        return (
            urlObj1.protocol === urlObj2.protocol &&
            urlObj1.hostname === urlObj2.hostname &&
            urlObj1.port === urlObj2.port
        );
    } catch {
        return false;
    }
}

/**
 * Convert relative URL to absolute URL
 */
export function toAbsoluteUrl(relativeUrl: string, baseUrl: string): string {
    try {
        return new URL(relativeUrl, baseUrl).toString();
    } catch {
        return relativeUrl;
    }
}

/**
 * Encode URL component safely
 */
export function encodeUrlComponent(str: string): string {
    return encodeURIComponent(str).replace(/[!'()*]/g, (c) => `%${c.charCodeAt(0).toString(16).toUpperCase()}`);
}

/**
 * Decode URL component safely
 */
export function decodeUrlComponent(str: string): string {
    try {
        return decodeURIComponent(str);
    } catch {
        return str;
    }
}

/**
 * Build API endpoint URL
 */
export function buildApiUrl(
    baseUrl: string,
    endpoint: string,
    params?: Record<string, string | number | boolean>,
): string {
    const url = joinPaths(baseUrl, endpoint);
    return params ? addQueryParams(url, params) : url;
}

/**
 * Extract hash fragment from URL
 */
export function getHashFragment(url: string): string | null {
    try {
        const urlObj = new URL(url);
        return urlObj.hash ? urlObj.hash.slice(1) : null;
    } catch {
        return null;
    }
}

/**
 * Set hash fragment in URL
 */
export function setHashFragment(url: string, hash: string): string {
    try {
        const urlObj = new URL(url);
        urlObj.hash = hash.startsWith('#') ? hash : `#${hash}`;
        return urlObj.toString();
    } catch {
        return url;
    }
}

/**
 * Remove hash fragment from URL
 */
export function removeHashFragment(url: string): string {
    try {
        const urlObj = new URL(url);
        urlObj.hash = '';
        return urlObj.toString();
    } catch {
        return url;
    }
}

/**
 * Check if URL matches a pattern (supports wildcards)
 */
export function matchesPattern(url: string, pattern: string): boolean {
    try {
        const urlObj = new URL(url);
        const patternRegex = new RegExp(pattern.replace(/\./g, '\\.').replace(/\*/g, '.*').replace(/\?/g, '.'));

        return patternRegex.test(urlObj.href);
    } catch {
        return false;
    }
}

/**
 * Get URL without query parameters and hash
 */
export function getCleanUrl(url: string): string {
    try {
        const urlObj = new URL(url);
        return `${urlObj.protocol}//${urlObj.host}${urlObj.pathname}`;
    } catch {
        return url;
    }
}
