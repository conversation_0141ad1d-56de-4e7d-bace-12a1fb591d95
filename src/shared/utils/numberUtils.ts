// Number formatting and manipulation utilities

/**
 * Format number with thousands separator
 */
export function formatNumber(num: number, options: Intl.NumberFormatOptions = {}, locale = 'en-US'): string {
    return new Intl.NumberFormat(locale, options).format(num);
}

/**
 * Format number as currency
 */
export function formatCurrency(amount: number, currency = 'USD', locale = 'en-US'): string {
    return new Intl.NumberFormat(locale, {
        style: 'currency',
        currency,
    }).format(amount);
}

/**
 * Format number as percentage
 */
export function formatPercentage(value: number, decimals = 2, locale = 'en-US'): string {
    return new Intl.NumberFormat(locale, {
        style: 'percent',
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals,
    }).format(value);
}

/**
 * Round number to specified decimal places
 */
export function roundTo(num: number, decimals: number): number {
    const factor = Math.pow(10, decimals);
    return Math.round(num * factor) / factor;
}

/**
 * Clamp number between min and max values
 */
export function clamp(num: number, min: number, max: number): number {
    return Math.min(Math.max(num, min), max);
}

/**
 * Check if number is between min and max (inclusive)
 */
export function isBetween(num: number, min: number, max: number): boolean {
    return num >= min && num <= max;
}

/**
 * Generate random number between min and max
 */
export function randomBetween(min: number, max: number): number {
    return Math.random() * (max - min) + min;
}

/**
 * Generate random integer between min and max (inclusive)
 */
export function randomIntBetween(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * Convert degrees to radians
 */
export function degreesToRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
}

/**
 * Convert radians to degrees
 */
export function radiansToDegrees(radians: number): number {
    return radians * (180 / Math.PI);
}

/**
 * Calculate percentage of value relative to total
 */
export function getPercentage(value: number, total: number): number {
    if (total === 0) return 0;
    return (value / total) * 100;
}

/**
 * Calculate value from percentage of total
 */
export function getValueFromPercentage(percentage: number, total: number): number {
    return (percentage / 100) * total;
}

/**
 * Check if number is even
 */
export function isEven(num: number): boolean {
    return num % 2 === 0;
}

/**
 * Check if number is odd
 */
export function isOdd(num: number): boolean {
    return num % 2 !== 0;
}

/**
 * Check if number is prime
 */
export function isPrime(num: number): boolean {
    if (num < 2) return false;
    if (num === 2) return true;
    if (num % 2 === 0) return false;

    for (let i = 3; i <= Math.sqrt(num); i += 2) {
        if (num % i === 0) return false;
    }

    return true;
}

/**
 * Calculate factorial of a number
 */
export function factorial(num: number): number {
    if (num < 0) throw new Error('Factorial is not defined for negative numbers');
    if (num === 0 || num === 1) return 1;

    let result = 1;
    for (let i = 2; i <= num; i++) {
        result *= i;
    }

    return result;
}

/**
 * Calculate greatest common divisor (GCD)
 */
export function gcd(a: number, b: number): number {
    a = Math.abs(a);
    b = Math.abs(b);

    while (b !== 0) {
        const temp = b;
        b = a % b;
        a = temp;
    }

    return a;
}

/**
 * Calculate least common multiple (LCM)
 */
export function lcm(a: number, b: number): number {
    return Math.abs(a * b) / gcd(a, b);
}

/**
 * Calculate average of numbers
 */
export function average(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    return sum(numbers) / numbers.length;
}

/**
 * Calculate sum of numbers
 */
export function sum(numbers: number[]): number {
    return numbers.reduce((acc, num) => acc + num, 0);
}

/**
 * Calculate median of numbers
 */
export function median(numbers: number[]): number {
    if (numbers.length === 0) return 0;

    const sorted = [...numbers].sort((a, b) => a - b);
    const middle = Math.floor(sorted.length / 2);

    if (sorted.length % 2 === 0) {
        return (sorted[middle - 1] + sorted[middle]) / 2;
    }

    return sorted[middle];
}

/**
 * Calculate mode of numbers (most frequent)
 */
export function mode(numbers: number[]): number[] {
    if (numbers.length === 0) return [];

    const frequency: Record<number, number> = {};
    let maxFreq = 0;

    // Count frequencies
    for (const num of numbers) {
        frequency[num] = (frequency[num] || 0) + 1;
        maxFreq = Math.max(maxFreq, frequency[num]);
    }

    // Find all numbers with max frequency
    return Object.keys(frequency)
        .filter((key) => frequency[Number(key)] === maxFreq)
        .map(Number);
}

/**
 * Calculate standard deviation
 */
export function standardDeviation(numbers: number[]): number {
    if (numbers.length === 0) return 0;

    const avg = average(numbers);
    const squaredDiffs = numbers.map((num) => Math.pow(num - avg, 2));
    const avgSquaredDiff = average(squaredDiffs);

    return Math.sqrt(avgSquaredDiff);
}

/**
 * Calculate variance
 */
export function variance(numbers: number[]): number {
    if (numbers.length === 0) return 0;

    const avg = average(numbers);
    const squaredDiffs = numbers.map((num) => Math.pow(num - avg, 2));

    return average(squaredDiffs);
}

/**
 * Find minimum value in array
 */
export function min(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    return Math.min(...numbers);
}

/**
 * Find maximum value in array
 */
export function max(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    return Math.max(...numbers);
}

/**
 * Calculate distance between two points
 */
export function distance(x1: number, y1: number, x2: number, y2: number): number {
    return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
}

/**
 * Linear interpolation between two values
 */
export function lerp(start: number, end: number, factor: number): number {
    return start + (end - start) * factor;
}

/**
 * Map value from one range to another
 */
export function mapRange(value: number, fromMin: number, fromMax: number, toMin: number, toMax: number): number {
    return ((value - fromMin) / (fromMax - fromMin)) * (toMax - toMin) + toMin;
}

/**
 * Convert bytes to human readable format
 */
export function bytesToSize(bytes: number, decimals = 2): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Parse string to number with fallback
 */
export function parseNumber(value: string | number, fallback = 0): number {
    if (typeof value === 'number') return value;

    const parsed = parseFloat(value);
    return isNaN(parsed) ? fallback : parsed;
}

/**
 * Parse string to integer with fallback
 */
export function parseInt(value: string | number, fallback = 0): number {
    if (typeof value === 'number') return Math.floor(value);

    const parsed = Number.parseInt(value, 10);
    return isNaN(parsed) ? fallback : parsed;
}

/**
 * Check if value is a valid number
 */
export function isValidNumber(value: unknown): value is number {
    return typeof value === 'number' && !isNaN(value) && isFinite(value);
}

/**
 * Format number with ordinal suffix (1st, 2nd, 3rd, etc.)
 */
export function formatOrdinal(num: number): string {
    const suffixes = ['th', 'st', 'nd', 'rd'];
    const value = num % 100;

    return num + (suffixes[(value - 20) % 10] || suffixes[value] || suffixes[0]);
}

/**
 * Convert number to Roman numerals
 */
export function toRoman(num: number): string {
    if (num <= 0 || num >= 4000) {
        throw new Error('Number must be between 1 and 3999');
    }

    const values = [1000, 900, 500, 400, 100, 90, 50, 40, 10, 9, 5, 4, 1];
    const symbols = ['M', 'CM', 'D', 'CD', 'C', 'XC', 'L', 'XL', 'X', 'IX', 'V', 'IV', 'I'];

    let result = '';

    for (let i = 0; i < values.length; i++) {
        while (num >= values[i]) {
            result += symbols[i];
            num -= values[i];
        }
    }

    return result;
}
