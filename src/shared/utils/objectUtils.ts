// Object manipulation and utility functions

/**
 * Deep clone an object
 */
export function deepClone<T>(obj: T): T {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }

    if (obj instanceof Date) {
        return new Date(obj.getTime()) as T;
    }

    if (obj instanceof Array) {
        return obj.map((item) => deepClone(item)) as T;
    }

    if (typeof obj === 'object') {
        const cloned = {} as Record<string, unknown>;
        for (const key in obj) {
            if (Object.prototype.hasOwnProperty.call(obj, key)) {
                cloned[key] = deepClone((obj as Record<string, unknown>)[key]);
            }
        }
        return cloned as T;
    }

    return obj;
}

/**
 * Deep merge two objects
 */
export function deepMerge(target: Record<string, unknown>, source: Record<string, unknown>): Record<string, unknown> {
    const result = { ...target };

    for (const key in source) {
        if (Object.prototype.hasOwnProperty.call(source, key)) {
            const sourceValue = source[key];
            const targetValue = result[key];

            if (
                sourceValue &&
                typeof sourceValue === 'object' &&
                !Array.isArray(sourceValue) &&
                targetValue &&
                typeof targetValue === 'object' &&
                !Array.isArray(targetValue)
            ) {
                result[key] = deepMerge(targetValue as Record<string, unknown>, sourceValue as Record<string, unknown>);
            } else {
                result[key] = sourceValue;
            }
        }
    }

    return result;
}

/**
 * Get nested property value using dot notation
 */
export function get(obj: Record<string, unknown>, path: string, defaultValue: unknown = undefined): unknown {
    const keys = path.split('.');
    let result: unknown = obj;

    for (const key of keys) {
        if (
            result === null ||
            result === undefined ||
            typeof result !== 'object' ||
            !Object.prototype.hasOwnProperty.call(result, key)
        ) {
            return defaultValue;
        }
        result = (result as Record<string, unknown>)[key];
    }

    return result;
}

/**
 * Set nested property value using dot notation
 */
export function set(obj: Record<string, unknown>, path: string, value: unknown): Record<string, unknown> {
    const keys = path.split('.');
    const result = deepClone(obj) as Record<string, unknown>;
    let current = result;

    for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!current[key] || typeof current[key] !== 'object' || Array.isArray(current[key])) {
            current[key] = {};
        }
        current = current[key] as Record<string, unknown>;
    }

    current[keys[keys.length - 1]] = value;
    return result;
}

/**
 * Check if object has nested property using dot notation
 */
export function has(obj: Record<string, unknown>, path: string): boolean {
    const keys = path.split('.');
    let current: unknown = obj;

    for (const key of keys) {
        if (
            current === null ||
            current === undefined ||
            typeof current !== 'object' ||
            !Object.prototype.hasOwnProperty.call(current, key)
        ) {
            return false;
        }
        current = (current as Record<string, unknown>)[key];
    }

    return true;
}

/**
 * Remove nested property using dot notation
 */
export function unset(obj: Record<string, unknown>, path: string): Record<string, unknown> {
    const keys = path.split('.');
    const result = deepClone(obj) as Record<string, unknown>;
    let current = result;

    for (let i = 0; i < keys.length - 1; i++) {
        const key = keys[i];
        if (!current[key] || typeof current[key] !== 'object' || Array.isArray(current[key])) {
            return result; // Path doesn't exist
        }
        current = current[key] as Record<string, unknown>;
    }

    delete current[keys[keys.length - 1]];
    return result;
}

/**
 * Pick specific properties from object
 */
export function pick<T extends Record<string, unknown>>(obj: T, keys: (keyof T)[]): Partial<T> {
    const result: Partial<T> = {};

    for (const key of keys) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            result[key] = obj[key];
        }
    }

    return result;
}

/**
 * Omit specific properties from object
 */
export function omit<T extends Record<string, unknown>>(obj: T, keys: (keyof T)[]): Partial<T> {
    const result = { ...obj };

    for (const key of keys) {
        delete result[key];
    }

    return result;
}

/**
 * Get all keys from object (including nested)
 */
export function getAllKeys(obj: Record<string, unknown>, prefix = ''): string[] {
    const keys: string[] = [];

    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const fullKey = prefix ? `${prefix}.${key}` : key;
            keys.push(fullKey);

            const value = obj[key];
            if (value && typeof value === 'object' && !Array.isArray(value)) {
                keys.push(...getAllKeys(value as Record<string, unknown>, fullKey));
            }
        }
    }

    return keys;
}

/**
 * Get all values from object (including nested)
 */
export function getAllValues(obj: Record<string, unknown>): unknown[] {
    const values: unknown[] = [];

    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const value = obj[key];
            values.push(value);

            if (value && typeof value === 'object' && !Array.isArray(value)) {
                values.push(...getAllValues(value as Record<string, unknown>));
            }
        }
    }

    return values;
}

/**
 * Flatten nested object to single level with dot notation keys
 */
export function flatten(obj: Record<string, unknown>, prefix = ''): Record<string, unknown> {
    const result: Record<string, unknown> = {};

    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const fullKey = prefix ? `${prefix}.${key}` : key;
            const value = obj[key];

            if (value && typeof value === 'object' && !Array.isArray(value)) {
                Object.assign(result, flatten(value as Record<string, unknown>, fullKey));
            } else {
                result[fullKey] = value;
            }
        }
    }

    return result;
}

/**
 * Unflatten object with dot notation keys to nested object
 */
export function unflatten(obj: Record<string, unknown>): Record<string, unknown> {
    const result: Record<string, unknown> = {};

    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const value = obj[key];
            const keys = key.split('.');
            let current = result;

            for (let i = 0; i < keys.length - 1; i++) {
                const k = keys[i];
                if (!current[k] || typeof current[k] !== 'object') {
                    current[k] = {};
                }
                current = current[k] as Record<string, unknown>;
            }

            current[keys[keys.length - 1]] = value;
        }
    }

    return result;
}

/**
 * Check if object is empty
 */
export function isEmpty(obj: Record<string, unknown>): boolean {
    return Object.keys(obj).length === 0;
}

/**
 * Get object size (number of properties)
 */
export function size(obj: Record<string, unknown>): number {
    return Object.keys(obj).length;
}

/**
 * Invert object (swap keys and values)
 */
export function invert(obj: Record<string, string | number>): Record<string, string> {
    const result: Record<string, string> = {};

    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const value = obj[key];
            result[String(value)] = key;
        }
    }

    return result;
}

/**
 * Map object keys
 */
export function mapKeys(
    obj: Record<string, unknown>,
    mapper: (key: string, value: unknown) => string,
): Record<string, unknown> {
    const result: Record<string, unknown> = {};

    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const value = obj[key];
            const newKey = mapper(key, value);
            result[newKey] = value;
        }
    }

    return result;
}

/**
 * Map object values
 */
export function mapValues(
    obj: Record<string, unknown>,
    mapper: (value: unknown, key: string) => unknown,
): Record<string, unknown> {
    const result: Record<string, unknown> = {};

    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const value = obj[key];
            result[key] = mapper(value, key);
        }
    }

    return result;
}

/**
 * Filter object properties
 */
export function filter(
    obj: Record<string, unknown>,
    predicate: (value: unknown, key: string) => boolean,
): Record<string, unknown> {
    const result: Record<string, unknown> = {};

    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
            const value = obj[key];
            if (predicate(value, key)) {
                result[key] = value;
            }
        }
    }

    return result;
}

/**
 * Deep equality comparison
 */
export function isEqual(obj1: unknown, obj2: unknown): boolean {
    if (obj1 === obj2) {
        return true;
    }

    if (obj1 === null || obj2 === null || obj1 === undefined || obj2 === undefined) {
        return obj1 === obj2;
    }

    if (typeof obj1 !== typeof obj2) {
        return false;
    }

    if (typeof obj1 !== 'object') {
        return obj1 === obj2;
    }

    if (Array.isArray(obj1) !== Array.isArray(obj2)) {
        return false;
    }

    if (Array.isArray(obj1) && Array.isArray(obj2)) {
        if (obj1.length !== obj2.length) {
            return false;
        }

        for (let i = 0; i < obj1.length; i++) {
            if (!isEqual(obj1[i], obj2[i])) {
                return false;
            }
        }

        return true;
    }

    const keys1 = Object.keys(obj1 as Record<string, unknown>);
    const keys2 = Object.keys(obj2 as Record<string, unknown>);

    if (keys1.length !== keys2.length) {
        return false;
    }

    for (const key of keys1) {
        if (!keys2.includes(key)) {
            return false;
        }

        if (!isEqual((obj1 as Record<string, unknown>)[key], (obj2 as Record<string, unknown>)[key])) {
            return false;
        }
    }

    return true;
}

/**
 * Remove null and undefined values from object
 */
export function compact(obj: Record<string, unknown>): Record<string, unknown> {
    return filter(obj, (value) => value !== null && value !== undefined);
}

/**
 * Remove falsy values from object
 */
export function compactFalsy(obj: Record<string, unknown>): Record<string, unknown> {
    return filter(obj, (value) => Boolean(value));
}
