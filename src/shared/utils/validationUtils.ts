// Validation utility functions

/**
 * Email validation regex
 */
const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

/**
 * Phone number validation regex (international format)
 */
const PHONE_REGEX = /^\+?[1-9]\d{1,14}$/;

/**
 * URL validation regex
 */
const URL_REGEX = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;

/**
 * Strong password regex (at least 8 chars, 1 uppercase, 1 lowercase, 1 number, 1 special char)
 */
// const STRONG_PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

/**
 * Username regex (alphanumeric, underscore, hyphen, 3-20 chars)
 */
const USERNAME_REGEX = /^[a-zA-Z0-9_-]{3,20}$/;

/**
 * Hexadecimal color regex
 */
const HEX_COLOR_REGEX = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;

/**
 * IPv4 address regex
 */
const IPV4_REGEX = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

/**
 * IPv6 address regex
 */
const IPV6_REGEX = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;

/**
 * Credit card number regex (basic format)
 */
const CREDIT_CARD_REGEX = /^[0-9]{13,19}$/;

/**
 * Validation result interface
 */
export interface ValidationResult {
  valid: boolean;
  message?: string;
}

/**
 * Validate email address
 */
export function validateEmail(email: string): ValidationResult {
  if (!email) {
    return { valid: false, message: 'Email is required' };
  }
  
  if (!EMAIL_REGEX.test(email)) {
    return { valid: false, message: 'Invalid email format' };
  }
  
  return { valid: true };
}

/**
 * Validate phone number
 */
export function validatePhone(phone: string): ValidationResult {
  if (!phone) {
    return { valid: false, message: 'Phone number is required' };
  }
  
  const cleanPhone = phone.replace(/[\s()-]/g, '');
  
  if (!PHONE_REGEX.test(cleanPhone)) {
    return { valid: false, message: 'Invalid phone number format' };
  }
  
  return { valid: true };
}

/**
 * Validate URL
 */
export function validateUrl(url: string): ValidationResult {
  if (!url) {
    return { valid: false, message: 'URL is required' };
  }
  
  if (!URL_REGEX.test(url)) {
    return { valid: false, message: 'Invalid URL format' };
  }
  
  return { valid: true };
}

/**
 * Validate password strength
 */
export function validatePassword(
  password: string,
  options: {
    minLength?: number;
    requireUppercase?: boolean;
    requireLowercase?: boolean;
    requireNumbers?: boolean;
    requireSpecialChars?: boolean;
    customRegex?: RegExp;
  } = {},
): ValidationResult {
  const {
    minLength = 8,
    requireUppercase = true,
    requireLowercase = true,
    requireNumbers = true,
    requireSpecialChars = true,
    customRegex,
  } = options;
  
  if (!password) {
    return { valid: false, message: 'Password is required' };
  }
  
  if (password.length < minLength) {
    return {
      valid: false,
      message: `Password must be at least ${minLength} characters long`,
    };
  }
  
  if (requireUppercase && !/[A-Z]/.test(password)) {
    return {
      valid: false,
      message: 'Password must contain at least one uppercase letter',
    };
  }
  
  if (requireLowercase && !/[a-z]/.test(password)) {
    return {
      valid: false,
      message: 'Password must contain at least one lowercase letter',
    };
  }
  
  if (requireNumbers && !/\d/.test(password)) {
    return {
      valid: false,
      message: 'Password must contain at least one number',
    };
  }
  
  if (requireSpecialChars && !/[@$!%*?&]/.test(password)) {
    return {
      valid: false,
      message: 'Password must contain at least one special character (@$!%*?&)',
    };
  }
  
  if (customRegex && !customRegex.test(password)) {
    return {
      valid: false,
      message: 'Password does not meet custom requirements',
    };
  }
  
  return { valid: true };
}

/**
 * Validate username
 */
export function validateUsername(username: string): ValidationResult {
  if (!username) {
    return { valid: false, message: 'Username is required' };
  }
  
  if (!USERNAME_REGEX.test(username)) {
    return {
      valid: false,
      message: 'Username must be 3-20 characters long and contain only letters, numbers, underscores, and hyphens',
    };
  }
  
  return { valid: true };
}

/**
 * Validate required field
 */
export function validateRequired(value: unknown, fieldName = 'Field'): ValidationResult {
  if (value === null || value === undefined || value === '') {
    return { valid: false, message: `${fieldName} is required` };
  }
  
  if (typeof value === 'string' && value.trim() === '') {
    return { valid: false, message: `${fieldName} is required` };
  }
  
  if (Array.isArray(value) && value.length === 0) {
    return { valid: false, message: `${fieldName} is required` };
  }
  
  return { valid: true };
}

/**
 * Validate string length
 */
export function validateLength(
  value: string,
  min?: number,
  max?: number,
  fieldName = 'Field',
): ValidationResult {
  if (min !== undefined && value.length < min) {
    return {
      valid: false,
      message: `${fieldName} must be at least ${min} characters long`,
    };
  }
  
  if (max !== undefined && value.length > max) {
    return {
      valid: false,
      message: `${fieldName} must be no more than ${max} characters long`,
    };
  }
  
  return { valid: true };
}

/**
 * Validate number range
 */
export function validateRange(
  value: number,
  min?: number,
  max?: number,
  fieldName = 'Value',
): ValidationResult {
  if (min !== undefined && value < min) {
    return {
      valid: false,
      message: `${fieldName} must be at least ${min}`,
    };
  }
  
  if (max !== undefined && value > max) {
    return {
      valid: false,
      message: `${fieldName} must be no more than ${max}`,
    };
  }
  
  return { valid: true };
}

/**
 * Validate date
 */
export function validateDate(
  date: string | Date,
  options: {
    minDate?: Date;
    maxDate?: Date;
    allowFuture?: boolean;
    allowPast?: boolean;
  } = {},
): ValidationResult {
  const {
    minDate,
    maxDate,
    allowFuture = true,
    allowPast = true,
  } = options;
  
  let dateObj: Date;
  
  if (typeof date === 'string') {
    dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) {
      return { valid: false, message: 'Invalid date format' };
    }
  } else {
    dateObj = date;
  }
  
  const now = new Date();
  
  if (!allowFuture && dateObj > now) {
    return { valid: false, message: 'Future dates are not allowed' };
  }
  
  if (!allowPast && dateObj < now) {
    return { valid: false, message: 'Past dates are not allowed' };
  }
  
  if (minDate && dateObj < minDate) {
    return {
      valid: false,
      message: `Date must be after ${minDate.toLocaleDateString()}`,
    };
  }
  
  if (maxDate && dateObj > maxDate) {
    return {
      valid: false,
      message: `Date must be before ${maxDate.toLocaleDateString()}`,
    };
  }
  
  return { valid: true };
}

/**
 * Validate age
 */
export function validateAge(
  birthDate: string | Date,
  minAge?: number,
  maxAge?: number,
): ValidationResult {
  let dateObj: Date;
  
  if (typeof birthDate === 'string') {
    dateObj = new Date(birthDate);
    if (isNaN(dateObj.getTime())) {
      return { valid: false, message: 'Invalid birth date format' };
    }
  } else {
    dateObj = birthDate;
  }
  
  const now = new Date();
  const age = now.getFullYear() - dateObj.getFullYear();
  const monthDiff = now.getMonth() - dateObj.getMonth();
  
  const actualAge = monthDiff < 0 || (monthDiff === 0 && now.getDate() < dateObj.getDate())
    ? age - 1
    : age;
  
  if (minAge !== undefined && actualAge < minAge) {
    return {
      valid: false,
      message: `Age must be at least ${minAge} years`,
    };
  }
  
  if (maxAge !== undefined && actualAge > maxAge) {
    return {
      valid: false,
      message: `Age must be no more than ${maxAge} years`,
    };
  }
  
  return { valid: true };
}

/**
 * Validate hex color
 */
export function validateHexColor(color: string): ValidationResult {
  if (!color) {
    return { valid: false, message: 'Color is required' };
  }
  
  if (!HEX_COLOR_REGEX.test(color)) {
    return { valid: false, message: 'Invalid hex color format' };
  }
  
  return { valid: true };
}

/**
 * Validate IP address
 */
export function validateIpAddress(
  ip: string,
  version: 'v4' | 'v6' | 'both' = 'both',
): ValidationResult {
  if (!ip) {
    return { valid: false, message: 'IP address is required' };
  }
  
  const isV4 = IPV4_REGEX.test(ip);
  const isV6 = IPV6_REGEX.test(ip);
  
  if (version === 'v4' && !isV4) {
    return { valid: false, message: 'Invalid IPv4 address format' };
  }
  
  if (version === 'v6' && !isV6) {
    return { valid: false, message: 'Invalid IPv6 address format' };
  }
  
  if (version === 'both' && !isV4 && !isV6) {
    return { valid: false, message: 'Invalid IP address format' };
  }
  
  return { valid: true };
}

/**
 * Validate credit card number (basic)
 */
export function validateCreditCard(cardNumber: string): ValidationResult {
  if (!cardNumber) {
    return { valid: false, message: 'Credit card number is required' };
  }
  
  const cleanNumber = cardNumber.replace(/[\s-]/g, '');
  
  if (!CREDIT_CARD_REGEX.test(cleanNumber)) {
    return { valid: false, message: 'Invalid credit card number format' };
  }
  
  // Luhn algorithm check
  let sum = 0;
  let isEven = false;
  
  for (let i = cleanNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(cleanNumber.charAt(i), 10);
    
    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }
    
    sum += digit;
    isEven = !isEven;
  }
  
  if (sum % 10 !== 0) {
    return { valid: false, message: 'Invalid credit card number' };
  }
  
  return { valid: true };
}

/**
 * Validate file upload
 */
export function validateFileUpload(
  file: File,
  options: {
    maxSize?: number; // in bytes
    allowedTypes?: string[];
    allowedExtensions?: string[];
  } = {},
): ValidationResult {
  const { maxSize, allowedTypes, allowedExtensions } = options;
  
  if (maxSize && file.size > maxSize) {
    const maxSizeMB = (maxSize / (1024 * 1024)).toFixed(2);
    return {
      valid: false,
      message: `File size must be less than ${maxSizeMB}MB`,
    };
  }
  
  if (allowedTypes && !allowedTypes.includes(file.type)) {
    return {
      valid: false,
      message: `File type ${file.type} is not allowed`,
    };
  }
  
  if (allowedExtensions) {
    const extension = file.name.split('.').pop()?.toLowerCase();
    if (!extension || !allowedExtensions.includes(extension)) {
      return {
        valid: false,
        message: `File extension .${extension} is not allowed`,
      };
    }
  }
  
  return { valid: true };
}

/**
 * Validate multiple fields
 */
export function validateFields(
  fields: Record<string, unknown>,
  validators: Record<string, (value: unknown) => ValidationResult>,
): { valid: boolean; errors: Record<string, string> } {
  const errors: Record<string, string> = {};
  let valid = true;
  
  for (const [fieldName, value] of Object.entries(fields)) {
    const validator = validators[fieldName];
    if (validator) {
      const result = validator(value);
      if (!result.valid) {
        errors[fieldName] = result.message || 'Invalid value';
        valid = false;
      }
    }
  }
  
  return { valid, errors };
}

/**
 * Create custom validator
 */
export function createValidator(
  validationFn: (value: unknown) => boolean,
  errorMessage: string,
): (value: unknown) => ValidationResult {
  return (value: unknown) => {
    if (validationFn(value)) {
      return { valid: true };
    }
    return { valid: false, message: errorMessage };
  };
}

/**
 * Combine multiple validators
 */
export function combineValidators(
  ...validators: Array<(value: unknown) => ValidationResult>
): (value: unknown) => ValidationResult {
  return (value: unknown) => {
    for (const validator of validators) {
      const result = validator(value);
      if (!result.valid) {
        return result;
      }
    }
    return { valid: true };
  };
}

/**
 * Validate object schema
 */
export function validateSchema<T extends Record<string, unknown>>(
  obj: T,
  schema: {
    [K in keyof T]?: {
      required?: boolean;
      validator?: (value: T[K]) => ValidationResult;
    };
  },
): { valid: boolean; errors: Partial<Record<keyof T, string>> } {
  const errors: Partial<Record<keyof T, string>> = {};
  let valid = true;
  
  for (const [key, rules] of Object.entries(schema) as Array<[keyof T, NonNullable<typeof schema[keyof T]>]>) {
    const value = obj[key];
    
    if (rules.required) {
      const requiredResult = validateRequired(value, String(key));
      if (!requiredResult.valid) {
        errors[key] = requiredResult.message;
        valid = false;
        continue;
      }
    }
    
    if (rules.validator && value !== undefined && value !== null) {
      const validatorResult = rules.validator(value);
      if (!validatorResult.valid) {
        errors[key] = validatorResult.message;
        valid = false;
      }
    }
  }
  
  return { valid, errors };
}