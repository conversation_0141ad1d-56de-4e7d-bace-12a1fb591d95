// Array manipulation and utility functions

/**
 * Remove duplicates from array
 */
export function unique<T>(array: T[]): T[] {
    return [...new Set(array)];
}

/**
 * Remove duplicates from array by key
 */
export function uniqueBy<T, K extends keyof T>(array: T[], key: K): T[] {
    const seen = new Set();
    return array.filter((item) => {
        const value = item[key];
        if (seen.has(value)) {
            return false;
        }
        seen.add(value);
        return true;
    });
}

/**
 * Chunk array into smaller arrays of specified size
 */
export function chunk<T>(array: T[], size: number): T[][] {
    if (size <= 0) throw new Error('Chunk size must be greater than 0');

    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
        chunks.push(array.slice(i, i + size));
    }
    return chunks;
}

/**
 * Flatten nested arrays
 */
export function flatten<T>(array: (T | T[])[]): T[] {
    return array.reduce<T[]>((acc, val) => {
        return acc.concat(Array.isArray(val) ? flatten(val) : val);
    }, []);
}

/**
 * Group array elements by key
 */
export function groupBy<T, K extends keyof T>(array: T[], key: K): Record<string, T[]> {
    return array.reduce(
        (groups, item) => {
            const groupKey = String(item[key]);
            if (!groups[groupKey]) {
                groups[groupKey] = [];
            }
            groups[groupKey].push(item);
            return groups;
        },
        {} as Record<string, T[]>,
    );
}

/**
 * Group array elements by function result
 */
export function groupByFn<T, K extends string | number>(array: T[], fn: (item: T) => K): Record<K, T[]> {
    return array.reduce(
        (groups, item) => {
            const key = fn(item);
            if (!groups[key]) {
                groups[key] = [];
            }
            groups[key].push(item);
            return groups;
        },
        {} as Record<K, T[]>,
    );
}

/**
 * Sort array by key
 */
export function sortBy<T, K extends keyof T>(array: T[], key: K, direction: 'asc' | 'desc' = 'asc'): T[] {
    return [...array].sort((a, b) => {
        const aVal = a[key];
        const bVal = b[key];

        if (aVal < bVal) return direction === 'asc' ? -1 : 1;
        if (aVal > bVal) return direction === 'asc' ? 1 : -1;
        return 0;
    });
}

/**
 * Sort array by multiple keys
 */
export function sortByMultiple<T>(
    array: T[],
    sortKeys: Array<{
        key: keyof T;
        direction?: 'asc' | 'desc';
    }>,
): T[] {
    return [...array].sort((a, b) => {
        for (const { key, direction = 'asc' } of sortKeys) {
            const aVal = a[key];
            const bVal = b[key];

            if (aVal < bVal) return direction === 'asc' ? -1 : 1;
            if (aVal > bVal) return direction === 'asc' ? 1 : -1;
        }
        return 0;
    });
}

/**
 * Shuffle array randomly
 */
export function shuffle<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

/**
 * Get random element from array
 */
export function sample<T>(array: T[]): T | undefined {
    if (array.length === 0) return undefined;
    return array[Math.floor(Math.random() * array.length)];
}

/**
 * Get multiple random elements from array
 */
export function sampleSize<T>(array: T[], size: number): T[] {
    if (size >= array.length) return shuffle(array);

    const shuffled = shuffle(array);
    return shuffled.slice(0, size);
}

/**
 * Find intersection of two arrays
 */
export function intersection<T>(array1: T[], array2: T[]): T[] {
    const set2 = new Set(array2);
    return array1.filter((item) => set2.has(item));
}

/**
 * Find difference between two arrays (items in first but not in second)
 */
export function difference<T>(array1: T[], array2: T[]): T[] {
    const set2 = new Set(array2);
    return array1.filter((item) => !set2.has(item));
}

/**
 * Find union of two arrays (all unique items from both)
 */
export function union<T>(array1: T[], array2: T[]): T[] {
    return unique([...array1, ...array2]);
}

/**
 * Check if arrays are equal
 */
export function isEqual<T>(array1: T[], array2: T[]): boolean {
    if (array1.length !== array2.length) return false;

    for (let i = 0; i < array1.length; i++) {
        if (array1[i] !== array2[i]) return false;
    }

    return true;
}

/**
 * Check if array includes all items from another array
 */
export function includesAll<T>(array: T[], items: T[]): boolean {
    return items.every((item) => array.includes(item));
}

/**
 * Check if array includes any item from another array
 */
export function includesAny<T>(array: T[], items: T[]): boolean {
    return items.some((item) => array.includes(item));
}

/**
 * Move element from one index to another
 */
export function move<T>(array: T[], fromIndex: number, toIndex: number): T[] {
    const result = [...array];
    const [removed] = result.splice(fromIndex, 1);
    result.splice(toIndex, 0, removed);
    return result;
}

/**
 * Insert element at specific index
 */
export function insert<T>(array: T[], index: number, item: T): T[] {
    const result = [...array];
    result.splice(index, 0, item);
    return result;
}

/**
 * Remove element at specific index
 */
export function removeAt<T>(array: T[], index: number): T[] {
    const result = [...array];
    result.splice(index, 1);
    return result;
}

/**
 * Remove elements by value
 */
export function remove<T>(array: T[], item: T): T[] {
    return array.filter((arrayItem) => arrayItem !== item);
}

/**
 * Remove elements by predicate function
 */
export function removeBy<T>(array: T[], predicate: (item: T) => boolean): T[] {
    return array.filter((item) => !predicate(item));
}

/**
 * Update element at specific index
 */
export function updateAt<T>(array: T[], index: number, item: T): T[] {
    const result = [...array];
    result[index] = item;
    return result;
}

/**
 * Update elements by predicate function
 */
export function updateBy<T>(array: T[], predicate: (item: T) => boolean, updater: (item: T) => T): T[] {
    return array.map((item) => (predicate(item) ? updater(item) : item));
}

/**
 * Partition array into two arrays based on predicate
 */
export function partition<T>(array: T[], predicate: (item: T) => boolean): [T[], T[]] {
    const truthy: T[] = [];
    const falsy: T[] = [];

    for (const item of array) {
        if (predicate(item)) {
            truthy.push(item);
        } else {
            falsy.push(item);
        }
    }

    return [truthy, falsy];
}

/**
 * Get first n elements
 */
export function take<T>(array: T[], count: number): T[] {
    return array.slice(0, count);
}

/**
 * Get last n elements
 */
export function takeLast<T>(array: T[], count: number): T[] {
    return array.slice(-count);
}

/**
 * Skip first n elements
 */
export function skip<T>(array: T[], count: number): T[] {
    return array.slice(count);
}

/**
 * Skip last n elements
 */
export function skipLast<T>(array: T[], count: number): T[] {
    return array.slice(0, -count);
}

/**
 * Get elements while predicate is true
 */
export function takeWhile<T>(array: T[], predicate: (item: T) => boolean): T[] {
    const result: T[] = [];

    for (const item of array) {
        if (!predicate(item)) break;
        result.push(item);
    }

    return result;
}

/**
 * Skip elements while predicate is true
 */
export function skipWhile<T>(array: T[], predicate: (item: T) => boolean): T[] {
    let skipCount = 0;

    for (const item of array) {
        if (!predicate(item)) break;
        skipCount++;
    }

    return array.slice(skipCount);
}

/**
 * Zip two arrays together
 */
export function zip<T, U>(array1: T[], array2: U[]): Array<[T, U]> {
    const length = Math.min(array1.length, array2.length);
    const result: Array<[T, U]> = [];

    for (let i = 0; i < length; i++) {
        result.push([array1[i], array2[i]]);
    }

    return result;
}

/**
 * Unzip array of pairs
 */
export function unzip<T, U>(pairs: Array<[T, U]>): [T[], U[]] {
    const first: T[] = [];
    const second: U[] = [];

    for (const [a, b] of pairs) {
        first.push(a);
        second.push(b);
    }

    return [first, second];
}

/**
 * Create array of numbers in range
 */
export function range(start: number, end?: number, step = 1): number[] {
    if (end === undefined) {
        end = start;
        start = 0;
    }

    const result: number[] = [];

    if (step > 0) {
        for (let i = start; i < end; i += step) {
            result.push(i);
        }
    } else if (step < 0) {
        for (let i = start; i > end; i += step) {
            result.push(i);
        }
    }

    return result;
}

/**
 * Create array with repeated value
 */
export function repeat<T>(value: T, count: number): T[] {
    return Array(count).fill(value);
}

/**
 * Check if array is empty
 */
export function isEmpty<T>(array: T[]): boolean {
    return array.length === 0;
}

/**
 * Check if array is not empty
 */
export function isNotEmpty<T>(array: T[]): array is [T, ...T[]] {
    return array.length > 0;
}

/**
 * Get first element
 */
export function first<T>(array: T[]): T | undefined {
    return array[0];
}

/**
 * Get last element
 */
export function last<T>(array: T[]): T | undefined {
    return array[array.length - 1];
}

/**
 * Get element at index with fallback
 */
export function at<T>(array: T[], index: number, fallback?: T): T | undefined {
    const item = array[index];
    return item !== undefined ? item : fallback;
}

/**
 * Count elements matching predicate
 */
export function count<T>(array: T[], predicate?: (item: T) => boolean): number {
    if (!predicate) return array.length;
    return array.filter(predicate).length;
}

/**
 * Sum numeric array
 */
export function sum(array: number[]): number {
    return array.reduce((acc, num) => acc + num, 0);
}

/**
 * Get average of numeric array
 */
export function average(array: number[]): number {
    if (array.length === 0) return 0;
    return sum(array) / array.length;
}

/**
 * Get minimum value from array
 */
export function min<T>(array: T[], compareFn?: (a: T, b: T) => number): T | undefined {
    if (array.length === 0) return undefined;

    if (compareFn) {
        return array.reduce((min, current) => (compareFn(current, min) < 0 ? current : min));
    }

    return array.reduce((min, current) => (current < min ? current : min));
}

/**
 * Get maximum value from array
 */
export function max<T>(array: T[], compareFn?: (a: T, b: T) => number): T | undefined {
    if (array.length === 0) return undefined;

    if (compareFn) {
        return array.reduce((max, current) => (compareFn(current, max) > 0 ? current : max));
    }

    return array.reduce((max, current) => (current > max ? current : max));
}
