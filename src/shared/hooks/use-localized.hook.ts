import { useTranslation } from 'react-i18next';

// Translatable text type
export interface TranslatableText {
    en?: string;
    ar?: string;
    [key: string]: string | undefined;
}

// Hook return type
export interface UseLocalizedReturn {
    // Function to get localized text
    (text: TranslatableText, fallback?: string): string;
    // Text direction based on current language
    dir: 'ltr' | 'rtl';
    // Current language code
    language: string;
    // Check if current language is RTL
    isRTL: boolean;
}

/**
 * Custom hook for handling localized text selection
 * Supports both function call and object destructuring usage patterns
 */
export function useLocalized(): UseLocalizedReturn {
    const { i18n } = useTranslation();
    const currentLanguage = i18n.language || 'en';
    const isRTL = currentLanguage === 'ar';
    const dir = isRTL ? 'rtl' : 'ltr';

    // Main function to get localized text
    const getLocalizedText = (text: TranslatableText, fallback = ''): string => {
        if (!text || typeof text !== 'object') {
            return fallback;
        }

        // Try current language first
        if (text[currentLanguage]) {
            return text[currentLanguage]!;
        }

        // Fallback to English
        if (text.en) {
            return text.en;
        }

        // Fallback to Arabic
        if (text.ar) {
            return text.ar;
        }

        // Fallback to first available value
        const firstValue = Object.values(text).find((value) => value && value.trim());
        if (firstValue) {
            return firstValue;
        }

        return fallback;
    };

    // Create the return object with function properties
    const localizedFunction = getLocalizedText as UseLocalizedReturn;
    localizedFunction.dir = dir;
    localizedFunction.language = currentLanguage;
    localizedFunction.isRTL = isRTL;

    return localizedFunction;
}

// Alternative hook for simple usage
export function useLocalizedText() {
    const localized = useLocalized();
    return localized;
}

// Hook for getting text direction
export function useTextDirection() {
    const { i18n } = useTranslation();
    const currentLanguage = i18n.language || 'en';
    const isRTL = currentLanguage === 'ar';
    const dir = isRTL ? 'rtl' : 'ltr';

    return {
        dir,
        isRTL,
        language: currentLanguage,
    };
}

// Hook for language switching
export function useLanguageSwitch() {
    const { i18n } = useTranslation();

    const switchLanguage = (language: string) => {
        i18n.changeLanguage(language);
    };

    const toggleLanguage = () => {
        const currentLang = i18n.language || 'en';
        const newLang = currentLang === 'en' ? 'ar' : 'en';
        switchLanguage(newLang);
    };

    return {
        currentLanguage: i18n.language || 'en',
        switchLanguage,
        toggleLanguage,
        availableLanguages: ['en', 'ar'],
    };
}
