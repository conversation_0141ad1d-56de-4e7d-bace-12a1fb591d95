import { useState, useEffect, useCallback } from 'react';

// Storage key management
export const STORAGE_KEYS = {
    USER_PREFERENCES: 'user_preferences',
    THEME: 'theme',
    LANGUAGE: 'language',
    AUTH_TOKEN: 'auth_token',
    USER_DATA: 'user_data',
    CART: 'cart',
    RECENT_SEARCHES: 'recent_searches',
    SETTINGS: 'settings',
} as const;

export type StorageKey = keyof typeof STORAGE_KEYS | string;

// Storage options
export interface StorageOptions {
    serialize?: (value: unknown) => string;
    deserialize?: (value: string) => unknown;
    defaultValue?: unknown;
    syncAcrossTabs?: boolean;
}

// Default serialization functions
const defaultSerialize = (value: unknown): string => JSON.stringify(value);
const defaultDeserialize = (value: string): unknown => {
    try {
        return JSON.parse(value);
    } catch {
        return value;
    }
};

/**
 * Custom hook for managing localStorage with type safety and React state synchronization
 */
export function useLocalStorage<T>(
    key: StorageKey,
    defaultValue?: T,
    options: StorageOptions = {},
): [T | undefined, (value: T | undefined | ((prev: T | undefined) => T | undefined)) => void, () => void] {
    const { serialize = defaultSerialize, deserialize = defaultDeserialize, syncAcrossTabs = true } = options;

    // Get the actual storage key
    const storageKey = typeof key === 'string' ? key : STORAGE_KEYS[key];

    // Initialize state with value from localStorage or default
    const [storedValue, setStoredValue] = useState<T | undefined>(() => {
        if (typeof window === 'undefined') {
            return defaultValue;
        }

        try {
            const item = window.localStorage.getItem(storageKey);
            return item ? (deserialize(item) as T) : defaultValue;
        } catch (error) {
            console.warn(`Error reading localStorage key "${storageKey}":`, error);
            return defaultValue;
        }
    });

    // Update localStorage and state
    const setValue = useCallback(
        (value: T | undefined | ((prev: T | undefined) => T | undefined)) => {
            try {
                const newValue =
                    typeof value === 'function'
                        ? (value as (prev: T | undefined) => T | undefined)(storedValue)
                        : value;
                setStoredValue(newValue);

                if (typeof window !== 'undefined') {
                    if (newValue === undefined) {
                        window.localStorage.removeItem(storageKey);
                    } else {
                        window.localStorage.setItem(storageKey, serialize(newValue));
                    }
                }
            } catch (error) {
                console.warn(`Error setting localStorage key "${storageKey}":`, error);
            }
        },
        [storageKey, serialize, storedValue],
    );

    // Remove item from localStorage
    const removeValue = useCallback(() => {
        try {
            setStoredValue(undefined);
            if (typeof window !== 'undefined') {
                window.localStorage.removeItem(storageKey);
            }
        } catch (error) {
            console.warn(`Error removing localStorage key "${storageKey}":`, error);
        }
    }, [storageKey]);

    // Listen for changes in other tabs/windows
    useEffect(() => {
        if (!syncAcrossTabs || typeof window === 'undefined') {
            return;
        }

        const handleStorageChange = (e: StorageEvent) => {
            if (e.key === storageKey && e.newValue !== null) {
                try {
                    const newValue = deserialize(e.newValue) as T;
                    setStoredValue(newValue);
                } catch (error) {
                    console.warn(`Error parsing localStorage change for key "${storageKey}":`, error);
                }
            } else if (e.key === storageKey && e.newValue === null) {
                setStoredValue(undefined);
            }
        };

        window.addEventListener('storage', handleStorageChange);
        return () => window.removeEventListener('storage', handleStorageChange);
    }, [storageKey, deserialize, syncAcrossTabs]);

    return [storedValue, setValue, removeValue];
}

/**
 * Hook for managing user preferences in localStorage
 */
export function useUserPreferences<T extends Record<string, unknown>>(
    defaultPreferences?: T,
): [T | undefined, (preferences: Partial<T>) => void, () => void] {
    const [preferences, setPreferences, removePreferences] = useLocalStorage<T>(
        STORAGE_KEYS.USER_PREFERENCES,
        defaultPreferences,
    );

    const updatePreferences = useCallback(
        (newPreferences: Partial<T>) => {
            setPreferences((prev: T | undefined) => ({ ...prev, ...newPreferences }) as T);
        },
        [setPreferences],
    );

    return [preferences, updatePreferences, removePreferences];
}

/**
 * Hook for managing theme in localStorage
 */
export function useThemeStorage() {
    const [theme, setTheme, removeTheme] = useLocalStorage<'light' | 'dark' | 'system'>(STORAGE_KEYS.THEME, 'system');

    return [theme, setTheme, removeTheme] as const;
}

/**
 * Hook for managing authentication token in localStorage
 */
export function useAuthTokenStorage() {
    const [token, setToken, removeToken] = useLocalStorage<string>(STORAGE_KEYS.AUTH_TOKEN);

    return [token, setToken, removeToken] as const;
}

/**
 * Hook for managing user data in localStorage
 */
export function useUserDataStorage<T extends Record<string, unknown>>() {
    const [userData, setUserData, removeUserData] = useLocalStorage<T>(STORAGE_KEYS.USER_DATA);

    return [userData, setUserData, removeUserData] as const;
}

/**
 * Hook for managing recent searches in localStorage
 */
export function useRecentSearches(maxItems = 10) {
    const [searches, setSearches, removeSearches] = useLocalStorage<string[]>(STORAGE_KEYS.RECENT_SEARCHES, []);

    const addSearch = useCallback(
        (search: string) => {
            if (!search.trim()) return;

            setSearches((prev: string[] | undefined) => {
                const filtered = (prev || []).filter((s: string) => s !== search);
                const updated = [search, ...filtered].slice(0, maxItems);
                return updated;
            });
        },
        [setSearches, maxItems],
    );

    const removeSearch = useCallback(
        (search: string) => {
            setSearches((prev: string[] | undefined) => (prev || []).filter((s: string) => s !== search));
        },
        [setSearches],
    );

    const clearSearches = useCallback(() => {
        removeSearches();
    }, [removeSearches]);

    return {
        searches: searches || [],
        addSearch,
        removeSearch,
        clearSearches,
    };
}

/**
 * Utility function to check if localStorage is available
 */
export function isLocalStorageAvailable(): boolean {
    if (typeof window === 'undefined') {
        return false;
    }

    try {
        const testKey = '__localStorage_test__';
        window.localStorage.setItem(testKey, 'test');
        window.localStorage.removeItem(testKey);
        return true;
    } catch {
        return false;
    }
}

/**
 * Utility function to get all localStorage keys with a prefix
 */
export function getStorageKeys(prefix?: string): string[] {
    if (typeof window === 'undefined' || !isLocalStorageAvailable()) {
        return [];
    }

    const keys: string[] = [];
    for (let i = 0; i < window.localStorage.length; i++) {
        const key = window.localStorage.key(i);
        if (key && (!prefix || key.startsWith(prefix))) {
            keys.push(key);
        }
    }
    return keys;
}

/**
 * Utility function to clear all localStorage keys with a prefix
 */
export function clearStorageByPrefix(prefix: string): void {
    if (typeof window === 'undefined' || !isLocalStorageAvailable()) {
        return;
    }

    const keys = getStorageKeys(prefix);
    keys.forEach((key) => {
        window.localStorage.removeItem(key);
    });
}
