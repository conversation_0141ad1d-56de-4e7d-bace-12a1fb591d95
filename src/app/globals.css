@import 'tailwindcss';

:root {
    --background: #ffffff;
    --foreground: #171717;
}

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --font-sans: var(--font-geist-sans);
    --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
    :root {
        --background: #0a0a0a;
        --foreground: #ededed;
    }
}

/* ✅ Global reset */
html,
body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow: hidden; /* block browser scrollbars */
}

/* ✅ If using Next.js/React root */
#__next,
#root {
    height: 100%;
    overflow: hidden; /* prevent double scroll */
}

/* ✅ Define where scroll is allowed */
.app-scroll {
    height: 100%;
    overflow: auto; /* only scroll here */
}
