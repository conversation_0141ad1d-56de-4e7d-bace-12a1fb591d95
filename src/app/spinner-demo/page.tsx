'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { SmartSpinner, SmartSpinnerOverlay, SmartSpinnerInline } from '@/components';

export default function SpinnerDemoPage() {
  const [showOverlay, setShowOverlay] = useState(false);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-8 text-center">
          Smart Spinner Demo
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          
          {/* Default Spinner */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
              Default Spinner
            </h2>
            <div className="flex justify-center">
              <SmartSpinner />
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-4 text-center">
              Standard 48px spinner with zoom animation
            </p>
          </div>

          {/* Large Spinner */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
              Large Spinner
            </h2>
            <div className="flex justify-center">
              <SmartSpinner size={80} />
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-4 text-center">
              80px spinner for prominent loading states
            </p>
          </div>

          {/* Fast Spinner */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
              Fast Spinner
            </h2>
            <div className="flex justify-center">
              <SmartSpinner speed={0.8} />
            </div>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-4 text-center">
              Faster animation for quick operations
            </p>
          </div>

          {/* Inline Spinners */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
              Inline Spinners
            </h2>
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <SmartSpinnerInline size={16} />
                <span className="text-gray-700 dark:text-gray-300">Small (16px)</span>
              </div>
              <div className="flex items-center gap-2">
                <SmartSpinnerInline size={24} />
                <span className="text-gray-700 dark:text-gray-300">Medium (24px)</span>
              </div>
              <div className="flex items-center gap-2">
                <SmartSpinnerInline size={32} />
                <span className="text-gray-700 dark:text-gray-300">Large (32px)</span>
              </div>
            </div>
          </div>

          {/* Button Examples */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
              Button Integration
            </h2>
            <div className="space-y-3">
              <button className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                <SmartSpinnerInline size={16} />
                Loading...
              </button>
              <button className="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 transition-colors">
                <SmartSpinnerInline size={20} />
                Processing
              </button>
            </div>
          </div>

          {/* Overlay Demo */}
          <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-white">
              Overlay Spinner
            </h2>
            <button
              onClick={() => setShowOverlay(true)}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg transition-colors font-medium"
            >
              Show Overlay Spinner
            </button>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-4 text-center">
              Full-screen loading overlay with backdrop
            </p>
          </div>

        </div>

        {/* Animation Speed Controls */}
        <div className="mt-12 bg-white dark:bg-gray-800 rounded-xl p-6 shadow-lg">
          <h2 className="text-2xl font-semibold mb-6 text-gray-800 dark:text-white text-center">
            Animation Variations
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <SmartSpinner speed={0.5} />
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">Slow (0.5s)</p>
            </div>
            <div className="text-center">
              <SmartSpinner speed={1} />
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">Normal (1s)</p>
            </div>
            <div className="text-center">
              <SmartSpinner speed={1.5} />
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">Default (1.5s)</p>
            </div>
            <div className="text-center">
              <SmartSpinner speed={2.5} />
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">Slow (2.5s)</p>
            </div>
          </div>
        </div>

        {/* Back to Home */}
        <div className="mt-8 text-center">
          <Link
            href="/"
            className="inline-flex items-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg transition-colors font-medium"
          >
            ← Back to Dashboard
          </Link>
        </div>
      </div>

      {/* Overlay Spinner */}
      {showOverlay && (
        <div onClick={() => setShowOverlay(false)}>
          <SmartSpinnerOverlay 
            size={72}
            speed={1.2}
          />
        </div>
      )}
    </div>
  );
}