'use client';

import React from 'react';
import { SystemCard } from '@/components/features/alert-dashboard/layout';

export default function SystemCardsDemoPage() {
    return (
        <div className="p-8 bg-gray-50 min-h-screen">
            <div className="max-w-7xl mx-auto">
                <h1 className="text-3xl font-bold text-gray-900 mb-2">System Cards Demo</h1>
                <p className="text-gray-600 mb-8">
                    Demonstration of the SystemCard component with different system types and states.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {/* Fire Alarm System */}
                    <SystemCard
                        title="Fire alarm system"
                        iconName="fire"
                        iconColor="#E87027"
                        metrics={[
                            { key: 'Total devices', value: '200', isAlert: false },
                            { key: 'Active Alarms', value: '2', isAlert: true },
                        ]}
                    />

                    {/* Access Control System */}
                    <SystemCard
                        title="Access control"
                        iconName="door"
                        iconColor="#10BCAD"
                        metrics={[
                            { key: 'Total doors', value: '150', isAlert: false },
                            { key: 'Open/Closed', value: '6/144', isAlert: false },
                        ]}
                    />

                    {/* CCTV System */}
                    <SystemCard
                        title="CCTV control"
                        iconName="camera"
                        iconColor="#877BD7"
                        metrics={[
                            { key: 'Total cameras', value: '200', isAlert: false },
                            { key: 'Active Incidents', value: '2', isAlert: true },
                        ]}
                    />
                </div>
            </div>
        </div>
    );
}
