/**
 * Dashboard Page
 * A simple, empty page demonstrating architectural patterns
 * and serving as a foundation for future development
 */

'use client';

import React from 'react';
import { useAppStore, useAppActions } from '@/stores/app.store';
import { appSettings } from '@/shared/config/app-settings.config';
import AlertCard from '@/components/features/alert-card';

/**
 * Dashboard page component
 * Demonstrates clean architecture, separation of concerns, and scalability
 */
const DashboardPage: React.FC = () => {
    // State management using Zustand store
    const { user, isAuthenticated, theme, isLoading } = useAppStore();
    const { setLoading, addNotification } = useAppActions();

    // Example of handling user interactions
    const handleWelcomeClick = () => {
        setLoading(true);

        // Simulate async operation
        setTimeout(() => {
            addNotification({
                type: 'success',
                title: 'Welcome!',
                message: 'Dashboard loaded successfully',
            });
            setLoading(false);
        }, 1000);
    };

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
            {/* Header Section */}
            <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        <div className="flex items-center">
                            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">Dashboard</h1>
                        </div>

                        <div className="flex items-center space-x-4">
                            {/* Theme indicator */}
                            <span className="text-sm text-gray-500 dark:text-gray-400">Theme: {theme}</span>

                            {/* Environment indicator */}
                            <span className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                {appSettings.environment}
                            </span>
                        </div>
                    </div>
                </div>
            </header>

            {/* Main Content */}
            <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                {/* Welcome Section */}
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
                    <div className="text-center">
                        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                            Welcome to the Dashboard
                        </h2>

                        <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
                            This is a foundational page built with clean architecture principles. It demonstrates proper
                            separation of concerns, state management, and scalable patterns for future development.
                        </p>

                        {/* User Status */}
                        <div className="mb-6">
                            {isAuthenticated && user ? (
                                <div className="inline-flex items-center px-4 py-2 rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            fillRule="evenodd"
                                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                            clipRule="evenodd"
                                        />
                                    </svg>
                                    Authenticated as {user.name}
                                </div>
                            ) : (
                                <div className="inline-flex items-center px-4 py-2 rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path
                                            fillRule="evenodd"
                                            d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                                            clipRule="evenodd"
                                        />
                                    </svg>
                                    Guest User
                                </div>
                            )}
                        </div>

                        {/* Action Button */}
                        <button
                            onClick={handleWelcomeClick}
                            disabled={isLoading}
                            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
                            {isLoading ? (
                                <>
                                    <svg
                                        className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                                        xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24">
                                        <circle
                                            className="opacity-25"
                                            cx="12"
                                            cy="12"
                                            r="10"
                                            stroke="currentColor"
                                            strokeWidth="4"></circle>
                                        <path
                                            className="opacity-75"
                                            fill="currentColor"
                                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Loading...
                                </>
                            ) : (
                                'Get Started'
                            )}
                        </button>
                    </div>
                </div>

                {/* Architecture Features Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {/* Feature 1: State Management */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <div className="flex items-center mb-4">
                            <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                                <svg
                                    className="w-6 h-6 text-blue-600 dark:text-blue-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                                    />
                                </svg>
                            </div>
                            <h3 className="ml-3 text-lg font-medium text-gray-900 dark:text-white">State Management</h3>
                        </div>
                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                            Zustand-powered global state with TypeScript support and optimized selectors.
                        </p>
                    </div>

                    {/* Feature 2: API Integration */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <div className="flex items-center mb-4">
                            <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                                <svg
                                    className="w-6 h-6 text-green-600 dark:text-green-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"
                                    />
                                </svg>
                            </div>
                            <h3 className="ml-3 text-lg font-medium text-gray-900 dark:text-white">API Integration</h3>
                        </div>
                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                            Axios-based HTTP client with interceptors, error handling, and type safety.
                        </p>
                    </div>

                    {/* Feature 3: Configuration */}
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                        <div className="flex items-center mb-4">
                            <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                                <svg
                                    className="w-6 h-6 text-purple-600 dark:text-purple-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                                    />
                                    <path
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth={2}
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                                    />
                                </svg>
                            </div>
                            <h3 className="ml-3 text-lg font-medium text-gray-900 dark:text-white">Configuration</h3>
                        </div>
                        <p className="text-gray-600 dark:text-gray-300 text-sm">
                            Environment-based configuration with type safety and development tools.
                        </p>
                    </div>
                </div>
                <AlertCard
                    type="fire_activated"
                    title="Fire alarm activated"
                    subHeader={{
                        text: 'Smoke Detector',
                        date: '12/5/2025 - 12:00 pm',
                        showText: true,
                        showDate: true,
                        separator: ' - ',
                        textColor: 'text-gray-300',
                        dateColor: 'text-gray-500',
                    }}
                    icon="fire"
                    statusBadge={{
                        text: 'ACTIVE',
                        variant: 'danger',
                        show: true,
                    }}
                    showPeopleCount={true}
                    peopleCount={15}
                    peopleCountLabel="evacuating"
                    infoRows={[
                        {
                            label: 'Zone:',
                            value: 'Floor 1, Building A, West Wing, Office area',
                            icon: 'map-pin',
                        },
                        {
                            label: 'Description:',
                            value: 'Smoke detected near the main conference room.',
                        },
                        {
                            label: 'Public address:',
                            value: 'Everyone must proceed to Gate 2',
                        },
                    ]}
                    buttons={[
                        {
                            text: 'Close',
                            onClick: () => console.log('Close'),
                            variant: 'secondary',
                        },
                        {
                            text: 'Action text',
                            onClick: () => console.log('Action'),
                            variant: 'primary',
                        },
                    ]}
                    onCloseX={() => console.log('Close X clicked')}
                />

                <AlertCard
                    type="access"
                    title="Access control"
                    subHeader={{
                        text: 'Some info',
                        showText: true,
                        showDate: false,
                    }}
                    statusBadge={{
                        text: 'Opened',
                        variant: 'success',
                        show: true,
                    }}
                    icon="openDoor"
                    showPeopleCount={true}
                    peopleCount={3}
                    peopleCountLabel="number of people"
                    infoRows={[
                        {
                            label: 'Zone:',
                            value: 'Floor 1, Building A, West Wing, Office area',
                        },
                        {
                            label: 'Description:',
                            value: 'the door in the floor 1 was opened',
                        },
                        {
                            label: 'Public address:',
                            value: 'Everyone must proceed to Gate 2',
                        },
                    ]}
                />

                <AlertCard
                    type="cctv"
                    title="cctv control"
                    subHeader={{
                        text: 'Some info',
                        showText: true,
                    }}
                    icon="camera"
                    showPeopleCount={true}
                    peopleCount={8}
                    imageConfig={{
                        src: '/api/camera/feed.jpg',
                        alt: 'Camera feed',
                        height: 200,
                        onClick: () => console.log('Image clicked'),
                    }}
                    infoRows={[
                        {
                            label: 'Zone:',
                            value: 'Floor 1, Building A, West Wing, Office area',
                        },
                        {
                            label: 'Description:',
                            value: 'we will add some description here',
                        },
                        {
                            label: 'Public address:',
                            value: 'Everyone must proceed to Gate 2',
                        },
                    ]}
                    buttons={[
                        {
                            text: 'Close',
                            onClick: () => console.log('Close'),
                            variant: 'secondary',
                        },
                        {
                            text: 'Watch stream',
                            onClick: () => console.log('Watch'),
                            variant: 'primary',
                            icon: 'play',
                        },
                    ]}
                />

                <AlertCard
                    type="fire_point"
                    title="Fire alarm point"
                    subHeader={{
                        text: 'Smoke Detector',
                        date: '12/5/2025 - 12:00 pm',
                        showText: true,
                        showDate: true,
                    }}
                    icon="fire"
                    statusBadge={{ text: 'ACTIVE', variant: 'danger', show: true }}
                    showPeopleCount={true}
                    peopleCount={15}
                    peopleCountLabel="evacuating"
                    infoRows={[
                        {
                            label: 'Zone:',
                            value: 'Floor 1, Building A, West Wing, Office area',
                        },
                        {
                            label: 'Description:',
                            value: 'Smoke detected near the main conference room.',
                        },
                        {
                            label: 'Public address:',
                            value: 'Everyone must proceed to Gate 2',
                        },
                    ]}
                    buttons={[
                        {
                            text: 'Close',
                            onClick: () => console.log('Close'),
                            variant: 'secondary',
                        },
                        {
                            text: 'Action text',
                            onClick: () => console.log('Action'),
                            variant: 'primary',
                        },
                    ]}
                    onCloseX={() => console.log('Dismissed')}
                />

                <AlertCard
                    type="cctv"
                    title="CCTV control"
                    subHeader={{ text: 'Some info', showText: true }}
                    icon="camera"
                    showPeopleCount={true}
                    peopleCount={8}
                    imageConfig={{
                        src: '/camera-feed.jpg',
                        alt: 'Camera feed',
                        width: 468,
                        height: 220,
                        onClick: () => console.log('Image clicked'),
                    }}
                    infoRows={[
                        {
                            label: 'Zone:',
                            value: 'Floor 1, Building A, West Wing, Office area',
                        },
                    ]}
                    buttons={[
                        {
                            text: 'Close',
                            onClick: () => {},
                            variant: 'secondary',
                        },
                        {
                            text: 'Watch stream',
                            onClick: () => {},
                            variant: 'primary',
                        },
                    ]}
                />
            </main>
        </div>
    );
};

export default DashboardPage;
