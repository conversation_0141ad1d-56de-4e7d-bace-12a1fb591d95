import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { logger } from '@/infrastructure/logging';
import type { Floor } from '@/infrastructure/api/geography/floors/types';
import { mockFloors, getFloorsByBuildingId } from '@/infrastructure/api/geography/floors/mock-floors';
import { useBuildingStore } from './building.store'; // Import the building store

// ---------------------- types ----------------------
export type FloorStoreType = FloorState & FloorActions;
// ---------------------- state ----------------------
interface FloorState {
    floors: Floor[];
    isLoading: boolean;
    selectedFloorId: number | null;
    isInitialized: boolean;
}
// ---------------------- default state ----------------------
const DEFAULT_STATE: FloorState = {
    floors: [],
    isLoading: false,
    selectedFloorId: null,
    isInitialized: false,
};
// ---------------------- actions ----------------------
interface FloorActions {
    loadFloors: () => Promise<void>;
    loadFloorsByBuilding: (buildingId: number) => Promise<void>;
    setSelectedFloor: (id: number) => void;
    clearSelectedFloor: () => void;
    initialize: () => void;
    subscribeToBuildingStore: () => void;
}
// ---------------------- store ----------------------
const _floorStore = (instanceId: string): StateCreator<FloorStoreType> => {
    let initialized = false; // Prevent double initialization
    let unsubscribeBuilding: (() => void) | null = null;
    return (set, get): FloorStoreType => ({
        ...DEFAULT_STATE,
        loadFloors: async () => {
            set((state: FloorState) => ({ ...state, isLoading: true }));
            try {
                // Instead of API call, use mock data
                const res = mockFloors;
                set((state: FloorState) => ({
                    ...state,
                    floors: res,
                    isLoading: false,
                }));
                logger.info(`floorStore(${instanceId}): loadFloors: loaded mock data`, res);
            } catch (error) {
                logger.error(`floorStore(${instanceId}): loadFloors: error: `, error as Error);
                set((state: FloorState) => ({ ...state, isLoading: false }));
            }
        },
        loadFloorsByBuilding: async (buildingId: number) => {
            set((state: FloorState) => ({ ...state, isLoading: true }));
            try {
                const res = getFloorsByBuildingId(buildingId);
                set((state: FloorState) => ({
                    ...state,
                    floors: res,
                    isLoading: false,
                    selectedFloorId: res.length > 0 ? res[0].id : null,
                }));
                logger.info(
                    `floorStore(${instanceId}): loadFloorsByBuilding: loaded floors for building ${buildingId}`,
                    res,
                );

                // Auto-select first floor if buildingId === 1 and there are floors
                if (res.length > 0) {
                    logger.info(`Auto-selected first floor: ${res[0].id}`);
                }
            } catch (error) {
                logger.error(`floorStore(${instanceId}): loadFloors: error: `, error as Error);
                set((state: FloorState) => ({ ...state, isLoading: false }));
            }
        },
        setSelectedFloor: (id: number) => {
            set((state: FloorState) => ({ ...state, selectedFloorId: id }));
            logger.info(`floorStore(${instanceId}): setSelectedFloor:`, id);
        },
        clearSelectedFloor: () => {
            set((state: FloorState) => ({ ...state, selectedFloorId: null }));
            logger.info(`floorStore(${instanceId}): clearSelectedFloor`);
        },
        initialize: () => {
            const { floors, isLoading, isInitialized } = get();
            if (!initialized && !isInitialized && floors.length === 0 && !isLoading) {
                initialized = true;
                set((state) => ({ ...state, isInitialized: true }));
                get().loadFloors();
            }
        },
        subscribeToBuildingStore: () => {
            // Unsubscribe previous if any
            if (unsubscribeBuilding) unsubscribeBuilding();
            // Subscribe to building selection changes
            unsubscribeBuilding = useBuildingStore.subscribe(
                (state) => state.selectedBuilding,
                (selectedBuilding) => {
                    if (selectedBuilding && selectedBuilding.id) {
                        get().loadFloorsByBuilding(selectedBuilding.id);
                    } else {
                        // If no building selected, clear floors and selectedFloorId
                        set((state: FloorState) => ({
                            ...state,
                            floors: [],
                            selectedFloorId: null,
                        }));
                    }
                },
                { fireImmediately: true },
            );
        },
    });
};
// ---------------------- store instances -------------------
export const useFloorStore = create<FloorStoreType>()(
    subscribeWithSelector(
        devtools(_floorStore('floorStore'), {
            name: 'floorStore',
            serialize: { options: { maxDepth: 5 } },
        }),
    ),
);
export const createFloorStore = (instanceId: string) =>
    create<FloorStoreType>()(
        subscribeWithSelector(devtools(_floorStore(instanceId), { name: `floor-store-${instanceId}` })),
    );
