import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type { System } from '@/infrastructure/api/systems/types';
import { mockSystems } from '@/infrastructure/api/systems/mock-systems';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type SystemsStoreType = SystemsState & SystemsActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------
interface SystemsState {
    systems: System[];
    isLoading: boolean;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: SystemsState = {
    systems: [],
    isLoading: false,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface SystemsActions {
    loadSystems: () => Promise<void>;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _systemsStore = (instanceId: string): StateCreator<SystemsStoreType> => {
    return (set): SystemsStoreType => ({
        ...DEFAULT_STATE,

        loadSystems: async () => {
            set((state: SystemsState) => ({ ...state, isLoading: true }));

            try {
                // Instead of API call, use mock data
                const res = mockSystems;

                set((state: SystemsState) => ({
                    ...state,
                    systems: res.systems,
                    isLoading: false,
                }));

                logger.info(`systemsStore(${instanceId}): loadSystems: loaded mock data`, res);
            } catch (error) {
                logger.error(`systemsStore(${instanceId}): loadSystems: error: `, error as Error);
                set((state: SystemsState) => ({ ...state, isLoading: false }));
            }
        },
    });
};

// ----------------------------------------------------------
// ---------------------- store instances -------------------
// ----------------------------------------------------------
export const useSystemsStore = create<SystemsStoreType>()(
    subscribeWithSelector(devtools(_systemsStore('global'), { name: 'systems-store-global' })),
);

export const createSystemsStore = (instanceId: string) =>
    create<SystemsStoreType>()(
        subscribeWithSelector(devtools(_systemsStore(instanceId), { name: `systems-store-${instanceId}` })),
    );
