import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type { Event } from '@/infrastructure/api/events/types';
import { mockEvents } from '@/infrastructure/api/events/mock-events';
import { useBuildingStore } from './building.store';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type EventsStoreType = EventsState & EventsActions;

export interface FloorAlertSummary {
    floorId: number;
    floorName: string;
    totalAlerts: number;
    criticalAlerts: number;
    alerts: Event[];
    deviceTypes: {
        fire: number;
        door: number;
        camera: number;
        gate: number;
    };
}

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------
interface EventsState {
    events: Event[];
    isLoading: boolean;
    // Floor-specific alert state
    floorAlerts: FloorAlertSummary[];
    selectedBuildingId: number | null;
    totalAlertsInBuilding: number;
    isInitialized: boolean;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: EventsState = {
    events: [],
    isLoading: false,
    // Floor-specific alert defaults
    floorAlerts: [],
    selectedBuildingId: null,
    totalAlertsInBuilding: 0,
    isInitialized: false,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface EventsActions {
    loadAllEvents: () => Promise<void>;
    loadAlertEvents: () => Promise<void>;
    // Floor-specific alert actions
    loadAlertsByBuilding: (buildingId: number) => Promise<void>;
    getAlertsByFloor: (floorId: number) => Event[];
    getCriticalAlertsByFloor: (floorId: number) => Event[];
    initialize: () => void;
    subscribeToBuildingStore: () => void;
    clearAlerts: () => void;
}

// ---------------------------------------------------
// ---------------------- helpers --------------------
// ---------------------------------------------------
const isCriticalAlert = (event: Event): boolean => {
    const criticalStatuses = ['alarm_triggered', 'Active Incidents', 'Unauthorized attempts', 'offline'];
    return event.isAlert && criticalStatuses.includes(event.status);
};

const groupAlertsByFloor = (events: Event[]): FloorAlertSummary[] => {
    const floorMap = new Map<number, FloorAlertSummary>();

    events.forEach((event) => {
        if (!event.isAlert) return;

        if (!floorMap.has(event.floorId)) {
            floorMap.set(event.floorId, {
                floorId: event.floorId,
                floorName: `Floor ${event.floorId}`, // This could be enhanced with actual floor names
                totalAlerts: 0,
                criticalAlerts: 0,
                alerts: [],
                deviceTypes: {
                    fire: 0,
                    door: 0,
                    camera: 0,
                    gate: 0,
                },
            });
        }

        const floorSummary = floorMap.get(event.floorId)!;
        floorSummary.totalAlerts++;
        floorSummary.alerts.push(event);
        floorSummary.deviceTypes[event.deviceType]++;

        if (isCriticalAlert(event)) {
            floorSummary.criticalAlerts++;
        }
    });

    return Array.from(floorMap.values()).sort((a, b) => a.floorId - b.floorId);
};

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _eventsStore = (instanceId: string): StateCreator<EventsStoreType> => {
    let initialized = false;
    let unsubscribeBuilding: (() => void) | null = null;

    return (set, get): EventsStoreType => ({
        ...DEFAULT_STATE,

        // load all events
        loadAllEvents: async () => {
            set({ isLoading: true });
            try {
                const res = mockEvents;
                const allAlerts = res.events.filter((event) => event.isAlert);
                const floorAlerts = groupAlertsByFloor(allAlerts);

                set({
                    events: res.events,
                    floorAlerts,
                    selectedBuildingId: null,
                    totalAlertsInBuilding: allAlerts.length,
                    isLoading: false,
                });
                logger.info(`eventsStore(${instanceId}): loadAllEvents loaded mock data`, res);
            } catch (error) {
                logger.error(`eventsStore(${instanceId}): loadAllEvents error`, error as Error);
                set({ isLoading: false });
            }
        },

        // load only alert events
        loadAlertEvents: async () => {
            set({ isLoading: true });
            try {
                const res = mockEvents;
                const alerts = res.events.filter((e) => e.isAlert);
                const floorAlerts = groupAlertsByFloor(alerts);

                set({
                    events: alerts,
                    floorAlerts,
                    selectedBuildingId: null,
                    totalAlertsInBuilding: alerts.length,
                    isLoading: false,
                });
                logger.info(`eventsStore(${instanceId}): loadAlertEvents loaded only alerts`, alerts);
            } catch (error) {
                logger.error(`eventsStore(${instanceId}): loadAlertEvents error`, error as Error);
                set({ isLoading: false });
            }
        },

        // Load alerts for specific building
        loadAlertsByBuilding: async (buildingId: number) => {
            set({ isLoading: true });
            try {
                const buildingEvents = mockEvents.events.filter(
                    (event) => event.buildingId === buildingId && event.isAlert,
                );

                const floorAlerts = groupAlertsByFloor(buildingEvents);
                const totalAlerts = buildingEvents.length;

                set({
                    events: buildingEvents,
                    floorAlerts,
                    selectedBuildingId: buildingId,
                    totalAlertsInBuilding: totalAlerts,
                    isLoading: false,
                });

                logger.info(
                    `eventsStore(${instanceId}): loadAlertsByBuilding: loaded ${totalAlerts} alerts for building ${buildingId}`,
                    { floorAlerts, totalAlerts },
                );
            } catch (error) {
                logger.error(`eventsStore(${instanceId}): loadAlertsByBuilding error`, error as Error);
                set({ isLoading: false });
            }
        },

        // Get alerts for specific floor
        getAlertsByFloor: (floorId: number) => {
            const { floorAlerts } = get();
            const floorSummary = floorAlerts.find((f) => f.floorId === floorId);
            return floorSummary?.alerts || [];
        },

        // Get critical alerts for specific floor
        getCriticalAlertsByFloor: (floorId: number) => {
            const { floorAlerts } = get();
            const floorSummary = floorAlerts.find((f) => f.floorId === floorId);
            return floorSummary?.alerts.filter(isCriticalAlert) || [];
        },

        // Clear all alerts
        clearAlerts: () => {
            set({
                events: [],
                floorAlerts: [],
                selectedBuildingId: null,
                totalAlertsInBuilding: 0,
            });
            logger.info(`eventsStore(${instanceId}): clearAlerts: cleared all alerts`);
        },

        // Initialize store
        initialize: () => {
            const { floorAlerts, isLoading, isInitialized } = get();
            if (!initialized && !isInitialized && floorAlerts.length === 0 && !isLoading) {
                initialized = true;
                set({ isInitialized: true });
                get().loadAlertEvents();
            }
        },

        // Subscribe to building store changes
        subscribeToBuildingStore: () => {
            // Unsubscribe previous if any
            if (unsubscribeBuilding) unsubscribeBuilding();

            // Subscribe to building selection changes
            unsubscribeBuilding = useBuildingStore.subscribe(
                (state) => state.selectedBuilding,
                (selectedBuilding) => {
                    if (selectedBuilding && selectedBuilding.id) {
                        get().loadAlertsByBuilding(selectedBuilding.id);
                    } else {
                        // If no building selected, load all alerts
                        get().loadAlertEvents();
                    }
                },
                { fireImmediately: true },
            );
        },
    });
};

// ----------------------------------------------------------
// ---------------------- store instances -------------------
// ----------------------------------------------------------
export const useEventsStore = create<EventsStoreType>()(
    subscribeWithSelector(devtools(_eventsStore('global'), { name: 'events-store-global' })),
);

export const createEventsStore = (instanceId: string) =>
    create<EventsStoreType>()(
        subscribeWithSelector(devtools(_eventsStore(instanceId), { name: `events-store-${instanceId}` })),
    );
