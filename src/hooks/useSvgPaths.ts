// hooks/useSvgCleanPaths.ts
import { useEffect, useState } from 'react';

export interface SvgPath {
    d: string;
    fill?: string;
    stroke?: string;
}

export function useSvgCleanPaths(fileName: string): SvgPath[] {
    const [paths, setPaths] = useState<SvgPath[]>([]);

    useEffect(() => {
        if (!fileName) return;

        async function fetchSvg() {
            try {
                const res = await fetch(`/svg/${fileName}.svg`);
                const text = await res.text();

                const parser = new DOMParser();
                const doc = parser.parseFromString(text, 'image/svg+xml');
                const pathEls = Array.from(doc.querySelectorAll('path'));

                const cleanPaths = pathEls
                    .map((p) => ({
                        d: p.getAttribute('d') || '',
                        fill: p.getAttribute('fill') || undefined,
                        stroke: p.getAttribute('stroke') || undefined,
                    }))
                    // filter out unwanted ones
                    .filter(
                        (p) =>
                            p.d &&
                            !p.d.startsWith('M0,0') && // skip background
                            p.fill !== 'none', // skip invisible paths
                    );

                setPaths(cleanPaths);
            } catch (err) {
                console.error('Error loading SVG:', err);
            }
        }

        fetchSvg();
    }, [fileName]);

    return paths;
}
