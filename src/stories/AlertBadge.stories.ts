import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import AlertBadge from '@/components/common/ui/AlertBadge';

const meta = {
    title: 'Components/AlertBadge',
    component: AlertBadge,
    parameters: {
        layout: 'centered',
        backgrounds: {
            default: 'dark',
            values: [
                { name: 'dark', value: '#1a1a1a' },
                { name: 'light', value: '#ffffff' },
            ],
        },
        docs: {
            description: {
                component:
                    'A clean badge component for displaying numeric values with alert states. Features red color and pulse animation for critical alerts.',
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        value: {
            control: { type: 'text' },
            description: 'The value to display in the badge',
        },
        isAlert: {
            control: { type: 'boolean' },
            description: 'Whether the badge should display in alert state (red color with pulse animation)',
        },
        size: {
            control: { type: 'select' },
            options: ['sm', 'md', 'lg'],
            description: 'Size of the badge',
        },
        animate: {
            control: { type: 'boolean' },
            description: 'Whether to show pulse animation when in alert state (default: true)',
        },
        className: {
            control: { type: 'text' },
            description: 'Additional CSS classes to apply',
        },
    },
} satisfies Meta<typeof AlertBadge>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default story
export const Default: Story = {
    args: {
        value: '200',
        isAlert: false,
        size: 'md',
    },
};

// Alert state
export const Alert: Story = {
    args: {
        value: '2',
        isAlert: true,
        size: 'md',
    },
};

// Different sizes - Normal state
export const SmallNormal: Story = {
    args: {
        value: '42',
        isAlert: false,
        size: 'sm',
    },
};

export const MediumNormal: Story = {
    args: {
        value: '156',
        isAlert: false,
        size: 'md',
    },
};

export const LargeNormal: Story = {
    args: {
        value: '999',
        isAlert: false,
        size: 'lg',
    },
};

// Different sizes - Alert state
export const SmallAlert: Story = {
    args: {
        value: '1',
        isAlert: true,
        size: 'sm',
    },
};

export const MediumAlert: Story = {
    args: {
        value: '5',
        isAlert: true,
        size: 'md',
    },
};

export const LargeAlert: Story = {
    args: {
        value: '12',
        isAlert: true,
        size: 'lg',
    },
};

// High numbers
export const HighNumber: Story = {
    args: {
        value: '99+',
        isAlert: false,
        size: 'md',
    },
};

export const HighNumberAlert: Story = {
    args: {
        value: '99+',
        isAlert: true,
        size: 'md',
    },
};

// Playground for interactive testing
export const Playground: Story = {
    args: {
        value: '10',
        isAlert: false,
        size: 'md',
    },
};

// Animation variations
export const AlertWithAnimation: Story = {
    args: {
        value: '5',
        isAlert: true,
        animate: true,
        size: 'md',
    },
};

export const AlertWithoutAnimation: Story = {
    args: {
        value: '5',
        isAlert: true,
        animate: false,
        size: 'md',
    },
};
