import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import React from 'react';

// Simple div-based spinner component for demonstration
const DivSpinner = ({ 
  size = 24, 
  speed = 1, 
  color = '#3b82f6',
  style = 'dots'
}: {
  size?: number;
  speed?: number;
  color?: string;
  style?: 'dots' | 'ring' | 'pulse' | 'bars';
}) => {
  const baseStyle: React.CSSProperties = {
    width: size,
    height: size,
    display: 'inline-block',
  };

  const animationDuration = `${speed}s`;

  if (style === 'dots') {
    return (
      <div
        style={{
          ...baseStyle,
          position: 'relative',
        }}
      >
        <style>{`
          @keyframes spin-dots {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
          .spinner-dots {
            animation: spin-dots ${animationDuration} linear infinite;
          }
          .spinner-dots::before,
          .spinner-dots::after {
            content: '';
            position: absolute;
            width: ${size * 0.2}px;
            height: ${size * 0.2}px;
            background: ${color};
            border-radius: 50%;
          }
          .spinner-dots::before {
            top: ${size * 0.1}px;
            left: 50%;
            transform: translateX(-50%);
          }
          .spinner-dots::after {
            bottom: ${size * 0.1}px;
            left: 50%;
            transform: translateX(-50%);
          }
        `}</style>
        <div
          className="spinner-dots"
          style={{
            width: '100%',
            height: '100%',
            position: 'relative',
          }}
        >
          <div
            style={{
              position: 'absolute',
              left: '50%',
              top: '50%',
              width: size * 0.2,
              height: size * 0.2,
              background: color,
              borderRadius: '50%',
              transform: 'translate(-50%, -50%)',
            }}
          />
        </div>
      </div>
    );
  }

  if (style === 'ring') {
    return (
      <div
        style={{
          ...baseStyle,
          border: `${size * 0.1}px solid #f3f3f3`,
          borderTop: `${size * 0.1}px solid ${color}`,
          borderRadius: '50%',
          animation: `spin ${animationDuration} linear infinite`,
        }}
      >
        <style>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  if (style === 'pulse') {
    return (
      <div
        style={{
          ...baseStyle,
          background: color,
          borderRadius: '50%',
          animation: `pulse ${animationDuration} ease-in-out infinite`,
        }}
      >
        <style>{`
          @keyframes pulse {
            0%, 100% { 
              transform: scale(1);
              opacity: 1;
            }
            50% { 
              transform: scale(1.2);
              opacity: 0.7;
            }
          }
        `}</style>
      </div>
    );
  }

  if (style === 'bars') {
    return (
      <div
        style={{
          ...baseStyle,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <style>{`
          @keyframes bar-scale {
            0%, 40%, 100% { transform: scaleY(0.4); }
            20% { transform: scaleY(1); }
          }
        `}</style>
        {[0, 1, 2].map((i) => (
          <div
            key={i}
            style={{
              width: size * 0.15,
              height: size * 0.8,
              background: color,
              borderRadius: size * 0.05,
              animation: `bar-scale ${animationDuration} ease-in-out infinite`,
              animationDelay: `${i * 0.1}s`,
            }}
          />
        ))}
      </div>
    );
  }

  return null;
};

const meta: Meta = {
  title: 'Showcase/Div Element Spinner Examples',
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Pure CSS div-based spinner examples with rotation effects for various UI contexts.',
      },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic div spinners
export const BasicDivSpinners: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      gap: '32px', 
      alignItems: 'center',
      flexWrap: 'wrap'
    }}>
      <div style={{ textAlign: 'center' }}>
        <DivSpinner style="ring" size={32} speed={1} />
        <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>Ring</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <DivSpinner style="dots" size={32} speed={1.2} />
        <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>Dots</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <DivSpinner style="pulse" size={32} speed={1.5} />
        <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>Pulse</div>
      </div>
      <div style={{ textAlign: 'center' }}>
        <DivSpinner style="bars" size={32} speed={1} />
        <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>Bars</div>
      </div>
    </div>
  ),
};

// Color variations
export const ColorVariations: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      gap: '24px', 
      alignItems: 'center',
      flexWrap: 'wrap'
    }}>
      {[
        { color: '#3b82f6', name: 'Blue' },
        { color: '#ef4444', name: 'Red' },
        { color: '#10b981', name: 'Green' },
        { color: '#f59e0b', name: 'Yellow' },
        { color: '#8b5cf6', name: 'Purple' },
      ].map(({ color, name }) => (
        <div key={color} style={{ textAlign: 'center' }}>
          <DivSpinner style="ring" size={28} speed={1.2} color={color} />
          <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>{name}</div>
        </div>
      ))}
    </div>
  ),
};

// Size variations
export const SizeVariations: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      gap: '24px', 
      alignItems: 'center',
      flexWrap: 'wrap'
    }}>
      {[16, 24, 32, 40, 48].map(size => (
        <div key={size} style={{ textAlign: 'center' }}>
          <DivSpinner style="ring" size={size} speed={1.2} />
          <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>{size}px</div>
        </div>
      ))}
    </div>
  ),
};

// Speed variations
export const SpeedVariations: Story = {
  render: () => (
    <div style={{ 
      display: 'flex', 
      gap: '32px', 
      alignItems: 'center',
      flexWrap: 'wrap'
    }}>
      {[
        { speed: 0.5, label: 'Very Fast' },
        { speed: 1, label: 'Fast' },
        { speed: 1.5, label: 'Normal' },
        { speed: 2, label: 'Slow' },
        { speed: 3, label: 'Very Slow' }
      ].map(({ speed, label }) => (
        <div key={speed} style={{ textAlign: 'center' }}>
          <DivSpinner style="ring" size={32} speed={speed} />
          <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>{label}</div>
          <div style={{ fontSize: '10px', color: '#999' }}>{speed}s</div>
        </div>
      ))}
    </div>
  ),
};

// Real-world div integration examples
export const RealWorldExamples: Story = {
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px', width: '500px' }}>
      {/* Loading button */}
      <button
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          padding: '12px 24px',
          backgroundColor: '#3b82f6',
          color: 'white',
          border: 'none',
          borderRadius: '8px',
          fontSize: '14px',
          fontWeight: '500',
          cursor: 'not-allowed',
          opacity: 0.8,
        }}
        disabled
      >
        <DivSpinner style="ring" size={16} speed={1} color="white" />
        Processing...
      </button>

      {/* Loading card */}
      <div style={{
        padding: '20px',
        border: '1px solid #e5e7eb',
        borderRadius: '12px',
        backgroundColor: '#ffffff',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '16px' }}>
          <DivSpinner style="pulse" size={20} speed={1.5} color="#6b7280" />
          <span style={{ color: '#6b7280', fontSize: '14px' }}>Loading content...</span>
        </div>
        <div style={{ height: '60px', backgroundColor: '#f3f4f6', borderRadius: '6px' }} />
      </div>

      {/* Form field validation */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <label style={{ fontSize: '14px', fontWeight: '500', color: '#374151' }}>
          Username
        </label>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          padding: '10px 12px',
          border: '2px solid #fbbf24',
          borderRadius: '6px',
          backgroundColor: '#fffbeb',
        }}>
          <input 
            type="text" 
            placeholder="Checking availability..."
            style={{ 
              border: 'none', 
              outline: 'none', 
              flex: 1,
              fontSize: '14px',
              backgroundColor: 'transparent',
            }}
            disabled
          />
          <DivSpinner style="bars" size={16} speed={1.2} color="#f59e0b" />
        </div>
      </div>

      {/* Status indicator */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '12px',
        padding: '12px 16px',
        backgroundColor: '#ecfdf5',
        borderRadius: '8px',
        border: '1px solid #10b981',
      }}>
        <DivSpinner style="dots" size={18} speed={1} color="#10b981" />
        <div>
          <div style={{ fontSize: '14px', fontWeight: '500', color: '#065f46' }}>
            Syncing Data
          </div>
          <div style={{ fontSize: '12px', color: '#047857' }}>
            Please wait while we update your information...
          </div>
        </div>
      </div>

      {/* Navigation loading */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '16px 20px',
        backgroundColor: '#ffffff',
        border: '1px solid #d1d5db',
        borderRadius: '10px',
        cursor: 'pointer',
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <div style={{
            width: '40px',
            height: '40px',
            backgroundColor: '#f3f4f6',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}>
            📊
          </div>
          <div>
            <div style={{ fontSize: '14px', fontWeight: '500', color: '#111827' }}>
              Analytics Dashboard
            </div>
            <div style={{ fontSize: '12px', color: '#6b7280' }}>
              Loading latest reports...
            </div>
          </div>
        </div>
        <DivSpinner style="ring" size={20} speed={1.2} color="#6b7280" />
      </div>
    </div>
  ),
};