import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { SmartSpinnerInline } from '../components/common/SmartSpinner';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
const meta = {
    title: 'Components/SmartSpinner/Inline',
    component: SmartSpinnerInline,
    parameters: {
        // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
        layout: 'centered',
    },
    // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
    tags: ['autodocs'],
    // More on argTypes: https://storybook.js.org/docs/api/argtypes
    argTypes: {
        size: { 
            control: { type: 'range', min: 12, max: 48, step: 2 },
            description: 'Size of the inline spinner in pixels'
        },
        speed: { 
            control: { type: 'range', min: 0.5, max: 3, step: 0.1 },
            description: 'Animation speed in seconds'
        },
        className: { 
            control: 'text',
            description: 'Additional CSS classes'
        },
    },
} satisfies Meta<typeof SmartSpinnerInline>;

export default meta;
type Story = StoryObj<typeof meta>;

// More on writing stories with args: https://storybook.js.org/docs/writing-stories/args
export const Default: Story = {
    args: {
        size: 20,
        speed: 1.2,
    },
};

export const Small: Story = {
    args: {
        size: 16,
        speed: 1.2,
    },
};

export const Large: Story = {
    args: {
        size: 32,
        speed: 1.2,
    },
};

export const Fast: Story = {
    args: {
        size: 20,
        speed: 0.8,
    },
};

export const Slow: Story = {
    args: {
        size: 20,
        speed: 2.5,
    },
};

export const WithRotation: Story = {
    args: {
        size: 24,
        speed: 1.0,
    },
    parameters: {
        docs: {
            description: {
                story: 'Inline spinner with rotation effect - combines zoom and rotation animations for dynamic loading indication.',
            },
        },
    },
};