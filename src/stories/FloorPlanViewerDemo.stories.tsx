import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { FloorPlanViewerDemo } from '@/components/features/floor-plan-demo/FloorPlanViewerDemo';
import type { DeviceMarker, AlertMarker } from '@/components/features/floor-plan-demo/FloorPlanViewerDemo';

const meta: Meta<typeof FloorPlanViewerDemo> = {
    title: 'Components/FloorPlanViewerDemo',
    component: FloorPlanViewerDemo,
    parameters: {
        layout: 'fullscreen',
        docs: {
            description: {
                component:
                    'A comprehensive floor plan viewer component with pan/zoom functionality, device markers, and alert indicators. Uses 2dFloorPlan.png as the reference floor plan and SVG icons for device markers.',
            },
        },
    },
    argTypes: {
        floorPlanUrl: {
            control: 'text',
            description: 'URL to the floor plan image',
        },
        devices: {
            control: 'object',
            description: 'Array of device markers to display on the floor plan',
        },
        alerts: {
            control: 'object',
            description: 'Array of alert markers to display on the floor plan',
        },
        selectedBuilding: {
            control: 'text',
            description: 'Name of the selected building',
        },
        selectedFloor: {
            control: 'text',
            description: 'Name of the selected floor',
        },
        onDeviceClick: {
            action: 'device-clicked',
            description: 'Callback when a device marker is clicked',
        },
        onAlertClick: {
            action: 'alert-clicked',
            description: 'Callback when an alert marker is clicked',
        },
    },
};

export default meta;
type Story = StoryObj<typeof FloorPlanViewerDemo>;

// Sample device data
const sampleDevices: DeviceMarker[] = [
    {
        id: 'camera-001',
        position: { x: 15, y: 10 },
        type: 'cctv',
        status: 'normal',
        label: 'Security Camera 1',
        metadata: {
            model: 'HD-2000',
            lastSeen: '2024-01-15T10:30:00Z',
            resolution: '1080p',
        },
    },
    {
        id: 'camera-002',
        position: { x: 40, y: 15 },
        type: 'cctv',
        status: 'warning',
        label: 'Security Camera 2',
        metadata: {
            model: 'HD-2000',
            lastSeen: '2024-01-15T09:45:00Z',
            resolution: '1080p',
            issue: 'Low battery',
        },
    },
    {
        id: 'access-001',
        position: { x: 25, y: 20 },
        type: 'access-control',
        status: 'normal',
        label: 'Access Control Panel',
        metadata: {
            cardReaders: 2,
            lastAccess: '2024-01-15T10:35:00Z',
        },
    },
    {
        id: 'gate-001',
        position: { x: 10, y: 30 },
        type: 'gate-barrier',
        status: 'normal',
        label: 'Main Gate',
        metadata: {
            status: 'closed',
            lastActivity: '2024-01-15T08:15:00Z',
        },
    },
    {
        id: 'fire-001',
        position: { x: 35, y: 28 },
        type: 'fire-alarm',
        status: 'offline',
        label: 'Fire Detector 1',
        metadata: {
            batteryLevel: '0%',
            lastTest: '2024-01-10T14:00:00Z',
        },
    },
    {
        id: 'access-002',
        position: { x: 50, y: 25 },
        type: 'access-control',
        status: 'danger',
        label: 'Emergency Access',
        metadata: {
            status: 'triggered',
            triggeredAt: '2024-01-15T10:40:00Z',
        },
    },
];

// Sample alert data
const sampleAlerts: AlertMarker[] = [
    {
        id: 'alert-001',
        position: { x: 40, y: 15 },
        type: 'security',
        severity: 'medium',
        title: 'Camera Battery Low',
        description: 'Security camera battery level is critically low',
        timestamp: new Date('2024-01-15T09:45:00Z'),
        acknowledged: false,
    },
    {
        id: 'alert-002',
        position: { x: 50, y: 25 },
        type: 'access',
        severity: 'critical',
        title: 'Emergency Access Triggered',
        description: 'Emergency access control has been activated',
        timestamp: new Date('2024-01-15T10:40:00Z'),
        acknowledged: false,
    },
    {
        id: 'alert-003',
        position: { x: 35, y: 28 },
        type: 'fire',
        severity: 'high',
        title: 'Fire Detector Offline',
        description: 'Fire detection system is not responding',
        timestamp: new Date('2024-01-15T08:30:00Z'),
        acknowledged: true,
    },
];

// Default story with sample data
export const Default: Story = {
    args: {
        floorPlanUrl: '/2dFloorPlan.png',
        devices: sampleDevices,
        alerts: sampleAlerts,
        selectedBuilding: 'Main Office Building',
        selectedFloor: 'Ground Floor',
    },
};

// Story with no devices or alerts
export const EmptyPlan: Story = {
    args: {
        floorPlanUrl: '/2dFloorPlan.png',
        devices: [],
        alerts: [],
        selectedBuilding: 'New Building',
        selectedFloor: 'Floor 1',
    },
};

// Story with only devices (no alerts)
export const DevicesOnly: Story = {
    args: {
        floorPlanUrl: '/2dFloorPlan.png',
        devices: sampleDevices,
        alerts: [],
        selectedBuilding: 'Security Center',
        selectedFloor: 'Monitoring Floor',
    },
};

// Story with only alerts (no devices)
export const AlertsOnly: Story = {
    args: {
        floorPlanUrl: '/2dFloorPlan.png',
        devices: [],
        alerts: sampleAlerts,
        selectedBuilding: 'Emergency Center',
        selectedFloor: 'Alert Floor',
    },
};

// Story with missing image (error state)
export const MissingImage: Story = {
    args: {
        floorPlanUrl: '/non-existent-plan.png',
        devices: sampleDevices.slice(0, 3),
        alerts: sampleAlerts.slice(0, 1),
        selectedBuilding: 'Test Building',
        selectedFloor: 'Test Floor',
    },
};

// Story with many devices and alerts
export const HighDensity: Story = {
    args: {
        floorPlanUrl: '/2dFloorPlan.png',
        devices: [
            ...sampleDevices,
            // Add more devices for density
            {
                id: 'camera-003',
                position: { x: 20, y: 35 },
                type: 'cctv',
                status: 'normal',
                label: 'Camera 3',
            },
            {
                id: 'access-003',
                position: { x: 45, y: 10 },
                type: 'access-control',
                status: 'warning',
                label: 'Access Panel 2',
            },
            {
                id: 'gate-002',
                position: { x: 30, y: 5 },
                type: 'gate-barrier',
                status: 'normal',
                label: 'Gate 2',
            },
            {
                id: 'fire-002',
                position: { x: 15, y: 25 },
                type: 'fire-alarm',
                status: 'normal',
                label: 'Fire Detector 2',
            },
        ],
        alerts: [
            ...sampleAlerts,
            {
                id: 'alert-004',
                position: { x: 20, y: 35 },
                type: 'security',
                severity: 'low',
                title: 'Motion Detected',
                description: 'Unusual motion detected in restricted area',
                timestamp: new Date('2024-01-15T10:45:00Z'),
                acknowledged: false,
            },
            {
                id: 'alert-005',
                position: { x: 45, y: 10 },
                type: 'system',
                severity: 'medium',
                title: 'System Calibration Needed',
                description: 'Access control system requires calibration',
                timestamp: new Date('2024-01-15T09:30:00Z'),
                acknowledged: false,
            },
        ],
        selectedBuilding: 'High Security Facility',
        selectedFloor: 'Main Operations Floor',
    },
};

// Story demonstrating different device statuses
export const DeviceStatuses: Story = {
    args: {
        floorPlanUrl: '/2dFloorPlan.png',
        devices: [
            {
                id: 'normal-device',
                position: { x: 10, y: 10 },
                type: 'cctv',
                status: 'normal',
                label: 'Normal Status',
            },
            {
                id: 'warning-device',
                position: { x: 20, y: 10 },
                type: 'access-control',
                status: 'warning',
                label: 'Warning Status',
            },
            {
                id: 'danger-device',
                position: { x: 30, y: 10 },
                type: 'fire-alarm',
                status: 'danger',
                label: 'Danger Status',
            },
            {
                id: 'offline-device',
                position: { x: 40, y: 10 },
                type: 'gate-barrier',
                status: 'offline',
                label: 'Offline Status',
            },
        ],
        alerts: [],
        selectedBuilding: 'Status Demo Building',
        selectedFloor: 'Device Status Floor',
    },
};

// Story demonstrating different alert severities
export const AlertSeverities: Story = {
    args: {
        floorPlanUrl: '/2dFloorPlan.png',
        devices: [],
        alerts: [
            {
                id: 'low-alert',
                position: { x: 10, y: 20 },
                type: 'security',
                severity: 'low',
                title: 'Low Severity Alert',
                description: 'This is a low severity security alert',
                timestamp: new Date('2024-01-15T10:00:00Z'),
                acknowledged: false,
            },
            {
                id: 'medium-alert',
                position: { x: 20, y: 20 },
                type: 'system',
                severity: 'medium',
                title: 'Medium Severity Alert',
                description: 'This is a medium severity system alert',
                timestamp: new Date('2024-01-15T10:10:00Z'),
                acknowledged: false,
            },
            {
                id: 'high-alert',
                position: { x: 30, y: 20 },
                type: 'fire',
                severity: 'high',
                title: 'High Severity Alert',
                description: 'This is a high severity fire alert',
                timestamp: new Date('2024-01-15T10:20:00Z'),
                acknowledged: false,
            },
            {
                id: 'critical-alert',
                position: { x: 40, y: 20 },
                type: 'access',
                severity: 'critical',
                title: 'Critical Severity Alert',
                description: 'This is a critical severity access alert',
                timestamp: new Date('2024-01-15T10:30:00Z'),
                acknowledged: false,
            },
            {
                id: 'acknowledged-alert',
                position: { x: 25, y: 30 },
                type: 'security',
                severity: 'high',
                title: 'Acknowledged Alert',
                description: 'This alert has been acknowledged by security team',
                timestamp: new Date('2024-01-15T09:00:00Z'),
                acknowledged: true,
            },
        ],
        selectedBuilding: 'Alert Demo Building',
        selectedFloor: 'Alert Severity Floor',
    },
};
