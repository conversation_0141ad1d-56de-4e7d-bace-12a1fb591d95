import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import { SmartSpinnerInline } from '../components/common/SmartSpinner';

const meta: Meta<typeof SmartSpinnerInline> = {
  title: 'Showcase/Inline Spinner Rotation Effects',
  component: SmartSpinnerInline,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Comprehensive showcase of inline spinner rotation effects with various configurations and div element examples.',
      },
    },
  },
  argTypes: {
    size: {
      control: { type: 'range', min: 12, max: 100, step: 2 },
      description: 'Size of the spinner in pixels',
    },
    speed: {
      control: { type: 'range', min: 0.5, max: 5, step: 0.1 },
      description: 'Animation speed (lower = faster)',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
  args: {
    size: 24,
    speed: 1.2,
    className: '',
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic rotation showcase
export const BasicRotation: Story = {
  args: {
    size: 32,
    speed: 1.2,
  },
  parameters: {
    docs: {
      description: {
        story: 'Basic inline spinner with combined scale and rotation animation effects.',
      },
    },
  },
};

// Fast rotation
export const FastRotation: Story = {
  args: {
    size: 28,
    speed: 0.8,
  },
  parameters: {
    docs: {
      description: {
        story: 'Fast rotating spinner for quick loading states.',
      },
    },
  },
};

// Slow rotation
export const SlowRotation: Story = {
  args: {
    size: 36,
    speed: 2.5,
  },
  parameters: {
    docs: {
      description: {
        story: 'Slow, smooth rotation for relaxed loading experiences.',
      },
    },
  },
};

// Small button spinner
export const ButtonSpinner: Story = {
  args: {
    size: 16,
    speed: 1.0,
  },
  parameters: {
    docs: {
      description: {
        story: 'Small spinner perfect for button loading states.',
      },
    },
  },
  render: (args) => (
    <div style={{ 
      display: 'inline-flex', 
      alignItems: 'center', 
      gap: '8px',
      padding: '8px 16px',
      backgroundColor: '#3b82f6',
      color: 'white',
      borderRadius: '6px',
      fontSize: '14px',
      fontWeight: '500'
    }}>
      <SmartSpinnerInline {...args} />
      Loading...
    </div>
  ),
};

// Multiple sizes showcase
export const SizeShowcase: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Showcase of different spinner sizes with rotation effects.',
      },
    },
  },
  render: () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '24px',
      flexWrap: 'wrap'
    }}>
      {[12, 16, 20, 24, 32, 40, 48].map(size => (
        <div key={size} style={{ 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center', 
          gap: '8px' 
        }}>
          <SmartSpinnerInline size={size} speed={1.2} />
          <span style={{ fontSize: '12px', color: '#666' }}>{size}px</span>
        </div>
      ))}
    </div>
  ),
};

// Speed variations showcase
export const SpeedShowcase: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Showcase of different rotation speeds with the same size.',
      },
    },
  },
  render: () => (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      gap: '32px',
      flexWrap: 'wrap'
    }}>
      {[
        { speed: 0.6, label: 'Very Fast' },
        { speed: 1.0, label: 'Fast' },
        { speed: 1.5, label: 'Normal' },
        { speed: 2.0, label: 'Slow' },
        { speed: 3.0, label: 'Very Slow' }
      ].map(({ speed, label }) => (
        <div key={speed} style={{ 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center', 
          gap: '8px' 
        }}>
          <SmartSpinnerInline size={32} speed={speed} />
          <span style={{ fontSize: '12px', color: '#666' }}>{label}</span>
          <span style={{ fontSize: '10px', color: '#999' }}>{speed}s</span>
        </div>
      ))}
    </div>
  ),
};

// Div element integration examples
export const DivElementExamples: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Examples of inline spinners integrated within div elements for various UI contexts.',
      },
    },
  },
  render: () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px', width: '400px' }}>
      {/* Card with spinner */}
      <div style={{
        padding: '16px',
        border: '1px solid #e5e7eb',
        borderRadius: '8px',
        backgroundColor: '#f9fafb'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <SmartSpinnerInline size={20} speed={1.2} />
          <span>Loading card content...</span>
        </div>
      </div>

      {/* Inline text with spinner */}
      <div style={{ padding: '12px' }}>
        <p style={{ display: 'flex', alignItems: 'center', gap: '8px', margin: 0 }}>
          Processing your request 
          <SmartSpinnerInline size={16} speed={1.0} />
          please wait...
        </p>
      </div>

      {/* Status indicator */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        padding: '8px 12px',
        backgroundColor: '#fef3c7',
        borderRadius: '6px',
        border: '1px solid #f59e0b'
      }}>
        <SmartSpinnerInline size={18} speed={1.5} />
        <span style={{ color: '#92400e', fontSize: '14px' }}>Syncing data...</span>
      </div>

      {/* Navigation item */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        padding: '12px 16px',
        backgroundColor: '#ffffff',
        border: '1px solid #d1d5db',
        borderRadius: '8px'
      }}>
        <span>Dashboard</span>
        <SmartSpinnerInline size={20} speed={1.3} />
      </div>

      {/* Form field with spinner */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        <label style={{ fontSize: '14px', fontWeight: '500', color: '#374151' }}>
          Email Address
        </label>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          padding: '8px 12px',
          border: '1px solid #d1d5db',
          borderRadius: '6px',
          backgroundColor: '#ffffff'
        }}>
          <input 
            type="email" 
            placeholder="Validating email..."
            style={{ 
              border: 'none', 
              outline: 'none', 
              flex: 1,
              fontSize: '14px'
            }}
            disabled
          />
          <SmartSpinnerInline size={16} speed={1.1} />
        </div>
      </div>
    </div>
  ),
};

// Interactive playground
export const InteractivePlayground: Story = {
  args: {
    size: 32,
    speed: 1.2,
  },
  parameters: {
    docs: {
      description: {
        story: 'Interactive playground to experiment with different spinner configurations.',
      },
    },
  },
  render: (args) => (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      gap: '24px',
      padding: '32px',
      border: '2px dashed #e5e7eb',
      borderRadius: '12px',
      backgroundColor: '#fafafa'
    }}>
      <h3 style={{ margin: 0, color: '#374151' }}>Interactive Spinner Playground</h3>
      <SmartSpinnerInline {...args} />
      <div style={{ 
        fontSize: '14px', 
        color: '#6b7280',
        textAlign: 'center',
        maxWidth: '300px'
      }}>
        Use the controls below to adjust size and speed. The spinner combines scale and rotation animations for a dynamic effect.
      </div>
    </div>
  ),
};