import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import MetricDisplay from '@/components/common/ui/MetricDisplay';

const meta = {
    title: 'Components/MetricDisplay',
    component: MetricDisplay,
    parameters: {
        layout: 'centered',
        backgrounds: {
            default: 'dark',
            values: [
                { name: 'dark', value: '#1a1a1a' },
                { name: 'light', value: '#ffffff' },
            ],
        },
        docs: {
            description: {
                component:
                    'A metric display component that shows a label and value with optional alert badge. Supports horizontal and vertical layouts with highlighting.',
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        label: {
            control: { type: 'text' },
            description: 'The label text to display',
        },
        value: {
            control: { type: 'text' },
            description: 'The value to display',
        },
        layout: {
            control: { type: 'select' },
            options: ['horizontal', 'vertical'],
            description: 'Layout orientation',
        },
        highlight: {
            control: { type: 'boolean' },
            description: 'Whether to highlight the value in yellow',
        },
        isAlert: {
            control: { type: 'boolean' },
            description: 'Whether to show the alert badge',
        },
        animate: {
            control: { type: 'boolean' },
            description: 'Whether to show pulse animation when in alert state (default: true)',
        },
        size: {
            control: { type: 'select' },
            options: ['sm', 'md', 'lg'],
            description: 'Size of the alert badge',
        },
        className: {
            control: { type: 'text' },
            description: 'Additional CSS classes for the container',
        },
        labelClassName: {
            control: { type: 'text' },
            description: 'Additional CSS classes for the label',
        },
        valueClassName: {
            control: { type: 'text' },
            description: 'Additional CSS classes for the value',
        },
    },
} satisfies Meta<typeof MetricDisplay>;

export default meta;
type Story = StoryObj<typeof meta>;

// Default horizontal layout
export const Default: Story = {
    args: {
        label: 'Total cameras',
        value: '200',
        layout: 'horizontal',
        highlight: false,
        isAlert: false,
        size: 'md',
    },
};

// Horizontal with alert
export const HorizontalWithAlert: Story = {
    args: {
        label: 'Active Incidents',
        value: '2',
        layout: 'horizontal',
        highlight: false,
        isAlert: true,
        size: 'md',
    },
};

// Vertical layout
export const Vertical: Story = {
    args: {
        label: 'Total barriers',
        value: '200',
        layout: 'vertical',
        highlight: false,
        isAlert: false,
        size: 'md',
    },
};

// Vertical with alert
export const VerticalWithAlert: Story = {
    args: {
        label: 'Unauthorized attempts',
        value: '5',
        layout: 'vertical',
        highlight: false,
        isAlert: true,
        size: 'md',
    },
};

// Highlighted value
export const Highlighted: Story = {
    args: {
        label: 'Critical systems',
        value: '12',
        layout: 'horizontal',
        highlight: true,
        isAlert: false,
        size: 'md',
    },
};

// Highlighted with alert
export const HighlightedWithAlert: Story = {
    args: {
        label: 'System failures',
        value: '3',
        layout: 'horizontal',
        highlight: true,
        isAlert: true,
        size: 'md',
    },
};

// Different badge sizes
export const SmallBadge: Story = {
    args: {
        label: 'Minor alerts',
        value: '1',
        layout: 'horizontal',
        highlight: false,
        isAlert: true,
        size: 'sm',
    },
};

export const LargeBadge: Story = {
    args: {
        label: 'Critical alerts',
        value: '99+',
        layout: 'horizontal',
        highlight: false,
        isAlert: true,
        size: 'lg',
    },
};

// System card examples (matching Figma design)
export const CCTVControl: Story = {
    args: {
        label: 'Total cameras',
        value: '200',
        layout: 'vertical',
        highlight: false,
        isAlert: false,
        size: 'md',
    },
};

export const GateBarriers: Story = {
    args: {
        label: 'Total barriers',
        value: '200',
        layout: 'vertical',
        highlight: false,
        isAlert: false,
        size: 'md',
    },
};

// Playground for interactive testing
export const Playground: Story = {
    args: {
        label: 'Custom metric',
        value: '42',
        layout: 'horizontal',
        highlight: false,
        isAlert: false,
        size: 'md',
    },
};

// Animation variations
export const AlertWithAnimation: Story = {
    args: {
        label: 'Active Incidents',
        value: '3',
        layout: 'horizontal',
        highlight: false,
        isAlert: true,
        animate: true,
        size: 'md',
    },
};

export const AlertWithoutAnimation: Story = {
    args: {
        label: 'Active Incidents',
        value: '3',
        layout: 'horizontal',
        highlight: false,
        isAlert: true,
        animate: false,
        size: 'md',
    },
};

export const VerticalAlertWithAnimation: Story = {
    args: {
        label: 'Unauthorized attempts',
        value: '7',
        layout: 'vertical',
        highlight: false,
        isAlert: true,
        animate: true,
        size: 'md',
    },
};

export const VerticalAlertWithoutAnimation: Story = {
    args: {
        label: 'Unauthorized attempts',
        value: '7',
        layout: 'vertical',
        highlight: false,
        isAlert: true,
        animate: false,
        size: 'md',
    },
};
