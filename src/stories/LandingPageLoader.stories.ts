import type { Meta, StoryObj } from '@storybook/react';
import { SmartSpinnerOverlay } from '../components/common/SmartSpinner';

const meta: Meta<typeof SmartSpinnerOverlay> = {
  title: 'Loading/Landing Page Loader',
  component: SmartSpinnerOverlay,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: 'Landing page loading experience with large spinner and loading text. Perfect for first-time app loading or major page transitions.',
      },
    },
  },
  argTypes: {
    size: {
      control: { type: 'range', min: 64, max: 200, step: 8 },
      description: 'Size of the spinner in pixels',
    },
    speed: {
      control: { type: 'range', min: 0.5, max: 3, step: 0.1 },
      description: 'Animation speed in seconds',
    },
    backdrop: {
      control: 'boolean',
      description: 'Show backdrop overlay',
    },
  },
  args: {
    size: 80,
    speed: 1.5,
    backdrop: true,
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const FirstTimeLoading: Story = {
  args: {
    size: 120,
    speed: 1.8,
    backdrop: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Large spinner for first-time app loading with smooth backdrop and loading text.',
      },
    },
  },
};

export const PageTransition: Story = {
  args: {
    size: 80,
    speed: 1.2,
    backdrop: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Medium-sized spinner for page transitions and navigation loading.',
      },
    },
  },
};

export const FastLoading: Story = {
  args: {
    size: 100,
    speed: 0.8,
    backdrop: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Quick loading animation for fast operations.',
      },
    },
  },
};

export const MinimalLoading: Story = {
  args: {
    size: 64,
    speed: 2.0,
    backdrop: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Minimal loading without backdrop for subtle loading states.',
      },
    },
  },
};

export const ExtraLargeHero: Story = {
  args: {
    size: 160,
    speed: 2.2,
    backdrop: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Extra large hero spinner for splash screens and major loading states.',
      },
    },
  },
};