'use client';

import React, { useState } from 'react';
import { Button } from '@/components/common/ui/Button';
import { Input } from '@/components/common/ui/Input';
import { Label } from '@/components/common/ui/Label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/common/ui/Card';

interface LoginFormProps {
    onSubmit?: (email: string, password: string) => void;
    className?: string;
}

function LoginForm({ onSubmit, className }: LoginFormProps) {
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);

        try {
            await onSubmit?.(email, password);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Card className={`w-full max-w-md ${className || ''}`}>
            <CardHeader>
                <CardTitle>Sign In</CardTitle>
                <CardDescription>Enter your email and password to access your account</CardDescription>
            </CardHeader>
            <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                            id="email"
                            type="email"
                            placeholder="Enter your email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            required
                        />
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="password">Password</Label>
                        <Input
                            id="password"
                            type="password"
                            placeholder="Enter your password"
                            value={password}
                            onChange={(e) => setPassword(e.target.value)}
                            required
                        />
                    </div>

                    <Button type="submit" className="w-full" disabled={isLoading}>
                        {isLoading ? 'Signing in...' : 'Sign In'}
                    </Button>
                </form>
            </CardContent>
        </Card>
    );
}

export default LoginForm;
