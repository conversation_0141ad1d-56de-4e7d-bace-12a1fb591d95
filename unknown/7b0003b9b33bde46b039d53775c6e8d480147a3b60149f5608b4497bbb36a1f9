# Dashboard Layout Documentation

## Overview

The Alert Dashboard System features a comprehensive layout designed for monitoring and managing building security systems. The interface is divided into four main sections that work together to provide a complete operational view.

## 1. Full Layout Structure

The dashboard follows a standard web application layout pattern with fixed navigation elements and a dynamic content area:

```
┌─────────────────────────────────────────────────────────────────────────────┐
│ Logo │                     TOP NAVIGATION BAR                  │             │
│ [🌐] │ [Building A ▼] [Floor 1 ▼] [Last 24 hours ▼] [🔔] [HA]   │              │
│      │─────────────────────────────────────────────────────────────────────┤
│ LEFT │                                                        │ RIGHT SIDEBAR │
│ SIDE │                                                        │               │
│ BAR  │                MAIN CONTENT AREA                       │  SYSTEM       │
│      │                                                        │  OVERVIEW     │
│      │                                                        │               │
│ [🏠] │              2D FLOOR PLAN VIEW                        │  Fire alarm   │
│ [🔔] │                                                        │  system       │
│ [🎯] │          Interactive Building Layout                   │  200 devices  │
│ [📱] │                                                        │  2 alerts     │
│ [🚪] │         Device Status Indicators                       │               │
│ [📊] │                                                        │  Access       │
│ [⚙️] │            Alert Popup Overlays                        │  control      │
│      │                                                    	│  150 doors    │
│      │                                                   	 │  6/144        │
│      │                                                  	  │               │
│      │                                                  	  │  CCTV control │
│      │                                                  	  │  200 cameras  │
│      │                                                  	  │  2 incidents  │
│      │                                                  	  │               │
│      │                                                 	   │  Gate         │
│      │                                                	    │  Barriers     │
│      │                                    	                │  200 barriers │
│      │                                                 	   │  5 attempts   │
│      │                                                  	  │               │
│      │                                                 	   │  Building     │
│      │                                                  	  │  floor        │
│      │                                                   	 │  Floor 1 [!]  │
│      │                                                   	 │  Floor 2 [!]  │
│      │                                                   	 │  Floor 3      │
│      │                                                   	 │  Floor 4      │
│      │                                                   	 │  Floor 5 [2]  │
└─────────────────────────────────────────────────────────────────────────────┘
```

### Layout Dimensions & Proportions

- **Left Sidebar**: ~60px width (collapsed state)
- **Main Content**: ~70% of viewport width
- **Right Sidebar**: ~300px width (expandable drawer open by default)
- **Top Bar**: ~60px height (fixed)

## 2. Left Sidebar Menu

### Structure & Components

The left sidebar serves as the primary navigation hub with a vertical icon-based menu system.

#### Navigation Icons (Top to Bottom):

1. **App logo (🌐)**: Global/Overview Dashboard

   - Purpose: Main dashboard view
   - State: Active/Selected (highlighted)
2. **Home Icon (🏠)**: Building/Facility Management Dashboard

   - Purpose: Building selection and management
   - State: Inactive
3. **Bell Icon (🔔)**: Alert Management
4. **Target Icon (🎯)**: Monitoring & Surveillance

   - Purpose: Active monitoring dashboard
   - State: Inactive
5. **Mobile Icon (📱)**: Mobile/Remote Access

   - Purpose: Mobile device management
   - State: Inactive
6. **Door Icon (🚪)**: Access Control

   - Purpose: Door and access management
   - State: Inactive
7. **Camera Icon (📊)**: Analytics & Reports
8. **Gate Icon ()**: System Configuration

   - Purpose: System settings and configuration
   - State: Inactive

#### Visual Design:

- **Background**: Dark theme (#1a1a1a)
- **Icons**: Light gray (#888) when inactive, blue accent when active
- **Hover State**: Subtle highlight effect
- **Active State**: Blue accent color with background highlight

## 3. Top Bar Filters and User Authentication

### Left Section - Filter Controls

The top bar contains three primary filter dropdown menus:

#### Building Selector

- **Label**: "Building A"
- **Type**: Dropdown menu
- **Purpose**: Switch between different building facilities
- **Icon**: Dropdown arrow (▼)

#### Floor Selector

- **Label**: "Floor 1"
- **Type**: Dropdown menu
- **Purpose**: Navigate between building floors
- **Icon**: Dropdown arrow (▼)

#### Time Range Filter

- **Label**: "Last 24 hours"
- **Type**: Dropdown menu
- **Purpose**: Filter alerts and data by time period
- **Icon**: Dropdown arrow (▼)

### Right Section - User Area

The right side of the top bar contains user-related controls:

#### Notification Bell

- **Icon**: Bell (🔔)
- **Purpose**: Show pending notifications and alerts
- **State**: May show notification count badge

#### User Profile

- **Display**: User initials "HA" in circular avatar
- **Purpose**: User menu and profile access
- **Background**: Dark circular background

### Visual Design:

- **Background**: Dark theme matching sidebar
- **Text Color**: White/light gray
- **Dropdown Styling**: Dark theme with white text
- **Spacing**: Consistent padding and margins

## 4. Right Sidebar - System Overview Drawer

### Container Structure

The right sidebar functions as an expandable drawer containing system status information.

#### System Overview Header

- **Title**: "System Overview"
- **Typography**: Bold, white text
- **Position**: Top of drawer

#### System Status Cards

Each system is represented by a status card with consistent structure:

##### Fire Alarm System

- **Icon**: Fire/flame icon (🔥)
- **Title**: "Fire alarm system"
- **Metrics**:
  - Total devices: 200
  - Active Alarms: 2 (red indicator)
- **Color Scheme**: Red accent for alerts

##### Access Control System

- **Icon**: Door/lock icon (🚪)
- **Title**: "Access control"
- **Metrics**:
  - Total doors: 150
  - Open/Closed status: 6/144
- **Color Scheme**: Blue/teal accent

##### CCTV Control System

- **Icon**: Camera icon (📹)
- **Title**: "CCTV control"
- **Metrics**:
  - Total cameras: 200
  - Active Incidents: 2 (red indicator)
- **Color Scheme**: Purple accent

##### Gate Barriers System

- **Icon**: Barrier/gate icon (🚧)
- **Title**: "Gate Barriers"
- **Metrics**:
  - Total barriers: 200
  - Unauthorized attempts: 5 (red indicator)
- **Color Scheme**: Blue accent

#### Building Floor Navigator

- **Title**: "Building floor"
- **Dropdown**: "Building A" selector
- **Floor List**:
  - Floor 1: Alert indicator (red circle with !)
  - Floor 2: Alert indicator (red circle with !)
  - Floor 3: Normal status
  - Floor 4: Normal status
  - Floor 5: Alert indicator (red circle with 2)

### Visual Design:

- **Background**: Dark theme (#2a2a2a)
- **Card Backgrounds**: Slightly lighter dark (#333)
- **Text**: White primary, gray secondary
- **Alert Indicators**: Red circular badges
- **Spacing**: Consistent card spacing and padding

## 5. Main Content Area - 2D Floor Plan

### Interactive Floor Plan

The central area displays an interactive 2D architectural floor plan:

#### Visual Elements:

- **Floor Plan**: Detailed architectural layout
- **Device Icons**: Colored circular indicators for different systems
- **Room Labels**: Text labels for different areas
- **Interactive Zones**: Clickable areas for device details

#### Device Status Indicators:

- **Green Circles**: Normal operational status
- **Red Circles**: Alert/danger status
- **Blue Circles**: Informational status
- **Orange Circles**: Warning status

#### Interactive Features:

- **Click Events**: Device icons trigger popup modals
- **Zoom Controls**: Pan and zoom functionality
- **Real-time Updates**: Live status indicator updates

## Text-Based Layout Diagram

```
╔═══════════════════════════════════════════════════════════════════════════════╗
║  [Building A ▼] [Floor 1 ▼] [Last 24 hours ▼]              [🔔] [HA]        ║
╠═══╦═══════════════════════════════════════════════════════════════════╦═══════╣
║ 🌐║                                                                   ║ SYS   ║
║ 🏠║                                                                   ║ OVER  ║
║ 🔔║                    MAIN FLOOR PLAN                               ║ VIEW  ║
║ 🎯║                                                                   ║       ║
║ 📱║  ┌─────────────────────────────────────────────────────────────┐ ║ 🔥200 ║
║ 🚪║  │                                                             │ ║  2⚠️  ║
║ 📊║  │    🟢     🟢        🔴                                      │ ║       ║
║ ⚙️║  │      🟢       🟢                                            │ ║ 🚪150 ║
║   ║  │                                                             │ ║ 6/144 ║
║   ║  │         🟢    🔴                                            │ ║       ║
║   ║  │                        🟠                                   │ ║ 📹200 ║
║   ║  │                                                             │ ║  2⚠️  ║
║   ║  │    🟢              🟢                                       │ ║       ║
║   ║  │                                                             │ ║ 🚧200 ║
║   ║  │                                                             │ ║  5⚠️  ║
║   ║  └─────────────────────────────────────────────────────────────┘ ║       ║
║   ║                                                                   ║ FLOOR ║
║   ║                                                                   ║ Floor1║
║   ║                                                                   ║ Floor2║
║   ║                                                                   ║ Floor3║
║   ║                                                                   ║ Floor4║
║   ║                                                                   ║ Floor5║
╚═══╩═══════════════════════════════════════════════════════════════════╩═══════╝
```

## Suggested Technical Components for Modularity

### 1. Layout Components

#### `DashboardLayout`

- **Purpose**: Main layout wrapper component
- **Props**: `children`, `sidebarCollapsed`, `rightDrawerOpen`
- **Responsibilities**: Grid layout, responsive behavior

#### `TopNavigationBar`

- **Purpose**: Top navigation and filters
- **Props**: `filters`, `user`, `notifications`
- **Subcomponents**: `FilterDropdown`, `UserMenu`, `NotificationBell`

#### `LeftSidebar`

- **Purpose**: Primary navigation menu
- **Props**: `menuItems`, `activeItem`, `collapsed`
- **Subcomponents**: `NavigationItem`, `CollapseToggle`

#### `RightDrawer`

- **Purpose**: System overview sidebar
- **Props**: `isOpen`, `systems`, `floors`
- **Subcomponents**: `SystemCard`, `FloorNavigator`

### 2. Content Components

#### `FloorPlanViewer`

- **Purpose**: Interactive 2D floor plan display
- **Props**: `floorData`, `devices`, `onDeviceClick`
- **Features**: Zoom, pan, device overlays

#### `DeviceIndicator`

- **Purpose**: Individual device status markers
- **Props**: `device`, `status`, `position`, `onClick`
- **States**: Normal, alert, warning, info

### 3. System Overview Components

#### `SystemCard`

- **Purpose**: Individual system status display
- **Props**: `system`, `metrics`, `alerts`
- **Variants**: Fire, Access, CCTV, Gates

#### `FloorNavigator`

- **Purpose**: Building floor selection and status
- **Props**: `floors`, `activeFloor`, `onFloorChange`
- **Features**: Alert indicators, floor switching

### 4. Filter Components

#### `FilterDropdown`

- **Purpose**: Reusable dropdown filter
- **Props**: `options`, `value`, `onChange`, `label`
- **Features**: Search, multi-select options

#### `TimeRangeFilter`

- **Purpose**: Specialized time period selector
- **Props**: `range`, `presets`, `onChange`
- **Features**: Custom ranges, quick presets

### 5. Utility Components

#### `AlertBadge`

- **Purpose**: Notification count indicators
- **Props**: `count`, `variant`, `size`
- **Variants**: Error, warning, info, success

#### `StatusIndicator`

- **Purpose**: System status visualization
- **Props**: `status`, `size`, `showLabel`
- **States**: Online, offline, alert, maintenance

### 6. Modal Components

#### `DevicePopup`

- **Purpose**: Device detail modal (from previous documentation)
- **Props**: `device`, `isOpen`, `onClose`
- **Features**: System-specific content, actions

### Component Architecture Benefits

1. **Reusability**: Components can be used across different views
2. **Maintainability**: Isolated components are easier to update
3. **Testing**: Individual components can be unit tested
4. **Scalability**: New features can be added as new components
5. **Consistency**: Shared components ensure UI consistency
6. **Performance**: Components can be optimized individually

### Responsive Design Considerations

- **Mobile**: Collapsible sidebars, stacked layout
- **Tablet**: Adaptive sidebar width, touch-friendly controls
- **Desktop**: Full layout with all panels visible
- **Large Screens**: Expanded content areas, additional information density

---

*This document provides a comprehensive overview of the dashboard layout structure and suggests a modular component architecture for optimal development and maintenance.*
