/**
 * Application Store
 * Global state management using Zustand
 */

import { create } from 'zustand';
import { useMemo } from 'react';

/**
 * User interface
 */
export interface User {
    id: string;
    name: string;
    email: string;
    role: 'admin' | 'user' | 'guest';
}

/**
 * Notification interface
 */
export interface Notification {
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message: string;
    timestamp: Date;
    read: boolean;
}

/**
 * Application state and actions interface
 */
export interface AppStore {
    // User state
    user: User | null;
    isAuthenticated: boolean;

    // UI state
    isLoading: boolean;
    theme: 'light' | 'dark' | 'system';
    sidebarOpen: boolean;

    // Notification state
    notifications: Notification[];

    // User actions
    setUser: (user: User | null) => void;
    login: (user: User) => void;
    logout: () => void;

    // UI actions
    setLoading: (loading: boolean) => void;
    setTheme: (theme: 'light' | 'dark' | 'system') => void;
    toggleSidebar: () => void;
    setSidebarOpen: (open: boolean) => void;

    // Notification actions
    addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void;
    removeNotification: (id: string) => void;
    markNotificationAsRead: (id: string) => void;
    clearAllNotifications: () => void;

    // Reset action
    reset: () => void;
}

/**
 * Generate unique ID for notifications
 */
const generateId = (): string => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

/**
 * Create the app store
 */
export const useAppStore = create<AppStore>((set) => ({
    // Initial state
    user: null,
    isAuthenticated: false,
    isLoading: false,
    theme: 'system',
    sidebarOpen: false,
    notifications: [],

    // User actions
    setUser: (user) => set({ user, isAuthenticated: !!user }),

    login: (user) => set({ user, isAuthenticated: true }),

    logout: () => set({ user: null, isAuthenticated: false }),

    // UI actions
    setLoading: (isLoading) => set({ isLoading }),

    setTheme: (theme) => set({ theme }),

    toggleSidebar: () => set((state) => ({ sidebarOpen: !state.sidebarOpen })),

    setSidebarOpen: (sidebarOpen) => set({ sidebarOpen }),

    // Notification actions
    addNotification: (notificationData) => {
        const notification: Notification = {
            ...notificationData,
            id: generateId(),
            timestamp: new Date(),
            read: false,
        };

        set((state) => ({
            notifications: [notification, ...state.notifications],
        }));
    },

    removeNotification: (id) =>
        set((state) => ({
            notifications: state.notifications.filter((n) => n.id !== id),
        })),

    markNotificationAsRead: (id) =>
        set((state) => ({
            notifications: state.notifications.map((n) => (n.id === id ? { ...n, read: true } : n)),
        })),

    clearAllNotifications: () => set({ notifications: [] }),

    // Reset action
    reset: () =>
        set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            theme: 'system',
            sidebarOpen: false,
            notifications: [],
        }),
}));

/**
 * Selector hooks for better performance
 */
export const useUser = () => useAppStore((state) => state.user);
export const useIsAuthenticated = () => useAppStore((state) => state.isAuthenticated);
export const useTheme = () => useAppStore((state) => state.theme);
export const useIsLoading = () => useAppStore((state) => state.isLoading);
export const useSidebarOpen = () => useAppStore((state) => state.sidebarOpen);
export const useNotifications = () => useAppStore((state) => state.notifications);

/**
 * Action hooks
 */
export const useAppActions = () => {
    const setUser = useAppStore((state) => state.setUser);
    const login = useAppStore((state) => state.login);
    const logout = useAppStore((state) => state.logout);
    const setLoading = useAppStore((state) => state.setLoading);
    const setTheme = useAppStore((state) => state.setTheme);
    const toggleSidebar = useAppStore((state) => state.toggleSidebar);
    const setSidebarOpen = useAppStore((state) => state.setSidebarOpen);
    const addNotification = useAppStore((state) => state.addNotification);
    const removeNotification = useAppStore((state) => state.removeNotification);
    const markNotificationAsRead = useAppStore((state) => state.markNotificationAsRead);
    const clearAllNotifications = useAppStore((state) => state.clearAllNotifications);
    const reset = useAppStore((state) => state.reset);

    return useMemo(
        () => ({
            setUser,
            login,
            logout,
            setLoading,
            setTheme,
            toggleSidebar,
            setSidebarOpen,
            addNotification,
            removeNotification,
            markNotificationAsRead,
            clearAllNotifications,
            reset,
        }),
        [
            setUser,
            login,
            logout,
            setLoading,
            setTheme,
            toggleSidebar,
            setSidebarOpen,
            addNotification,
            removeNotification,
            markNotificationAsRead,
            clearAllNotifications,
            reset,
        ],
    );
};
