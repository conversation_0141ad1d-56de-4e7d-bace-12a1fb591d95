import type { <PERSON>og<PERSON>, Log<PERSON>evel, LogEntry } from './logger.interface';

// Logger configuration
export interface LoggerConfig {
    level: LogLevel;
    enableConsole: boolean;
    enableExternal: boolean;
    enablePerformance: boolean;
    enableUserActions: boolean;
    enableSystemEvents: boolean;
    environment: 'development' | 'production' | 'test';
    appName: string;
    appVersion: string;
    externalServices?: {
        sentry?: {
            dsn: string;
            environment: string;
        };
        logRocket?: {
            appId: string;
        };
        customEndpoint?: {
            url: string;
            apiKey: string;
        };
    };
}

// External logging service interface
export interface ExternalLogService {
    name: string;
    log(entry: LogEntry): Promise<void> | void;
    isEnabled(): boolean;
}

// Performance metrics
export interface PerformanceMetrics {
    startTime: number;
    endTime?: number;
    duration?: number;
    memory?: {
        used: number;
        total: number;
    };
    userAgent?: string;
    url?: string;
}

// User action tracking
export interface UserAction {
    action: string;
    element?: string;
    page?: string;
    userId?: string;
    sessionId?: string;
    timestamp: number;
    metadata?: Record<string, unknown>;
}

// System event tracking
export interface SystemEvent {
    event: string;
    category: 'error' | 'warning' | 'info' | 'performance' | 'security';
    severity: 'low' | 'medium' | 'high' | 'critical';
    source: string;
    timestamp: number;
    metadata?: Record<string, unknown>;
}

export class Logger implements ILogger {
    private static instance: Logger;
    private config: LoggerConfig;
    private externalServices: ExternalLogService[] = [];
    private performanceMarks: Map<string, number> = new Map();
    private sessionId: string;

    private constructor(config: LoggerConfig) {
        this.config = config;
        this.sessionId = this.generateSessionId();
        this.initializeExternalServices();

        // Log initialization
        this.info('Logger initialized', {
            config: this.config,
            sessionId: this.sessionId,
        });
    }

    public static getInstance(config?: LoggerConfig): Logger {
        if (!Logger.instance) {
            if (!config) {
                throw new Error('Logger config is required for first initialization');
            }
            Logger.instance = new Logger(config);
        } else if (config) {
            Logger.instance.updateConfig(config);
        }
        return Logger.instance;
    }

    public updateConfig(newConfig: Partial<LoggerConfig>): void {
        this.config = { ...this.config, ...newConfig };
        this.info('Logger config updated', { config: this.config });
    }

    // Core logging methods
    public debug(message: string, data?: unknown): void {
        this.log('debug', message, data);
    }

    public info(message: string, data?: unknown): void {
        this.log('info', message, data);
    }

    public warn(message: string, data?: unknown): void {
        this.log('warn', message, data);
    }

    public error(message: string, error?: Error | unknown, data?: unknown): void {
        const errorData = this.formatError(error);
        const combinedData = data ? { ...errorData, ...(data as Record<string, unknown>) } : errorData;
        this.log('error', message, combinedData);
    }

    // Performance logging
    public startPerformanceMark(name: string): void {
        if (!this.config.enablePerformance) return;

        this.performanceMarks.set(name, performance.now());
        this.debug(`Performance mark started: ${name}`);
    }

    public endPerformanceMark(name: string, metadata?: Record<string, unknown>): void {
        if (!this.config.enablePerformance) return;

        const startTime = this.performanceMarks.get(name);
        if (!startTime) {
            this.warn(`Performance mark not found: ${name}`);
            return;
        }

        const endTime = performance.now();
        const duration = endTime - startTime;
        this.performanceMarks.delete(name);

        const metrics: PerformanceMetrics = {
            startTime,
            endTime,
            duration,
            memory: this.getMemoryUsage(),
            userAgent: navigator?.userAgent,
            url: window?.location?.href,
        };

        this.info(`Performance: ${name}`, { metrics, metadata });
    }

    public logPerformance(name: string, metrics: PerformanceMetrics, metadata?: Record<string, unknown>): void {
        if (!this.config.enablePerformance) return;
        this.info(`Performance: ${name}`, { metrics, metadata });
    }

    // Performance timer
    public startTimer(label: string): () => void {
        const startTime = performance.now();
        this.performanceMarks.set(label, startTime);

        return () => {
            const endTime = performance.now();
            const duration = endTime - startTime;
            this.performanceMarks.delete(label);

            this.logPerformance(label, {
                duration,
                startTime,
                endTime,
            });
        };
    }

    // API request/response logging
    public logApiRequest(method: string, url: string, body?: unknown, headers?: Record<string, string>): void {
        this.info(`API Request: ${method} ${url}`, {
            method,
            url,
            body: this.sanitizeData(body),
            headers: this.sanitizeHeaders(headers),
            timestamp: Date.now(),
        });
    }

    public logApiResponse(method: string, url: string, status: number, duration: number, data?: unknown): void {
        const level = status >= 400 ? 'error' : status >= 300 ? 'warn' : 'info';
        this.log(level, `API Response: ${method} ${url} - ${status}`, {
            method,
            url,
            status,
            duration,
            data: this.sanitizeData(data),
            timestamp: Date.now(),
        });
    }

    // User action logging
    public logUserAction(action: UserAction): void {
        if (!this.config.enableUserActions) return;

        this.info(`User Action: ${action.action}`, {
            ...action,
            sessionId: this.sessionId,
        });
    }

    // System event logging
    public logSystemEvent(event: SystemEvent): void {
        if (!this.config.enableSystemEvents) return;

        const level = this.mapSeverityToLogLevel(event.severity);
        this.log(level, `System Event: ${event.event}`, {
            ...event,
            sessionId: this.sessionId,
        });
    }

    // External logging
    public logToExternal(entry: LogEntry): void {
        if (!this.config.enableExternal) return;

        this.externalServices.forEach(async (service) => {
            if (service.isEnabled()) {
                try {
                    await service.log(entry);
                } catch (error) {
                    console.error(`Failed to log to ${service.name}:`, error);
                }
            }
        });
    }

    // Utility methods
    public setUserId(userId: string): void {
        this.info('User ID set', { userId, sessionId: this.sessionId });
    }

    public clearLogs(): void {
        this.info('Logs cleared', { sessionId: this.sessionId });
    }

    public exportLogs(): LogEntry[] {
        // In a real implementation, this would return stored logs
        // For now, return empty array as logs are sent to external services
        return [];
    }

    // Private methods
    private log(level: LogLevel, message: string, data?: unknown): void {
        if (!this.shouldLog(level)) return;

        const entry: LogEntry = {
            level,
            message,
            data,
            timestamp: Date.now(),
            sessionId: this.sessionId,
            environment: this.config.environment,
            appName: this.config.appName,
            appVersion: this.config.appVersion,
        };

        // Console logging
        if (this.config.enableConsole) {
            this.logToConsole(entry);
        }

        // External logging
        if (this.config.enableExternal) {
            this.logToExternal(entry);
        }
    }

    private shouldLog(level: LogLevel): boolean {
        const levels: Record<LogLevel, number> = {
            debug: 0,
            info: 1,
            warn: 2,
            error: 3,
        };
        return levels[level] >= levels[this.config.level];
    }

    private logToConsole(entry: LogEntry): void {
        const timestamp = new Date(entry.timestamp).toISOString();
        const prefix = `[${timestamp}] [${entry.level.toUpperCase()}] [${entry.appName}]`;

        switch (entry.level) {
            case 'debug':
                console.debug(prefix, entry.message, entry.data);
                break;
            case 'info':
                console.info(prefix, entry.message, entry.data);
                break;
            case 'warn':
                console.warn(prefix, entry.message, entry.data);
                break;
            case 'error':
                console.error(prefix, entry.message, entry.data);
                break;
        }
    }

    private formatError(error: Error | unknown): Record<string, unknown> {
        if (error instanceof Error) {
            return {
                name: error.name,
                message: error.message,
                stack: error.stack,
            };
        }
        return { error };
    }

    private sanitizeData(data: unknown): unknown {
        if (!data) return data;

        // Remove sensitive information
        const sensitiveKeys = ['password', 'token', 'apiKey', 'secret', 'authorization'];

        if (typeof data === 'object' && data !== null) {
            const sanitized = { ...(data as Record<string, unknown>) };
            sensitiveKeys.forEach((key) => {
                if (key in sanitized) {
                    sanitized[key] = '[REDACTED]';
                }
            });
            return sanitized;
        }

        return data;
    }

    private sanitizeHeaders(headers?: Record<string, string>): Record<string, string> | undefined {
        if (!headers) return headers;

        const sanitized = { ...headers };
        const sensitiveHeaders = ['authorization', 'x-api-key', 'cookie'];

        sensitiveHeaders.forEach((header) => {
            const key = Object.keys(sanitized).find((k) => k.toLowerCase() === header);
            if (key) {
                sanitized[key] = '[REDACTED]';
            }
        });

        return sanitized;
    }

    private getMemoryUsage(): { used: number; total: number } | undefined {
        if (typeof performance !== 'undefined' && 'memory' in performance) {
            const memory = (performance as unknown as { memory: { usedJSHeapSize: number; totalJSHeapSize: number } })
                .memory;
            return {
                used: memory.usedJSHeapSize,
                total: memory.totalJSHeapSize,
            };
        }
        return undefined;
    }

    private mapSeverityToLogLevel(severity: SystemEvent['severity']): LogLevel {
        switch (severity) {
            case 'low':
                return 'info';
            case 'medium':
                return 'warn';
            case 'high':
            case 'critical':
                return 'error';
            default:
                return 'info';
        }
    }

    private generateSessionId(): string {
        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    }

    private initializeExternalServices(): void {
        // Initialize external logging services based on config
        // This would be implemented based on specific service requirements
        if (this.config.externalServices?.sentry) {
            // Initialize Sentry
        }
        if (this.config.externalServices?.logRocket) {
            // Initialize LogRocket
        }
        if (this.config.externalServices?.customEndpoint) {
            // Initialize custom endpoint service
        }
    }
}
