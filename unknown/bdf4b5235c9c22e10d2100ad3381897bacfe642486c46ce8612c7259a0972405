import React from 'react';
import { But<PERSON> } from '@/components/common/ui/Button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/common/ui/Card';

function HomePage() {
    return (
        <div className="space-y-8">
            {/* Hero Section */}
            <section className="text-center py-12">
                <h1 className="text-4xl font-bold tracking-tight mb-4">Welcome to Your App</h1>
                <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
                    Build amazing applications with our modern, scalable architecture.
                </p>
                <div className="flex gap-4 justify-center">
                    <Button size="lg">Get Started</Button>
                    <Button variant="outline" size="lg">
                        Learn More
                    </Button>
                </div>
            </section>

            {/* Features Section */}
            <section className="py-12">
                <h2 className="text-3xl font-bold text-center mb-8">Key Features</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Modern Stack</CardTitle>
                            <CardDescription>Built with Next.js, TypeScript, and Tailwind CSS</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <p className="text-sm text-muted-foreground">
                                Leverage the latest technologies for optimal performance and developer experience.
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Type Safe</CardTitle>
                            <CardDescription>Full TypeScript support with strict type checking</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <p className="text-sm text-muted-foreground">
                                Catch errors at compile time and improve code quality with comprehensive typing.
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Scalable</CardTitle>
                            <CardDescription>Modular architecture for growing applications</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <p className="text-sm text-muted-foreground">
                                Organized structure that scales with your team and project requirements.
                            </p>
                        </CardContent>
                    </Card>
                </div>
            </section>
        </div>
    );
}

export default HomePage;
