import React from 'react';

interface FooterProps {
    className?: string;
}

function Footer({ className }: FooterProps) {
    return (
        <footer className={`bg-background border-t mt-auto ${className || ''}`}>
            <div className="container mx-auto px-4 py-6">
                <div className="flex flex-col md:flex-row justify-between items-center">
                    <div className="text-sm text-muted-foreground">
                        © {new Date().getFullYear()} Your App Name. All rights reserved.
                    </div>

                    <div className="flex items-center space-x-4 mt-4 md:mt-0">
                        <span className="text-sm text-muted-foreground">Built with Next.js</span>
                    </div>
                </div>
            </div>
        </footer>
    );
}

export default Footer;
