/**
 * Base API Client Configuration
 * Centralized HTTP client with interceptors and error handling
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { appSettings } from '@/shared/config/app-settings.config';

/**
 * API Response wrapper interface
 */
export interface ApiResponse<T = unknown> {
    data: T;
    message?: string;
    success: boolean;
    errors?: string[];
}

/**
 * API Error interface
 */
export interface ApiError {
    message: string;
    status?: number;
    code?: string;
    details?: unknown;
}

/**
 * Create and configure axios instance
 */
const createApiClient = (): AxiosInstance => {
    const client = axios.create({
        baseURL: appSettings.apiBaseUrl,
        timeout: 10000,
        headers: {
            'Content-Type': 'application/json',
        },
    });

    // Request interceptor
    client.interceptors.request.use(
        (config) => {
            // Add auth token if available
            const token = localStorage.getItem('authToken');
            if (token) {
                config.headers.Authorization = `Bearer ${token}`;
            }

            // Log request in development
            if (appSettings.enableLogging) {
                console.log('API Request:', {
                    method: config.method?.toUpperCase(),
                    url: config.url,
                    data: config.data,
                });
            }

            return config;
        },
        (error) => {
            if (appSettings.enableLogging) {
                console.error('API Request Error:', error);
            }
            return Promise.reject(error);
        },
    );

    // Response interceptor
    client.interceptors.response.use(
        (response: AxiosResponse) => {
            if (appSettings.enableLogging) {
                console.log('API Response:', {
                    status: response.status,
                    data: response.data,
                });
            }
            return response;
        },
        (error) => {
            const apiError: ApiError = {
                message: error.response?.data?.message || error.message || 'An error occurred',
                status: error.response?.status,
                code: error.response?.data?.code,
                details: error.response?.data,
            };

            if (appSettings.enableLogging) {
                console.error('API Response Error:', apiError);
            }

            // Handle specific error cases
            if (error.response?.status === 401) {
                // Handle unauthorized - redirect to login
                localStorage.removeItem('authToken');
                window.location.href = '/login';
            }

            return Promise.reject(apiError);
        },
    );

    return client;
};

/**
 * Main API client instance
 */
export const apiClient = createApiClient();

/**
 * Generic API request wrapper
 */
export const apiRequest = async <T = unknown>(config: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    try {
        const response = await apiClient.request<ApiResponse<T>>(config);
        return response.data;
    } catch (error) {
        throw error as ApiError;
    }
};

/**
 * HTTP method helpers
 */
export const api = {
    get: <T = unknown>(url: string, config?: AxiosRequestConfig) => apiRequest<T>({ method: 'GET', url, ...config }),

    post: <T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig) =>
        apiRequest<T>({ method: 'POST', url, data, ...config }),

    put: <T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig) =>
        apiRequest<T>({ method: 'PUT', url, data, ...config }),

    patch: <T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig) =>
        apiRequest<T>({ method: 'PATCH', url, data, ...config }),

    delete: <T = unknown>(url: string, config?: AxiosRequestConfig) =>
        apiRequest<T>({ method: 'DELETE', url, ...config }),
};
