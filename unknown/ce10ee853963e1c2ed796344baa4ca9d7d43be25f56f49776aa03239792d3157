import React from 'react';
import Link from 'next/link';

interface HeaderProps {
    className?: string;
}

function Header({ className }: HeaderProps) {
    return (
        <header className={`bg-background border-b ${className || ''}`}>
            <div className="container mx-auto px-4 py-4">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <h1 className="text-xl font-semibold">App Name</h1>
                    </div>

                    <nav className="hidden md:flex items-center space-x-6">
                        <Link href="/" className="text-sm font-medium hover:text-primary">
                            Home
                        </Link>
                        <Link href="/dashboard" className="text-sm font-medium hover:text-primary">
                            Dashboard
                        </Link>
                    </nav>

                    <div className="flex items-center space-x-4">{/* Add user menu, theme toggle, etc. */}</div>
                </div>
            </div>
        </header>
    );
}

export default Header;
