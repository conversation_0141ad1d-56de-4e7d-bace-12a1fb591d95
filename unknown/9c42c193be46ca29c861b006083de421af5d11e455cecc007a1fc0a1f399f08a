import React from 'react';

import Footer from './footer/Footer';
import Header from './header/Header';

interface LayoutProps {
    children: React.ReactNode;
    className?: string;
}

function Layout({ children, className }: LayoutProps) {
    return (
        <div className={`min-h-screen flex flex-col ${className || ''}`}>
            <Header />

            <main className="flex-1 container mx-auto px-4 py-6">{children}</main>

            <Footer />
        </div>
    );
}

export default Layout;
